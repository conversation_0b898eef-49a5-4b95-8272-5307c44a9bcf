<svg width="158" height="50" viewBox="0 0 158 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_503_9325)">
<ellipse cx="79.1673" cy="22.5" rx="76.1111" ry="22.5" fill="url(#paint0_radial_503_9325)"/>
</g>
<defs>
<filter id="filter0_d_503_9325" x="0.83393" y="0" width="156.667" height="49.4444" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.22222"/>
<feGaussianBlur stdDeviation="1.11111"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_503_9325"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_503_9325" result="shape"/>
</filter>
<radialGradient id="paint0_radial_503_9325" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(79.1673 22.5) rotate(90) scale(22.5 76.1111)">
<stop stop-color="#B08E2C"/>
<stop offset="1" stop-color="#EDC03F"/>
</radialGradient>
</defs>
</svg>

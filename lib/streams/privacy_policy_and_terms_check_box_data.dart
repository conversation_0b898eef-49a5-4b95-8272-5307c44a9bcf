import 'dart:async';

class CheckBoxPrivacyPolicyAndTosStream {
  static final CheckBoxPrivacyPolicyAndTosStream _singleton =
      CheckBoxPrivacyPolicyAndTosStream._internal();

  factory CheckBoxPrivacyPolicyAndTosStream() {
    return _singleton;
  }
  CheckBoxPrivacyPolicyAndTosStream._internal();

  static final StreamController<bool> _controller =
      StreamController<bool>.broadcast();

  StreamSink<bool> get _checkBox {
    return _controller.sink;
  }

  Stream<bool> get checkBox {
    return _controller.stream;
  }

  void updateCheckBox(bool checkBox) {
    _checkBox.add(checkBox);
  }

  void closeStream() {
    _controller.close();
  }
}

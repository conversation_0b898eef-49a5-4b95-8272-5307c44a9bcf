import 'dart:async';

import '../models/nearby_vehicle.dart';

class MapAndAvailableVehicleStream {
  final _selectedVehicleController = StreamController<NearByVehicle?>.broadcast();
  Stream<NearByVehicle?> get selectedVehicle => _selectedVehicleController.stream;
  void updateSelectedVehicle(NearByVehicle? vehicle) {
    _selectedVehicleController.sink.add(vehicle);
  }

  void dispose() {
    _selectedVehicleController.close();
  }
}
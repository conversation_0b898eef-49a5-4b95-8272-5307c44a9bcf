import 'dart:async';

import 'package:nds_app/common/constant.dart';
import 'package:nds_app/models/vehicle_info.dart';

class VehicleDataStream {
  static final VehicleDataStream _singleton = VehicleDataStream._internal();

  factory VehicleDataStream() {
    return _singleton;
  }

  VehicleDataStream._internal();

  static final StreamController<VehicleInfo> _controller =
      StreamController<VehicleInfo>.broadcast();
  StreamSink<VehicleInfo> get _vehicleInfo {
    return _controller.sink;
  }

  Stream<VehicleInfo> get vehicleInfo {
    return _controller.stream;
  }

  void updateVehicleInfo(VehicleInfo vehicleInfo) {
    vehicleInfoConstant = vehicleInfo;
    _vehicleInfo.add(vehicleInfo);
  }

  void closeStream() {
    _controller.close();
  }
}

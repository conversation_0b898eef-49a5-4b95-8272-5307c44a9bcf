import 'dart:async';

import 'package:nds_app/models/leadership_status.dart';
import 'package:nds_app/models/leadership_metadata.dart';

class LeadershipCombinedData {
  final LeadershipMetadata? metadata;
  final LeadershipStatusResponse? status;

  LeadershipCombinedData({
    this.metadata,
    this.status,
  });

  LeadershipCombinedData copyWith({
    LeadershipMetadata? metadata,
    LeadershipStatusResponse? status,
  }) {
    return LeadershipCombinedData(
      metadata: metadata ?? this.metadata,
      status: status ?? this.status,
    );
  }
}

class LeadershipCombinedStream {
  static final LeadershipCombinedStream _instance = LeadershipCombinedStream._internal();
  
  factory LeadershipCombinedStream() {
    return _instance;
  }
  
  LeadershipCombinedStream._internal();

  final StreamController<LeadershipCombinedData> _controller =
      StreamController<LeadershipCombinedData>.broadcast();

  Stream<LeadershipCombinedData> get stream => _controller.stream;

  LeadershipCombinedData _currentData = LeadershipCombinedData();

  void updateMetadata(LeadershipMetadata? metadata) {
    _currentData = _currentData.copyWith(metadata: metadata);
    _controller.sink.add(_currentData);
  }

  void updateStatus(LeadershipStatusResponse? status) {
    _currentData = _currentData.copyWith(status: status);
    _controller.sink.add(_currentData);
  }

  void updateBoth(LeadershipMetadata? metadata, LeadershipStatusResponse? status) {
    _currentData = LeadershipCombinedData(metadata: metadata, status: status);
    _controller.sink.add(_currentData);
  }

  void dispose() {
    _controller.close();
  }
}

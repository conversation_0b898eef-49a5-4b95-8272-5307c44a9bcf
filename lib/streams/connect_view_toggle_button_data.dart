import 'dart:async';

class ConnectViewToggleToggleButtonData {
  static final ConnectViewToggleToggleButtonData _singleton =
      ConnectViewToggleToggleButtonData._internal();

  factory ConnectViewToggleToggleButtonData() {
    return _singleton;
  }

  ConnectViewToggleToggleButtonData._internal();

  static final StreamController<bool> _controller =
      StreamController<bool>.broadcast();
  StreamSink<bool> get _connectViewToggleButtonValue {
    return _controller.sink;
  }

  Stream<bool> get connectViewToggleButtonValue {
    return _controller.stream;
  }

  void updateConnectViewToggleButton(bool connectViewToggleButtonValue) {
    _connectViewToggleButtonValue.add(connectViewToggleButtonValue);
  }
}

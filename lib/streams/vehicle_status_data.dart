import 'dart:async';
import 'package:nds_app/constant/vehicle_status.dart';

class VehicleStatusDataStream {
  static final VehicleStatusDataStream _singleton =
      VehicleStatusDataStream._internal();

  factory VehicleStatusDataStream() {
    return _singleton;
  }
  VehicleStatusDataStream._internal();

  static final StreamController<VehicleStatus> _controller =
      StreamController<VehicleStatus>.broadcast();

  StreamSink<VehicleStatus> get _vehicleStatus {
    return _controller.sink;
  }

  Stream<VehicleStatus> get vehicleStatus {
    return _controller.stream;
  }

  void updateVehicleStatusResponse(VehicleStatus vehicleStatus) {
    _vehicleStatus.add(vehicleStatus);
  }

  void closeStream() {
    _controller.close();
  }
}

import 'dart:async';

import 'package:nds_app/models/leadership_status.dart';

class LeadershipStatusStream {
  static final LeadershipStatusStream _instance = LeadershipStatusStream._internal();
  
  factory LeadershipStatusStream() {
    return _instance;
  }
  
  LeadershipStatusStream._internal();

  final StreamController<LeadershipStatusResponse?> _controller =
      StreamController<LeadershipStatusResponse?>.broadcast();

  Stream<LeadershipStatusResponse?> get stream => _controller.stream;

  void update(LeadershipStatusResponse? data) {
    _controller.sink.add(data);
  }

  void dispose() {
    _controller.close();
  }
}



import 'dart:async';

class WebViewLoaderStateStream {
  static final WebViewLoaderStateStream _singleton =
      WebViewLoaderStateStream._internal();

  factory WebViewLoaderStateStream() {
    return _singleton;
  }
  WebViewLoaderStateStream._internal();

  static final StreamController<bool> _controller =
      StreamController<bool>.broadcast();

  StreamSink<bool> get _loaderState {
    return _controller.sink;
  }

  Stream<bool> get loaderState {
    return _controller.stream;
  }

  void updateLoaderState(bool loaderState) {
    _loaderState.add(loaderState);
  }

  void closeStream() {
    _controller.close();
  }
}

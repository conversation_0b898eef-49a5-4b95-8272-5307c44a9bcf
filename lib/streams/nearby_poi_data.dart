import 'dart:async';

import 'package:nds_app/models/nearby_vehicle.dart';

class NearbyPOIDataStream {
  static final NearbyPOIDataStream _singleton = NearbyPOIDataStream._internal();

  factory NearbyPOIDataStream() {
    return _singleton;
  }
  NearbyPOIDataStream._internal();

  static final StreamController<NearbyPOI> _controller =
      StreamController<NearbyPOI>.broadcast();

  StreamSink<NearbyPOI> get _nearbyPOI {
    return _controller.sink;
  }

  Stream<NearbyPOI> get nearbyPOI {
    return _controller.stream;
  }

  void updateNearbyVehiclesResponse(NearbyPOI nearbyPOI) {
    _nearbyPOI.add(nearbyPOI);
  }

  void closeStream() {
    _controller.close();
  }
}

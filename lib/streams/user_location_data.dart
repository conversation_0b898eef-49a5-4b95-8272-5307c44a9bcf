import 'dart:async';

import 'package:location/location.dart';

class UserLocationDataStream {
  static final UserLocationDataStream _singleton =
      UserLocationDataStream._internal();

  factory UserLocationDataStream() {
    return _singleton;
  }
  UserLocationDataStream._internal();

  static final StreamController<LocationData> _controller =
      StreamController<LocationData>.broadcast();

  StreamSink<LocationData> get _userLocationData {
    return _controller.sink;
  }

  Stream<LocationData> get userLocationData {
    return _controller.stream;
  }

  void updateUserlocationDataResponse(LocationData userLocationData) {
    _userLocationData.add(userLocationData);
  }

  void closeStream() {
    _controller.close();
  }
}

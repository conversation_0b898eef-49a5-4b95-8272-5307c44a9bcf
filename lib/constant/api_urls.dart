import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/constant.dart';

const String _preProdUrl =
    "https://${(companyName == "b2c") ? "nds" : (companyName == "prodred") ? "Nichesolv" : companyName}.ev-be-preprod.Nichesolv.com/";
const String _devUrl =
    "https://${(companyName == "b2c") ? "nds" : (companyName == "prodred") ? "Nichesolv" : companyName}.ev-be-dev.Nichesolv.com/";
const String _prodUrl =
    "https://${(companyName == "b2c") ? "nds" : (companyName == "prodred") ? "Nichesolv" : companyName}.ev-be.Nichesolv.com/";

const String _eksPreProdUrl =
    "https://${(companyName == "b2c") ? "nds" : (companyName == "prodred") ? "Nichesolv" : companyName}.eks-be-preprod.Nichesolv.com/";

const String _eksDevUrl =
    "https://${(companyName == "b2c") ? "nds" : (companyName == "prodred") ? "Nichesolv" : companyName}.eks-be-dev.Nichesolv.com/";

const String _eksProdUrl =
    "https://${(companyName == "b2c") ? "nds" : (companyName == "prodred") ? "Nichesolv" : companyName}.eks-be-prod.Nichesolv.com/";

const String _base = env == "eksprod"
    ? _eksProdUrl
    : (env == "eksdev"
    ? _eksDevUrl
    : (env == "ekspreprod"
    ? _eksPreProdUrl
    : (env == "preprod"
    ? _preProdUrl
    : (env == "prod" ? _prodUrl : _devUrl))));

enum ApiUrls {
  userInfo("users", false),
  vehicleInfo("vehicles/info", false),
  connectVehicle("vehicles/connect", false),
  disconnectVehicle("vehicles/disconnect", false),
  availableVehicles("users/nearby-poi", false),
  tripHistory("users/trips", false),
  profileDetails("users/profile", false),
  updateProfileDetails("users/profile", false),
  userVehicleConnections("user-vehicles/connections", false),
  uploadProfileImage("user/profile/upload", true),
  saveDeviceToken("user/device/token", true),
  alert("vehicle-alerts/temperature", false),
  saveUserKeyActivity("user/activity", true),
  getUserKeyActivity("user/setting-activity", true),
  setting("setting", true),
  otpPath("auth/otp", true),
  verifyOtpPath("auth/otp/verify", true),
  aboutVehicle("vehicles/about", false),
  authLogout("auth/logout", true),
  statistics("user-vehicles/insights", false),
  statisticsDetails("user-vehicles/insights/details", false),
  markUserInActive("users/deactivate", false),
  userRiders("riders", false),
  addUserRider("riders", false),
  userVehiclesB2C("riders/vehicles", false),
  acceptVehicleInvite("riders/invitations/accept", false),
  getPromotionalImage("promotions/images", false),
  vehicleHealth("vehicles/health", false),
  userVehicleTestDetails("user-vehicles/test-rides/metadata", false),
  // Leadership-only consolidated status API
  leadershipVehiclesStatus("user-vehicles/status", false),
  leadershipMetadata("user-vehicles/test-rides/metadata", false),
  submitRideActivity("vehicle-test-rides/details", false),
  validateRideActivity("trips/summary", false),
  tripsVehicle("user-vehicles/trips", false),
  telemetryFieldValues("telemetry-data/field-values", false),
  getEmergencyReasons("emergency", false),
  createEmergencyEvent("emergency-event", false),
  vehicleModels("vehicle-models/list", false),
  vehiclesDetails("vehicles/details", false),
  fleets("fleets/list", false),
  performanceSummary("user-vehicles/performance-summary", false);


  const ApiUrls(this._url, this._isUserMgmt);

  final String _url;
  final bool _isUserMgmt;

  String getUrl() {
    String suffix = _isUserMgmt ? "user-mgmt/" : "ev/";
    return _base + suffix + _url;
  }
}
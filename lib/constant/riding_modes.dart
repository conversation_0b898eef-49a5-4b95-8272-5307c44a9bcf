enum RidingModes {
  reverse("reverse", "assets/ride_mode_icons/reverse.png", "#123ecc", 6, 3),
  power("power", "assets/ride_mode_icons/sports.png", "#F16521", 60, 2),
  city("city", "assets/ride_mode_icons/normal.png", "#D3A00E", 80, 1),
  eco("eco", "assets/ride_mode_icons/eco.png", "#12B76A", 100, 0);

  final String url;
  final String name;
  final String color;
  final int range;
  final int order;

  const RidingModes(this.name, this.url, this.color, this.range, this.order);
}

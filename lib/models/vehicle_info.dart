import 'package:nds_app/models/enums/color_type.dart';

import 'image_url.dart';

class VehicleInfo {
  int? charge;
  bool? batteryCharging;
  bool? batteryConnected;
  int? remainingTimeForCharge;
  String? regNo;
  String? modelNo;
  String? color;
  ColorType? colorType;
  List<ImageUrl>? images;
  String? currentDriveMode;
  int? totalDistance;
  int? batteryTemperature;
  String? temperatureUnit;
  int? batteryVoltage;
  List<VehicleModeInfo>? vehicleModeInfoList;
  int? aiVinRecordedTime;
  double? current;
  List<double>? motorDcCurrents;
  double? speed;
  bool? diMotion;

  VehicleInfo(
      {charge,
      batteryCharging,
      batteryConnected,
      remainingTimeForCharge,
      regNo,
      modelNo,
      color,
      imageUrls,
      currentDriveMode,
      totalDistance,
      batteryTemperature,
      temperatureUnit,
      batteryVoltage,
      vehicleModeInfoList,
      aiVinRecordedTime,
      current,
      motorDcCurrents,
      speed,
      colorType,
      diMotion});

  VehicleInfo.fromJson(Map<String, dynamic> json) {
    charge = json['charge'] ?? 0;
    batteryCharging = json['batteryCharging'];
    batteryConnected = json['batteryConnected'];
    remainingTimeForCharge = json['remainingTimeForCharge'] ?? 0;
    regNo = json['regNo'] ?? "";
    modelNo = json['modelNo'] ?? "";
    color = json['color'];
    try {
      colorType = ColorType.values.firstWhere((element) =>
          element.name.toUpperCase() == (json['colorType'] ?? 'NORMAL'));
    } catch (e) {
      // Default to normal color type if not found
      colorType = ColorType.normal;
    }
    currentDriveMode = json['currentDriveMode'] ?? "ECO";
    totalDistance = json['totalDistance'] ?? 0;
    batteryTemperature = json['batteryTemperature'] ?? 0;
    temperatureUnit = json['temperatureUnit'] ?? "C";
    batteryVoltage = json['batteryVoltage'] ?? 0;

    aiVinRecordedTime = json['aiVinRecordedTime'];
    current = json['current'];
    speed = json['speed'];

    images = <ImageUrl>[];

    if (json['vehicleModeInfoList'] != null) {
      vehicleModeInfoList = <VehicleModeInfo>[];
      json['vehicleModeInfoList'].forEach((v) {
        vehicleModeInfoList!.add(VehicleModeInfo.fromJson(v));
      });
    }
    motorDcCurrents = <double>[];

    if (json['images'] != null) {
      json['images'].forEach((v) {
        images!.add(ImageUrl.fromJson(v));
      });
    }
    if (json['motorDcCurrents'] != null) {
      json['motorDcCurrents'].forEach((c) {
        motorDcCurrents!.add(c);
      });
    }
    diMotion = json['diMotion'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['charge'] = charge;
    data['batteryCharging'] = batteryCharging;
    data['batteryConnected'] = batteryConnected;
    data['remainingTimeForCharge'] = remainingTimeForCharge;
    data['regNo'] = regNo;
    data['modelNo'] = modelNo;
    data['color'] = color;
    data['currentDriveMode'] = currentDriveMode;
    data['totalDistance'] = totalDistance;
    data['batteryTemperature'] = batteryTemperature;
    data['temperatureUnit'] = temperatureUnit;
    data['batteryVoltage'] = batteryVoltage;
    data['aiVinRecordedTime'] = aiVinRecordedTime;
    data['speed'] = speed;
    if (vehicleModeInfoList != null) {
      data['vehicleModeInfoList'] =
          vehicleModeInfoList!.map((v) => v.toJson()).toList();
    }
    if (images != null) {
      data['images'] = images!.map((v) => v.toJson()).toList();
    }
    data['diMotion'] = diMotion;
    return data;
  }
}

class VehicleModeInfo {
  String? mode;
  String? colorCode;
  double? range;
  String? rangeUnit;
  double? maxRange;
  double? rangeCorrection;

  VehicleModeInfo({mode, colorCode, range, rangeUnit, rangeCorrection});

  VehicleModeInfo.fromJson(Map<String, dynamic> json) {
    mode = json['mode'];
    colorCode = json['colorCode'];
    range = double.parse((json['range'] ?? 0).toString());
    rangeUnit = json['rangeUnit'] ?? 'Km';
    maxRange = double.parse((json['maxRange'] ?? 0).toString());
    rangeCorrection = double.parse((json['correction'] ?? 0).toString());
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mode'] = mode;
    data['colorCode'] = colorCode;
    data['range'] = range;
    data['rangeUnit'] = rangeUnit;
    data['maxRange'] = range;
    data['correction'] = rangeCorrection;

    return data;
  }
}

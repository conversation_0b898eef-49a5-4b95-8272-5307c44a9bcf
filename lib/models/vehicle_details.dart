class VehicleDetail {
  String vehChassisNo;
  String vehRegNo;
  String vehId;
  String vehImei;
  String imei;
  String modelName;
  String mfrDate;
  double netWeight;
  String color;
  String chassisNumber;
  String operationStatus;
  int vehicleModelId;
  String imageUrl;

  VehicleDetail({
    required this.vehChassisNo,
    required this.vehRegNo,
    required this.vehId,
    required this.vehImei,
    required this.imei,
    required this.modelName,
    required this.mfrDate,
    required this.netWeight,
    required this.color,
    required this.chassisNumber,
    required this.operationStatus,
    required this.vehicleModelId,
    required this.imageUrl,
  });

  factory VehicleDetail.fromJson(Map<String, dynamic> json) => VehicleDetail(
        vehChassisNo: json['vehChassisNo'] ?? '',
        vehRegNo: json['vehRegNo'] ?? '',
        vehId: json['vehId'] ?? '',
        vehImei: json['vehImei'] ?? '',
        imei: json['imei'] ?? '',
        modelName: json['modelName'] ?? '',
        mfrDate: json['mfrDate']?.toString() ?? '',
        netWeight: (json['netWeight'] is int)
            ? (json['netWeight'] as int).toDouble()
            : (json['netWeight'] as num?)?.toDouble() ?? 0.0,
        color: json['color'] ?? '',
        chassisNumber: json['chassisNumber'] ?? '',
        operationStatus: json['operationStatus'] ?? '',
        vehicleModelId: json['vehicleModelId'] ?? 0,
        imageUrl: json['imageUrl'] ?? '',
      );

  Map<String, dynamic> toJson() => {
        'vehChassisNo': vehChassisNo,
        'vehRegNo': vehRegNo,
        'vehId': vehId,
        'vehImei': vehImei,
        'imei': imei,
        'modelName': modelName,
        'mfrDate': mfrDate,
        'netWeight': netWeight,
        'color': color,
        'chassisNumber': chassisNumber,
        'operationStatus': operationStatus,
        'vehicleModelId': vehicleModelId,
        'imageUrl': imageUrl,
      };
}

class VehicleDetailsResponse {
  List<VehicleDetail> vehicleDetailDto;
  int totalPages;
  int totalElements;

  VehicleDetailsResponse({
    required this.vehicleDetailDto,
    required this.totalPages,
    required this.totalElements,
  });

  factory VehicleDetailsResponse.fromJson(Map<String, dynamic> json) =>
      VehicleDetailsResponse(
        vehicleDetailDto: (json['vehicleDetailDto'] as List<dynamic>?)
                ?.map((e) => VehicleDetail.fromJson(e))
                .toList() ??
            [],
        totalPages: json['totalPages'] ?? 0,
        totalElements: json['totalElements'] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        'vehicleDetailDto': vehicleDetailDto.map((e) => e.toJson()).toList(),
        'totalPages': totalPages,
        'totalElements': totalElements,
      };
}

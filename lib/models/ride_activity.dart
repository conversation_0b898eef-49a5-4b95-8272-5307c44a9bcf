import 'package:intl/intl.dart';

class RideActivity {
  final String id;
  final DateTime startTime;
  final DateTime endTime;
  final double riderWeight;
  final double? pillionWeight;
  final List<RideMetric> metrics;
  final String? observations;

  RideActivity({
    required this.id,
    required this.startTime,
    required this.endTime,
    required this.riderWeight,
    this.pillionWeight,
    required this.metrics,
    this.observations,
  });

  Duration get duration => endTime.difference(startTime);

  String get formattedStartTime =>
      DateFormat("dd'th' MMMM, yyyy 'At' ha").format(startTime);

  String get formattedEndTime =>
      DateFormat("dd'th' MMMM, yyyy 'At' ha").format(endTime);

  String get formattedDuration {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    return "${hours}hr ${minutes}min";
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'riderWeight': riderWeight,
      'pillionWeight': pillionWeight,
      'metrics': metrics.map((m) => m.toJson()).toList(),
      'observations': observations,
    };
  }

  factory RideActivity.fromJson(Map<String, dynamic> json) {
    return RideActivity(
      id: json['id'],
      startTime: DateTime.parse(json['startTime']),
      endTime: DateTime.parse(json['endTime']),
      riderWeight: json['riderWeight'],
      pillionWeight: json['pillionWeight'],
      metrics:
          (json['metrics'] as List).map((m) => RideMetric.fromJson(m)).toList(),
      observations: json['observations'],
    );
  }
}

class RideMetric {
  final String name;
  final String value;

  RideMetric({
    required this.name,
    required this.value,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'value': value,
    };
  }

  factory RideMetric.fromJson(Map<String, dynamic> json) {
    return RideMetric(
      name: json['name'],
      value: json['value'],
    );
  }
}

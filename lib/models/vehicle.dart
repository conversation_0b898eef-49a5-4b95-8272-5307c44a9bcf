import 'package:nds_app/models/image_url.dart';

class Vehicle {
  String regNo = "";
  String model = "";
  List<ImageUrl>? images;
  int? soc;
  double? distanceCovered;
  int startDate = 0;
  int? endDate;

  Vehicle({
    required this.regNo,
    required this.model,
    required this.images,
    required this.soc,
    required this.distanceCovered,
    required this.startDate,
    this.endDate,
  });

  Vehicle.fromJson(Map<String, dynamic> json) {
    regNo = json['regNo'];
    model = json['model'];
    soc = json['soc'];
    distanceCovered = json['distanceCovered'];
    startDate = json['startDate'];
    endDate = json['endDate'];

    images = <ImageUrl>[];

    if (json['images'] != null) {
      json['images'].forEach((v) {
        images!.add(ImageUrl.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['regNo'] = regNo;
    data['model'] = model;
    data['soc'] = soc;
    data['distanceCovered'] = distanceCovered;
    data['startDate'] = startDate;
    data['endDate'] = endDate;

    if (images != null) {
      data['images'] = images!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class LeadershipMetadata {
  final String? regNo;
  final List<dynamic>? images;
  final int? startTime;
  final int? endTime;
  final String? startAddress;
  final String? endAddress;
  final double? rideTime;
  final double? rideDistance;
  final int? testRecords;
  final bool? isManual;
  final String? tripId;
  final String? testId;
  final int? testRideCount;
  final int totalAlarmCount;
  final int totalAlertCount;
  final int totalTripCount;
  final int totalRunning;
  final int totalVehicles;

  LeadershipMetadata({
    this.regNo,
    this.images,
    this.startTime,
    this.endTime,
    this.startAddress,
    this.endAddress,
    this.rideTime,
    this.rideDistance,
    this.testRecords,
    this.isManual,
    this.tripId,
    this.testId,
    this.testRideCount,
    required this.totalAlarmCount,
    required this.totalAlertCount,
    required this.totalTripCount,
    required this.totalRunning,
    required this.totalVehicles,
  });

  factory LeadershipMetadata.fromJson(Map<String, dynamic> json) {
    return LeadershipMetadata(
      regNo: json['regNo'] as String?,
      images: json['images'] as List<dynamic>?,
      startTime: (json['startTime'] as num?)?.toInt(),
      endTime: (json['endTime'] as num?)?.toInt(),
      startAddress: json['startAddress'] as String?,
      endAddress: json['endAddress'] as String?,
      rideTime: (json['rideTime'] as num?)?.toDouble(),
      rideDistance: (json['rideDistance'] as num?)?.toDouble(),
      testRecords: (json['testRecords'] as num?)?.toInt(),
      isManual: json['isManual'] as bool?,
      tripId: json['tripId'] as String?,
      testId: json['testId'] as String?,
      testRideCount: (json['testRideCount'] as num?)?.toInt(),
      totalAlarmCount: (json['totalAlarmCount'] as num?)?.toInt() ?? 0,
      totalAlertCount: (json['totalAlertCount'] as num?)?.toInt() ?? 0,
      totalTripCount: (json['totalTripCount'] as num?)?.toInt() ?? 0,
      totalRunning: (json['totalRunning'] as num?)?.toInt() ?? 0,
      totalVehicles: (json['totalVehicles'] as num?)?.toInt() ?? 0,
    );
  }
}

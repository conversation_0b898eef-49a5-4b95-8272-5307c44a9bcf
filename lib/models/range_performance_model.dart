class RangePerformanceModel {
  // For single vehicle
  final String? vehChassisNo;
  final String? vehRegNo;
  final String? vehId;
  final String? vehImei;
  final String? modelName;
  final double avgRange;
  final double overallRange;

  // For group of vehicles
  final List<TopVehicle>? topVehicles;

  // Common
  final GraphData graph;

  RangePerformanceModel({
    this.vehChassisNo,
    this.vehRegNo,
    this.vehId,
    this.vehImei,
    this.modelName,
    required this.avgRange,
    required this.overallRange,
    this.topVehicles,
    required this.graph,
  });

  factory RangePerformanceModel.fromJson(Map<String, dynamic> json) {
    return RangePerformanceModel(
      vehChassisNo: json['vehChassisNo'],
      vehRegNo: json['vehRegNo'],
      vehId: json['vehId'],
      vehImei: json['vehImei'],
      modelName: json['modelName'],
      avgRange: (json['avgRange'] ?? 0).toDouble(),
      overallRange: (json['overallRange'] ?? 0).toDouble(),
      topVehicles: (json['topVehicles'] as List<dynamic>?)
          ?.map((e) => TopVehicle.fromJson(e))
          .toList(),
      graph: GraphData.fromJson(json['graph'] ?? {}),
    );
  }
}

class GraphData {
  final List<GraphPoint> day;
  final List<GraphPoint> week;
  final List<GraphPoint> month;

  GraphData({
    required this.day,
    required this.week,
    required this.month,
  });

  factory GraphData.fromJson(Map<String, dynamic> json) {
    return GraphData(
      day: (json['day'] as List<dynamic>? ?? [])
          .map((e) => GraphPoint.fromJson(e))
          .toList(),
      week: (json['week'] as List<dynamic>? ?? [])
          .map((e) => GraphPoint.fromJson(e))
          .toList(),
      month: (json['month'] as List<dynamic>? ?? [])
          .map((e) => GraphPoint.fromJson(e))
          .toList(),
    );
  }
}

class GraphPoint {
  final int x;
  final double y;

  GraphPoint({required this.x, required this.y});

  factory GraphPoint.fromJson(Map<String, dynamic> json) {
    return GraphPoint(
      x: json['x'] ?? 0,
      y: (json['y'] ?? 0).toDouble(),
    );
  }
}

class TopVehicle {
  final String? vehId;
  final String? vehRegNo;
  final String? vehImei;
  final String? modelName;
  final double avgRange;

  TopVehicle({
    this.vehId,
    this.vehRegNo,
    this.vehImei,
    this.modelName,
    required this.avgRange,
  });

  factory TopVehicle.fromJson(Map<String, dynamic> json) {
    return TopVehicle(
      vehId: json['vehId'],
      vehRegNo: json['vehRegNo'],
      vehImei: json['vehImei'],
      modelName: json['modelName'],
      avgRange: (json['avgRange'] ?? 0).toDouble(),
    );
  }
}

class UserInfo {
  String? firstName;
  String? lastName;
  int? distanceCovered;
  int? spent;
  int? equivalent;
  String? connectedVehicleImei;

  UserInfo(
      {firstName,
      lastName,
      distanseCovered,
      spent,
      equivalent,
      connectedVehicleImei,
      userConnectionType});

  UserInfo.fromJson(Map<String, dynamic> json) {
    firstName = json['firstName'].toString();
    lastName = json['lastName'].toString();
    distanceCovered = json['distanceCovered'] ?? 0;
    spent = json['spent'] ?? 0;
    equivalent = json['equivalent'];
    connectedVehicleImei = json['connectedVehicleImei'];
  }
}

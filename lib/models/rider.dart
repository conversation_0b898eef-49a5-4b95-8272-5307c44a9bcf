import 'package:equatable/equatable.dart';
import 'package:nds_app/models/enums/permission_status.dart'; // Assuming you have these enums
import 'package:nds_app/models/enums/relation.dart';
import 'package:nds_app/models/enums/verification_status.dart';

class Rider extends Equatable {
  final String regNo;
  final String imei;
  final String ownerName;
  final String riderPhoneNumber;
  final VerificationStatus verificationStatus;
  final RiderPermissionStatus permissionStatus;
  final Relation relationType;
  final bool isOwner;
  final bool isConnected;
  final String? profileUrl;

  const Rider({
    required this.regNo,
    required this.imei,
    required this.ownerName,
    required this.riderPhoneNumber,
    required this.verificationStatus,
    required this.permissionStatus,
    required this.relationType,
    required this.isOwner,
    required this.isConnected,
    required this.profileUrl,
  });

  // Factory method to create a UserVehicleDto from JSON
  factory Rider.fromJson(Map<String, dynamic> json) {
    String number = "";
    if (json['riderPhoneNumber'] != null) {
      number = json['riderPhoneNumber'].toString().replaceFirst("+91", "");
    }

    return Rider(
        regNo: json['regNo'],
        imei: json['imei'],
        ownerName: json['ownerName'],
        riderPhoneNumber: number,
        verificationStatus: _getVerificationStatus(json['verificationStatus']),
        permissionStatus: _getPermissionStatus(json['permissionStatus']),
        relationType: _getRelationType(json['relationType']),
        isOwner: json['owner'] as bool,
        isConnected: json['connected'] as bool,
        profileUrl: json['profileUrl']);
  }

  @override
  List<Object?> get props => [
        regNo,
        imei,
        ownerName,
        riderPhoneNumber,
        verificationStatus,
        permissionStatus,
        relationType,
        isOwner,
        isConnected,
      ];

  // Add a copyWith method
  Rider copyWith({
    String? regNo,
    String? imei,
    String? ownerName,
    String? riderPhoneNumber,
    VerificationStatus? verificationStatus,
    RiderPermissionStatus? permissionStatus,
    Relation? relationType,
    bool? isOwner,
    bool? isConnected,
    String? profileUrl,
  }) {
    return Rider(
      regNo: regNo ?? this.regNo,
      imei: imei ?? this.imei,
      ownerName: ownerName ?? this.ownerName,
      riderPhoneNumber: riderPhoneNumber ?? this.riderPhoneNumber,
      verificationStatus: verificationStatus ?? this.verificationStatus,
      permissionStatus: permissionStatus ?? this.permissionStatus,
      relationType: relationType ?? this.relationType,
      isOwner: isOwner ?? this.isOwner,
      isConnected: isConnected ?? this.isConnected,
      profileUrl: profileUrl ?? this.profileUrl,
    );
  }

  // Helper methods for safe enum conversion
  static VerificationStatus _getVerificationStatus(dynamic status) {
    try {
      return VerificationStatus.values.firstWhere(
        (e) =>
            e.toString() ==
            'VerificationStatus.${status?.toString().toLowerCase() ?? 'pending'}',
      );
    } catch (e) {
      return VerificationStatus.pending; // Default value
    }
  }

  static RiderPermissionStatus _getPermissionStatus(dynamic status) {
    try {
      return RiderPermissionStatus.values.firstWhere(
        (e) =>
            e.toString() ==
            'RiderPermissionStatus.${status?.toString().toLowerCase() ?? 'pending'}',
      );
    } catch (e) {
      return RiderPermissionStatus.none; // Default value
    }
  }

  static Relation _getRelationType(dynamic type) {
    try {
      return Relation.values.firstWhere(
        (e) =>
            e.toString() ==
            'Relation.${type?.toString().toLowerCase() ?? 'family'}',
      );
    } catch (e) {
      return Relation.family; // Default value
    }
  }
}
import 'package:nds_app/models/image_url.dart';

class Trip {
  String? regNo;
  List<ImageUrl>? images;
  int? startTime;
  int? endTime;
  String? startAddress;
  String? endAddress;
  double? rideTime;
  double? rideDistance;
  int? testRecords;
  bool? isManual;
  int? tripId;
  int? testId;

  Trip({
    this.regNo,
    this.images,
    this.startTime,
    this.endTime,
    this.startAddress,
    this.endAddress,
    this.rideTime,
    this.rideDistance,
    this.testRecords,
    this.isManual,
    this.tripId,
    this.testId,
  });

  Trip.fromJson(Map<String, dynamic> json) {
    regNo = json['regNo']?.toString();
    startTime = json['startTime'];
    endTime = json['endTime'];
    endAddress = json['endAddress']?.toString() ?? '';
    startAddress = json['startAddress']?.toString() ?? '';
    rideTime = json['rideTime']?.toDouble() ?? 0;
    rideDistance = json['rideDistance']?.toDouble() ?? 0;
    testRecords = json['testRecords'];
    isManual = json['isManual'];
    tripId = json['tripId'];
    testId = json['testId'];

    images = <ImageUrl>[];
    if (json['images'] != null) {
      json['images'].forEach((v) {
        images!.add(ImageUrl.fromJson(v));
      });
    }
  }
}

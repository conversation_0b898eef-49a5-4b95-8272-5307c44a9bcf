import 'package:flutter/material.dart';

class Statistics {
  int? distanceTravelled;
  int? rideDuration;
  double? avgSpeed;
  double? topSpeed;
  int? chargingTime;
  int? numberOfSwaps;
  Map<String, double>? avgDriveModesRange;

  Statistics({
    this.distanceTravelled,
    this.rideDuration,
    this.avgSpeed,
    this.topSpeed,
    this.chargingTime,
    this.numberOfSwaps,
    this.avgDriveModesRange,
  });

  static Map<String, double>? _convertAvgDriveModesRange(dynamic json) {
    if (json is Map) {
      try {
        return json.map((key, value) =>
            MapEntry(key.toString(), ((value ?? 0) as num).toDouble()));
      } catch (e) {
        // Handle the error if any conversion fails
        debugPrint('Error converting avgDriveModesRange: $e');
        return null;
      }
    }
    return null;
  }

  // Factory constructor to create an instance from a JSON map
  Statistics.fromJson(Map<String, dynamic> json) {
    distanceTravelled = (json['distanceTravelled'] as num?)?.round();
    rideDuration = json['rideDuration'] as int?;
    avgSpeed = (json['avgSpeed'] as num?)?.toDouble();
    topSpeed = (json['topSpeed'] as num?)?.toDouble();
    chargingTime = json['chargingTime'] as int?;
    numberOfSwaps = json['numberOfSwaps'] as int;
    avgDriveModesRange =
        _convertAvgDriveModesRange(json['avgDriveModesRange'] ?? {});
  }

  // Method to convert an instance to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'distanceTravelled': distanceTravelled,
      'rideDuration': rideDuration,
      'avgSpeed': avgSpeed,
      'topSpeed': topSpeed,
      'chargingTime': chargingTime,
      'numberOfSwaps': numberOfSwaps,
      'avgDriveModesRange': avgDriveModesRange,
    };
  }
}

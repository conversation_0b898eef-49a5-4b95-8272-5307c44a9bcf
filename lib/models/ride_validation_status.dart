enum RideValidationStatus {
  aborted('ABORTED'),
  inProgress('IN_PROGRESS'),
  failed('FAILED');

  final String value;
  const RideValidationStatus(this.value);

  static RideValidationStatus? fromString(String? value) {
    if (value == null) return null;
    return RideValidationStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => throw Exception('Unknown validation status: $value'),
    );
  }
}

import 'package:equatable/equatable.dart';
import 'package:nds_app/models/enums/permission_status.dart';
import 'package:nds_app/models/enums/relation.dart';
import 'package:nds_app/models/enums/verification_status.dart';

class VehicleRiderBody extends Equatable {
  final String regNo;
  final String riderName;
  final String riderPhoneNumber;
  final String? newRiderPhoneNumber;
  final VerificationStatus verificationStatus;
  final RiderPermissionStatus permissionStatus;
  final Relation relationType;
  final bool isEdit;

  const VehicleRiderBody({
    required this.regNo,
    required this.riderName,
    required this.riderPhoneNumber,
    this.newRiderPhoneNumber,
    this.verificationStatus = VerificationStatus.pending,
    this.permissionStatus = RiderPermissionStatus.granted,
    required this.relationType,
    required this.isEdit,
  });

  Map<String, dynamic> toJson() {
    return {
      'regNo': regNo,
      'riderName': riderName,
      'riderPhoneNumber': riderPhoneNumber,
      'newRiderPhoneNumber': newRiderPhoneNumber,
      'verificationStatus': verificationStatus.name.toUpperCase(),
      'permissionStatus': permissionStatus.name.toUpperCase(),
      'relationType': relationType.name.toUpperCase(),
    };
  }

  // Equality and props for Equatable
  @override
  List<Object?> get props => [
        regNo,
        riderName,
        riderPhoneNumber,
        newRiderPhoneNumber,
        verificationStatus,
        permissionStatus,
        relationType,
      ];
}

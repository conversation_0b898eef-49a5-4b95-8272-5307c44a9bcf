import 'package:nds_app/constant/activity_type.dart';

class UserActivitySetting {
  late ActivityType activityType;
  late bool isNewSettingExist;
  late String message;
  late String value;

  UserActivitySetting(
      {required this.activityType,
      required this.isNewSettingExist,
      required this.message,
      required this.value});

  UserActivitySetting.fromJson(Map<String, dynamic> json) {
    message = json['message'];

    activityType = ActivityType.getActivityType(json['activityType']);
    isNewSettingExist = json['newSettingExist'];
    value = json['value'];
  }
}

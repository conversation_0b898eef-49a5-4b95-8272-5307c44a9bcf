import 'package:nds_app/models/image_url.dart';

class AboutVehicle {
  String? regNo;
  String? modelName;
  String? vehicleManufacturerName;
  String? colorName;
  String? vehicleColorHexCode;
  List<ImageUrl>? images;
  double? odometer;
  String? batteryName;
  String? batteryManufacturerName;
  int? batteryCapacity;
  double? rearTyreDiameter;
  double? frontTyreDiameter;
  String? tyreManufacturerName;
  double? netWeight;
  Map<String, double>? rideModesMaxRange;

  AboutVehicle({
    regNo,
    modelName,
    vehicleManufacturerName,
    colorName,
    vehicleColorHexCode,
    images,
    odometer,
    batteryName,
    batteryManufacturerName,
    batteryCapacity,
    rearTyreDiameter,
    frontTyreDiameter,
    tyreManufacturerName,
    netWeight,
    rideModesMaxRange,
  });

  AboutVehicle.fromJson(Map<String, dynamic> json) {
    regNo = json["regNo"];
    modelName = json["modelName"];
    vehicleManufacturerName = json["vehicleManufacturerName"];
    colorName = json["colorName"];
    vehicleColorHexCode = json["vehicleColorHexCode"];
    images = <ImageUrl>[];

    if (json['images'] != null) {
      json['images'].forEach((v) {
        images!.add(ImageUrl.fromJson(v));
      });
    }
    odometer = json["odometer"];
    batteryName = json["batteryName"];
    batteryManufacturerName = json["batteryManufacturerName"];
    if (json["fullCapacity"] != null) {
      batteryCapacity = int.parse(json["fullCapacity"].toString());
    }
    if (json["rearTyreDiameter"] != null) {
      rearTyreDiameter = double.parse(json["rearTyreDiameter"].toString());
    }
    if (json["frontTyreDiameter"] != null) {
      frontTyreDiameter = double.parse(json["frontTyreDiameter"].toString());
    }
    tyreManufacturerName = json["tyreManufacturerName"];

    if (json["netWeight"] != null) {
      netWeight = double.parse(json["netWeight"].toString());
    }

    rideModesMaxRange = <String, double>{};

    if (json["driveModesRange"] != null) {
      rideModesMaxRange = Map<String, double>.from(json['driveModesRange']);
    }
  }
}

class UserVehicleDto {
  final String regNo;
  final String imei;
  final String ownerName;
  final String riderPhoneNumber;
  final String verificationStatus;
  final String permissionStatus;
  final String relationType;
  final bool isOwner;
  final bool isConnected;

  UserVehicleDto({
    required this.regNo,
    required this.imei,
    required this.ownerName,
    required this.riderPhoneNumber,
    required this.verificationStatus,
    required this.permissionStatus,
    required this.relationType,
    required this.isOwner,
    required this.isConnected,
  });

  factory UserVehicleDto.fromJson(Map<String, dynamic> json) {
    return UserVehicleDto(
      regNo: json['regNo'],
      imei: json['imei'],
      ownerName: json['ownerName'],
      riderPhoneNumber: json['riderPhoneNumber'],
      verificationStatus: json['verificationStatus'],
      permissionStatus: json['permissionStatus'],
      relationType: json['relationType'],
      isOwner: json['isOwner'],
      isConnected: json['isConnected'],
    );
  }

  static List<UserVehicleDto> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => UserVehicleDto.fromJson(json)).toList();
  }
}

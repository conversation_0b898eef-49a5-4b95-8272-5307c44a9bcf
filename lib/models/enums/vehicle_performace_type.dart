enum VehicleType { vehicle, model, fleet }
enum SummaryType { motor, battery, range }

extension VehicleTypeExtension on VehicleType {
  String get value {
    switch (this) {
      case VehicleType.vehicle:
        return "vehicle";
      case VehicleType.model:
        return "model";
      case VehicleType.fleet:
        return "fleet";
    }
  }
}

extension SummaryTypeExtension on SummaryType {
  String get value {
    switch (this) {
      case SummaryType.motor:
        return "motor";
      case SummaryType.battery:
        return "battery";
      case SummaryType.range:
        return "range";
    }
  }
}

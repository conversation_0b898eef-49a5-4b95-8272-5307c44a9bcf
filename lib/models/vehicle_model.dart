class VehicleModel {
  int id;
  String name;
  String imageUrl;
  int vehicleCount;

  VehicleModel({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.vehicleCount,
  });

  VehicleModel.fromJson(Map<String, dynamic> json)
      : id = json['id'] ?? 0,
        name = json['name'] ?? '',
        imageUrl = json['imageUrl'] ?? '',
        vehicleCount = json['vehicleCount'] ?? 0;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['imageUrl'] = imageUrl;
    data['vehicleCount'] = vehicleCount;
    return data;
  }
}

class VehicleModelsResponse {
  List<VehicleModel> vehicleModels;
  int totalPages;
  int totalModels;
  int totalVehicles;

  VehicleModelsResponse({
    required this.vehicleModels,
    required this.totalPages,
    required this.totalModels,
    required this.totalVehicles,
  });

  VehicleModelsResponse.fromJson(Map<String, dynamic> json)
      : vehicleModels = (json['vehicleModels'] as List<dynamic>?)
                ?.map((v) => VehicleModel.fromJson(v))
                .toList() ??
            [],
        totalPages = json['totalPages'] ?? 0,
        totalModels = json['totalModels'] ?? 0,
        totalVehicles = json['totalVehicles'] ?? 0;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['vehicleModels'] = vehicleModels.map((v) => v.toJson()).toList();
    data['totalPages'] = totalPages;
    data['totalModels'] = totalModels;
    data['totalVehicles'] = totalVehicles;
    return data;
  }
}

class LeadershipVehicleImage {
  final String? tag;
  final String? url;

  LeadershipVehicleImage({this.tag, this.url});

  factory LeadershipVehicleImage.fromJson(Map<String, dynamic> json) {
    return LeadershipVehicleImage(
      tag: json['tag'] as String?,
      url: json['url'] as String?,
    );
  }
}

class LeadershipStatusItem {
  final String? vehChassisNo;
  final String? vehRegNo;
  final String? vehId;
  final String? vehImei;
  final String? modelNo;
  final double? latitude;
  final double? longitude;
  final double? distance;
  final int? count;
  final List<LeadershipVehicleImage> images;

  LeadershipStatusItem({
    this.vehChassisNo,
    this.vehRegNo,
    this.vehId,
    this.vehImei,
    this.modelNo,
    this.latitude,
    this.longitude,
    this.distance,
    this.count,
    this.images = const [],
  });

  factory LeadershipStatusItem.fromJson(Map<String, dynamic> json) {
    final List<dynamic>? imagesJson = json['images'] as List<dynamic>?;
    return LeadershipStatusItem(
      vehChassisNo: json['vehChassisNo'] as String?,
      vehRegNo: json['vehRegNo'] as String?,
      vehId: json['vehId']?.toString(),
      vehImei: json['vehImei'] as String?,
      modelNo: json['modelNo'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      distance: (json['distance'] as num?)?.toDouble(),
      count: (json['count'] as num?)?.toInt(),
      images: imagesJson == null
          ? []
          : imagesJson
              .map((e) => LeadershipVehicleImage.fromJson(e as Map<String, dynamic>))
              .toList(),
    );
  }
}

class LeadershipStatusResponse {
  final List<LeadershipStatusItem> running;
  final List<LeadershipStatusItem> trips;
  final List<LeadershipStatusItem> alerts;
  final List<LeadershipStatusItem> alarms;

  LeadershipStatusResponse({
    required this.running,
    required this.trips,
    required this.alerts,
    required this.alarms,
  });

  factory LeadershipStatusResponse.fromJson(Map<String, dynamic> json) {
    List<LeadershipStatusItem> parseList(String key) {
      final List<dynamic>? arr = json[key] as List<dynamic>?;
      if (arr == null) return [];
      return arr
          .map((e) => LeadershipStatusItem.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    return LeadershipStatusResponse(
      running: parseList('running'),
      trips: parseList('trips'),
      alerts: parseList('alerts'),
      alarms: parseList('alarms'),
    );
  }
}



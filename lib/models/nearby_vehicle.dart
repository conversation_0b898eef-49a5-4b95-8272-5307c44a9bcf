import 'package:nds_app/models/image_url.dart';
import 'package:flutter/foundation.dart';

class NearbyPOI {
  List<NearByVehicle>? nearByVehicles;
  List? nearByChargingStations;

  NearbyPOI({this.nearByVehicles, this.nearByChargingStations});

  NearbyPOI.fromJson(Map<String, dynamic> json) {
    try {
      if (json['nearByVehicles'] != null) {
        nearByVehicles = <NearByVehicle>[];
        json['nearByVehicles'].forEach((v) {
          try {
            nearByVehicles!.add(NearByVehicle.fromJson(v));
          } catch (e) {
            debugPrint('Error parsing vehicle: $e');
            // Continue with other vehicles
          }
        });
      }
      if (json['nearByChargingStations'] != null) {
        nearByChargingStations = [];
        // json['nearByChargingStations'].forEach((v) {
        //   nearByChargingStations!.add(new Null.fromJson(v));
        // });
      }
    } catch (e) {
      debugPrint('Error parsing NearbyPOI: $e');
      // Set default values
      nearByVehicles = [];
      nearByChargingStations = [];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (nearByVehicles != null) {
      data['nearByVehicles'] = nearByVehicles!.map((v) => v.toJson()).toList();
    }
    if (nearByChargingStations != null) {
      data['nearByChargingStations'] = nearByChargingStations!;
    }
    return data;
  }
}

class NearByVehicle {
  String? imei;
  double? latitude;
  double? longitude;
  String? regNo;
  int? charge;
  int? distance;
  List<ImageUrl>? images;
  String? distanceUnit;
  int? locationRecordedTime;

  NearByVehicle({
    this.imei,
    this.latitude,
    this.longitude,
    this.regNo,
    this.charge,
    this.distance,
    this.images,
    this.distanceUnit,
    this.locationRecordedTime,
  });

  NearByVehicle.fromJson(Map<String, dynamic> json) {
    try {
      imei = json['imei']?.toString();
      latitude = json['latitude']?.toDouble();
      longitude = json['longitude']?.toDouble();
      regNo = json['regNo']?.toString();
      charge = json['charge']?.toInt();
      distance = json['distance']?.toInt();
      distanceUnit = json['distanceUnit']?.toString();
      locationRecordedTime = json['locationRecordedTime']?.toInt();

      images = <ImageUrl>[];
      if (json['images'] != null) {
        json['images'].forEach((v) {
          try {
            images!.add(ImageUrl.fromJson(v));
          } catch (e) {
            debugPrint('Error parsing image: $e');
            // Continue with other images
          }
        });
      }
    } catch (e) {
      debugPrint('Error parsing NearByVehicle: $e');
      // Set default values
      imei = '';
      latitude = 0.0;
      longitude = 0.0;
      regNo = '';
      charge = 0;
      distance = 0;
      distanceUnit = '';
      locationRecordedTime = 0;
      images = [];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['imei'] = imei;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['regNo'] = regNo;
    data['charge'] = charge;
    data['distance'] = distance;
    data['distanceUnit'] = distanceUnit;
    data['locationRecordedTime'] = locationRecordedTime;

    if (images != null) {
      data['images'] = images!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

import 'package:flutter/material.dart';

class StatisticsDetails {
  dynamic value;
  Map<int, dynamic>? dataPoints;
  Map<int, Map<String, double>>? modeRangeDataPoints;
  Map<String, double>? avgModeRange;

  StatisticsDetails({
    this.value,
    this.dataPoints,
    this.modeRangeDataPoints,
    this.avgModeRange,
  });

  static Map<int, dynamic>? _convertDataPoints(dynamic json) {
    if (json is Map) {
      try {
        return json
            .map((key, value) => MapEntry(int.parse(key.toString()), value));
      } catch (e) {
        debugPrint('Error converting dataPoints: $e');
        return null;
      }
    }
    return null;
  }

  static Map<int, Map<String, double>>? _convertModeRangeDataPoints(
      dynamic json) {
    if (json is Map) {
      try {
        return json.map((key, value) {
          if (value is Map) {
            return MapEntry(
                int.parse(key.toString()),
                value.map((subKey, subValue) =>
                    MapEntry(subKey.toString(), (subValue as num).toDouble())));
          }
          return MapEntry(int.parse(key.toString()), {});
        });
      } catch (e) {
        debugPrint('Error converting modeRangeDataPoints: $e');
        return null;
      }
    }
    return null;
  }

  static Map<String, double>? _convertAvgModeRange(dynamic json) {
    if (json is Map) {
      try {
        return json.map((key, value) =>
            MapEntry(key.toString(), (value as num).toDouble()));
      } catch (e) {
        debugPrint('Error converting avgModeRange: $e');
        return null;
      }
    }
    return null;
  }

  // Factory constructor to create an instance from a JSON map
  factory StatisticsDetails.fromJson(Map<String, dynamic> json) {
    return StatisticsDetails(
      value: json['value'],
      dataPoints: _convertDataPoints(json['dataPoints']),
      modeRangeDataPoints:
          _convertModeRangeDataPoints(json['modeRangeDataPoints']),
      avgModeRange: _convertAvgModeRange(json['avgModeRange']),
    );
  }

  // Method to convert an instance to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'value': value,
      'dataPoints':
          dataPoints?.map((key, value) => MapEntry(key.toString(), value)),
      'modeRangeDataPoints': modeRangeDataPoints?.map((key, value) => MapEntry(
          key.toString(),
          value.map((subKey, subValue) => MapEntry(subKey, subValue)))),
      'avgModeRange': avgModeRange,
    };
  }
}

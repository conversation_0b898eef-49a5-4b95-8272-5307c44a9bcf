class RideSummary {
  final double totalDistance;
  final String tripDuration;
  final double averageSpeed;
  final double topSpeed;
  final double startingVoltage;
  final double endingVoltage;
  final double totalDischarge;
  final double startSoc;
  final double endSoc;
  final List<SpeedAnalysis> speedAnalysis;

  RideSummary({
    required this.totalDistance,
    required this.tripDuration,
    required this.averageSpeed,
    required this.topSpeed,
    required this.startingVoltage,
    required this.endingVoltage,
    required this.totalDischarge,
    required this.startSoc,
    required this.endSoc,
    required this.speedAnalysis,
  });

  factory RideSummary.fromApiJson(Map<String, dynamic> json) {
    double parseDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is int) return value.toDouble();
      if (value is double) return value;
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    List<SpeedAnalysis> parseSpeedAnalysis(List<dynamic>? stats) {
      if (stats == null) return [];
      return stats.map((e) => SpeedAnalysis.fromApiJson(e)).toList();
    }

    // Extract batteryPerformance[0] if present
    final batteryPerf = (json['batteryPerformance'] is List &&
            (json['batteryPerformance'] as List).isNotEmpty)
        ? json['batteryPerformance'][0]
        : null;

    return RideSummary(
      totalDistance: parseDouble(json['totalGpsDistance']),
      tripDuration: json['tripDuration'] ?? '00:00:00',
      averageSpeed: parseDouble(json['averageSpeed']),
      topSpeed: parseDouble(json['topSpeed']),
      startingVoltage: parseDouble(json['startingVoltage']),
      endingVoltage: parseDouble(json['endingVoltage']),
      totalDischarge: batteryPerf != null
          ? parseDouble(batteryPerf['discharge'])
          : parseDouble(json['totalDischarge']),
      startSoc:
          batteryPerf != null ? parseDouble(batteryPerf['startSoc']) : 0.0,
      endSoc: batteryPerf != null ? parseDouble(batteryPerf['endSoc']) : 0.0,
      speedAnalysis: parseSpeedAnalysis(json['tripModeStats'] as List?),
    );
  }
}

class SpeedAnalysis {
  final String mode;
  final double speed;
  final double distance;

  SpeedAnalysis({
    required this.mode,
    required this.speed,
    required this.distance,
  });

  factory SpeedAnalysis.fromApiJson(Map<String, dynamic> json) {
    double parseDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is int) return value.toDouble();
      if (value is double) return value;
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    return SpeedAnalysis(
      mode: json['mode'] ?? '',
      speed: parseDouble(json['speed']),
      distance: parseDouble(json['distance']),
    );
  }
}

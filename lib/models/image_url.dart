import 'package:flutter/foundation.dart';

class ImageUrl {
  String? tag;
  String? url;

  ImageUrl({tag, url});

  ImageUrl.fromJson(Map<String, dynamic> json) {
    tag = json['tag'];
    url = json['url'];
    
    // Ensure URLs are valid
    if (url != null) {
      // Trim whitespace that might cause issues
      url = url!.trim();
      
      // Debug log for tracking
      if (tag == 'default') {
        debugPrint("Parsed image URL: $url");
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['tag'] = tag;
    data['url'] = url;

    return data;
  }
}

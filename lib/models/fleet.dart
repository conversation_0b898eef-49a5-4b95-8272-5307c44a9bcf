class Fleet {
  int id;
  String name;
  String status;
  int vehicleCount;

  Fleet({
    required this.id,
    required this.name,
    required this.status,
    required this.vehicleCount,
  });

  Fleet.fromJson(Map<String, dynamic> json)
      : id = json['id'] ?? 0,
        name = json['name'] ?? '',
        status = json['status'] ?? '',
        vehicleCount = json['vehicleCount'] ?? 0;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['status'] = status;
    data['vehicleCount'] = vehicleCount;
    return data;
  }
}

class FleetsResponse {
  List<Fleet> fleets;
  int totalPages;
  int totalFleets;
  int totalVehicles;

  FleetsResponse({
    required this.fleets,
    required this.totalPages,
    required this.totalFleets,
    required this.totalVehicles,
  });

  FleetsResponse.fromJson(Map<String, dynamic> json)
      : fleets = (json['fleets'] as List<dynamic>?)
                ?.map((v) => Fleet.fromJson(v))
                .toList() ??
            [],
        totalPages = json['totalPages'] ?? 0,
        totalFleets = json['totalFleets'] ?? 0,
        totalVehicles = json['totalVehicles'] ?? 0;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['fleets'] = fleets.map((v) => v.toJson()).toList();
    data['totalPages'] = totalPages;
    data['totalFleets'] = totalFleets;
    data['totalVehicles'] = totalVehicles;
    return data;
  }
}

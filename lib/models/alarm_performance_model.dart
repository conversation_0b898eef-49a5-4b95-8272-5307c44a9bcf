class MotorPerformanceModel {
  final Map<String, HealthCategory> health;
  final Map<String, MotorAlert> alerts;
  final Map<String, AlarmItem> alarms;

  MotorPerformanceModel({
    required this.health,
    required this.alerts,
    required this.alarms,
  });

  factory MotorPerformanceModel.fromJson(Map<String, dynamic> json) {
    return MotorPerformanceModel(
      health: (json['health'] as Map<String, dynamic>).map(
            (key, value) => MapEntry(key, HealthCategory.fromJson(value)),
      ),
      alerts: (json['alerts'] as Map<String, dynamic>).map(
            (key, value) => MapEntry(key, MotorAlert.fromJson(value)),
      ),
      alarms: (json['alarms'] as Map<String, dynamic>).map(
            (key, value) => MapEntry(key, AlarmItem.fromJson(value)),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'health': health.map((key, value) => MapEntry(key, value.toJson())),
      'alerts': alerts.map((key, value) => MapEntry(key, value.toJson())),
      'alarms': alarms.map((key, value) => MapEntry(key, value.toJson())),
    };
  }
}

class HealthCategory {
  final int count;
  final int percentage;
  final List<String> imei;

  HealthCategory({
    required this.count,
    required this.percentage,
    required this.imei,
  });

  factory HealthCategory.fromJson(Map<String, dynamic> json) {
    return HealthCategory(
      count: json['count'] ?? 0,
      percentage: json['percentage'] ?? 0,
      imei: (json['imei'] as List<dynamic>? ?? [])
          .map((e) => e.toString())
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'percentage': percentage,
      'imei': imei,
    };
  }
}

class MotorAlert {
  final int totalCount;
  final int infoPercentage;
  final int warningPercentage;
  final int criticalPercentage;
  final int normalPercentage;
  final List<TopVehicle> topVehicles;

  MotorAlert({
    required this.totalCount,
    required this.infoPercentage,
    required this.warningPercentage,
    required this.criticalPercentage,
    required this.normalPercentage,
    required this.topVehicles,
  });

  factory MotorAlert.fromJson(Map<String, dynamic> json) {
    return MotorAlert(
      totalCount: json['totalCount'] ?? 0,
      infoPercentage: json['infoPercentage'] ?? 0,
      warningPercentage: json['warningPercentage'] ?? 0,
      criticalPercentage: json['criticalPercentage'] ?? 0,
      normalPercentage: json['normalPercentage'] ?? 0,
      topVehicles: (json['topVehicles'] as List<dynamic>? ?? [])
          .map((e) => TopVehicle.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalCount': totalCount,
      'infoPercentage': infoPercentage,
      'warningPercentage': warningPercentage,
      'criticalPercentage': criticalPercentage,
      'normalPercentage': normalPercentage,
      'topVehicles': topVehicles.map((e) => e.toJson()).toList(),
    };
  }
}

class TopVehicle {
  final String? vehChassisNo;
  final String? vehRegNo;
  final String? vehId;
  final String? vehImei;
  final int count;

  TopVehicle({
    this.vehChassisNo,
    this.vehRegNo,
    this.vehId,
    this.vehImei,
    required this.count,
  });

  factory TopVehicle.fromJson(Map<String, dynamic> json) {
    return TopVehicle(
      vehChassisNo: json['vehChassisNo'],
      vehRegNo: json['vehRegNo'],
      vehId: json['vehId'],
      vehImei: json['vehImei'],
      count: json['count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'vehChassisNo': vehChassisNo,
      'vehRegNo': vehRegNo,
      'vehId': vehId,
      'vehImei': vehImei,
      'count': count,
    };
  }
}

class AlarmItem {
  final int totalCount;
  final int percentage;
  final List<TopVehicle> topVehicles;

  AlarmItem({
    required this.totalCount,
    required this.percentage,
    required this.topVehicles,
  });

  factory AlarmItem.fromJson(Map<String, dynamic> json) {
    return AlarmItem(
      totalCount: json['totalCount'] ?? 0,
      percentage: json['percentage'] ?? 0,
      topVehicles: (json['topVehicles'] as List<dynamic>? ?? [])
          .map((e) => TopVehicle.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalCount': totalCount,
      'percentage': percentage,
      'topVehicles': topVehicles.map((e) => e.toJson()).toList(),
    };
  }
}

class BatteryPerformanceModel {
  final String? avgTimeForFullCharge;
  final Map<String, HealthCategory>? health;
  final Map<String, AlertItem>? alerts;
  final Map<String, AlarmItem>? alarms;

  BatteryPerformanceModel({
    this.avgTimeForFullCharge,
    this.health,
    this.alerts,
    this.alarms,
  });

  factory BatteryPerformanceModel.fromJson(Map<String, dynamic> json) {
    return BatteryPerformanceModel(
      avgTimeForFullCharge: json['avgTimeForFullCharge'],
      health: (json['health'] != null && json['health'].isNotEmpty)
          ? (json['health'] as Map<String, dynamic>).map(
            (key, value) => MapEntry(key, HealthCategory.fromJson(value)),
      )
          : null,
      alerts: (json['alerts'] != null && json['alerts'].isNotEmpty)
          ? (json['alerts'] as Map<String, dynamic>).map(
            (key, value) => MapEntry(key, AlertItem.fromJson(value)),
      )
          : null,
      alarms: (json['alarms'] != null && json['alarms'].isNotEmpty)
          ? (json['alarms'] as Map<String, dynamic>).map(
            (key, value) => MapEntry(key, AlarmItem.fromJson(value)),
      )
          : null,
    );
  }
}

/// ---------------- HEALTH ----------------
class HealthCategory {
  final int count;
  final int percentage;
  final List<String> imei;

  HealthCategory({
    required this.count,
    required this.percentage,
    required this.imei,
  });

  factory HealthCategory.fromJson(Map<String, dynamic> json) {
    return HealthCategory(
      count: json['count'] ?? 0,
      percentage: json['percentage'] ?? 0,
      imei: List<String>.from(json['imei'] ?? []),
    );
  }
}

/// ---------------- ALERTS ----------------
class AlertItem {
  final int totalCount;
  final int infoPercentage;
  final int warningPercentage;
  final int criticalPercentage;
  final int normalPercentage;
  final List<TopVehicle> topVehicles;

  AlertItem({
    required this.totalCount,
    required this.infoPercentage,
    required this.warningPercentage,
    required this.criticalPercentage,
    required this.normalPercentage,
    required this.topVehicles,
  });

  factory AlertItem.fromJson(Map<String, dynamic> json) {
    return AlertItem(
      totalCount: json['totalCount'] ?? 0,
      infoPercentage: json['infoPercentage'] ?? 0,
      warningPercentage: json['warningPercentage'] ?? 0,
      criticalPercentage: json['criticalPercentage'] ?? 0,
      normalPercentage: json['normalPercentage'] ?? 0,
      topVehicles: (json['topVehicles'] as List<dynamic>? ?? [])
          .map((e) => TopVehicle.fromJson(e))
          .toList(),
    );
  }
}

/// ---------------- ALARMS ----------------
class AlarmItem {
  final int totalCount;
  final int percentage;
  final List<TopVehicle> topVehicles;

  AlarmItem({
    required this.totalCount,
    required this.percentage,
    required this.topVehicles,
  });

  factory AlarmItem.fromJson(Map<String, dynamic> json) {
    return AlarmItem(
      totalCount: json['totalCount'] ?? 0,
      percentage: json['percentage'] ?? 0,
      topVehicles: (json['topVehicles'] as List<dynamic>? ?? [])
          .map((e) => TopVehicle.fromJson(e))
          .toList(),
    );
  }
}

/// ---------------- COMMON ----------------
class TopVehicle {
  final String? vehChassisNo;
  final String? vehRegNo;
  final String? vehId;
  final String? vehImei;
  final int count;

  TopVehicle({
    this.vehChassisNo,
    this.vehRegNo,
    this.vehId,
    this.vehImei,
    required this.count,
  });

  factory TopVehicle.fromJson(Map<String, dynamic> json) {
    return TopVehicle(
      vehChassisNo: json['vehChassisNo'],
      vehRegNo: json['vehRegNo'],
      vehId: json['vehId'],
      vehImei: json['vehImei'],
      count: json['count'] ?? 0,
    );
  }
}

import 'dart:io';

String androidManifestFileUrl = 'android/app/src/main/AndroidManifest.xml';
void main(List<String> arguments) {
  String name = arguments.isEmpty ? "" : arguments[0];
  name = name[0].toUpperCase() + name.substring(1).toLowerCase();
  if (name.toLowerCase() == "b2c") {
    name = "NDS Connect";
  } else if (name.toLowerCase() == "nds") {
    name = "NDS ProRide";
  } else if (name.toLowerCase() == "lapa") {
    name = "LAPA";
  } else if (name.toLowerCase() == "prodred") {
    name = "Prod Red";
  } else if (name.toLowerCase() == "nichesolv") {
    name = "Nichesolv";
  }
  overwriteAndroidManifest(name);
}

Future<void> overwriteAndroidManifest(String name) async {
  final File androidManifestFile = File(androidManifestFileUrl);
  final List<String> lines = await androidManifestFile.readAsLines();
  for (int x = 0; x < lines.length; x++) {
    String line = lines[x];
    if (line.contains('android:label')) {
      line = line.replaceAll(
          RegExp(r'android:label="[^"]*(\\"[^"]*)*"'), 'android:label="$name"');
      lines[x] = line;
      lines.add('');
    }
  }
  androidManifestFile.writeAsString(lines.join('\n'));
}

import 'package:flutter/material.dart';
import 'package:nds_app/branding/company.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';

class Simpson extends Company {

  @override
  String splashScreenLoadingPageCircularWhite = splashScreenImages["circularWhiteAnimation"]!;

  @override
  String splashScreenLoadingPageCompanyLogoBg = splashScreenImages["simpson_Logo_Bg"]!;

  @override
  String splashScreenLoadingPageCompanyLogo1 =
      splashScreenImages["simpson_logo_1"]!;

  @override
  String splashScreenLoadingScreenCompanyLogo2 =
      splashScreenImages["simpson_logo_1"]!;

  @override
  String splashScreenLoadingScreenCompanyLogo3 =
      splashScreenImages["simpson_logo_3"]!;

  @override
  String loginScreenLogo1 = loginScreenImages["loginScreenImageBlue"]!;

  @override
  String afterConnectionCompanyLabel = homeScreenText['text17']!;

  @override
  String clusterTitleRowCompanyLogo = splashScreenImages["simpson_logo_4"]!;

  @override
  Color loginThemeColor = colorBlueSimpsonTheme;

  @override
  String contactMail = nichesolvContactMail;

  @override
  String contactPhoneNumber = nichesolvContactPhoneNumber;

  @override
  String otpSenderId = nichesolvOtpSenderId;

  @override
  String website = nichesolvWebsite;

  @override
  String iosAppId = simpsonB2BIosAppId;

  @override
  String androidPackageName = simpsonB2BAndroidPackageName;

  @override
  int noOfWheels = simpsonWheels;
}

import 'package:flutter/material.dart';
import 'package:nds_app/branding/company.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';

class Prodred extends Company {

  @override
  String splashScreenLoadingPageCircularWhite = splashScreenImages["circularWhiteAnimation"]!;

  @override
  String splashScreenLoadingPageCompanyLogoBg = splashScreenImages["nichesolv_Logo_Bg"]!;

  @override
  String splashScreenLoadingPageCompanyLogo1 =
  splashScreenImages["nichesolv_logo_1"]!;

  @override
  String splashScreenLoadingScreenCompanyLogo2 =
  splashScreenImages["nichesolv_logo_1"]!;

  @override
  String splashScreenLoadingScreenCompanyLogo3 =
  splashScreenImages["nichesolv_logo_2"]!;

  @override
  String loginScreenLogo1 = loginScreenImages["loginScreenImageGreen"]!;

  @override
  String afterConnectionCompanyLabel = homeScreenText['text29']!;

  @override
  String clusterTitleRowCompanyLogo = splashScreenImages["nichesolv_logo_1"]!;

  @override
  Color loginThemeColor = colorGreenNichesolvTheme;

  @override
  String contactMail = nichesolvContactMail;

  @override
  String contactPhoneNumber = nichesolvContactPhoneNumber;

  @override
  String otpSenderId = nichesolvOtpSenderId;

  @override
  String website = nichesolvWebsite;

  @override
  String iosAppId = nichesolvB2BIosAppId;

  @override
  String androidPackageName = nichesolvB2BAndroidPackageName;

  @override
  int noOfWheels = prodRedWheels;
}

import 'package:flutter/material.dart';
import 'package:nds_app/branding/company.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';

class Lml extends Company {

  @override
  String splashScreenLoadingPageCircularWhite = splashScreenImages["circularWhiteAnimation"]!;

  @override
  String splashScreenLoadingPageCompanyLogoBg = splashScreenImages["lml_Logo_Bg"]!;


  @override
  String splashScreenLoadingPageCompanyLogo1 =
      splashScreenImages["lml_logo_1"]!;

  @override
  String splashScreenLoadingScreenCompanyLogo2 =
      splashScreenImages["lml_logo_2"]!;

  @override
  String splashScreenLoadingScreenCompanyLogo3 =
      splashScreenImages["lml_logo_3"]!;

  @override
  String loginScreenLogo1 = loginScreenImages["loginScreenImage"]!;

  @override
  String afterConnectionCompanyLabel = homeScreenText['text14']!;

  @override
  String clusterTitleRowCompanyLogo = splashScreenImages["lml_logo_2"]!;

  @override
  Color loginThemeColor = primaryOrange;

  @override
  String contactMail = lmlContactMail;

  @override
  String contactPhoneNumber = lmlContactPhoneNumber;

  @override
  String otpSenderId = nichesolvOtpSenderId;

  @override
  String website = nichesolvWebsite;

  @override
  String iosAppId = lmlB2BIosAppId;

  @override
  String androidPackageName = lmlB2BAndroidPackageName;

  @override
  int noOfWheels = imlWheels;
}

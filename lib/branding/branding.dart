import 'package:flutter/material.dart';
import 'package:nds_app/branding/b2c.dart';
import 'package:nds_app/branding/company.dart';
import 'package:nds_app/branding/lapa.dart';
import 'package:nds_app/branding/lml.dart';
import 'package:nds_app/branding/nds.dart';
import 'package:nds_app/branding/nichesolv.dart';
import 'package:nds_app/branding/prodred.dart';
import 'package:nds_app/branding/simpson.dart';

const String companyName =
    String.fromEnvironment('company', defaultValue: 'nds');

Company company = getCompanyObject(companyName);

String splashScreenLoadingPageCircularWhite =
    company.splashScreenLoadingPageCircularWhite;

String splashScreenLoadingPageCompanyLogoBg =
    company.splashScreenLoadingPageCompanyLogoBg;

String splashScreenLoadingPageCompanyLogo1 =
    company.splashScreenLoadingPageCompanyLogo1;

String splashScreenLoadingScreenCompanyLogo2 =
    company.splashScreenLoadingScreenCompanyLogo2;

String splashScreenLoadingScreenCompanyLogo3 =
    company.splashScreenLoadingScreenCompanyLogo3;

String loginScreenLogo1 = company.loginScreenLogo1;

String afterConnectionCompanyLabel = company.afterConnectionCompanyLabel;

String clusterTitleRowCompanyLogo = company.clusterTitleRowCompanyLogo;

Color loginThemeColor = company.loginThemeColor;

String contactPhoneNumber = company.contactPhoneNumber;

String contactMail = company.contactMail;

String otpSenderId = company.otpSenderId;

String website = company.website;

String iosAppId = company.iosAppId;

String androidPackageName = company.androidPackageName;

int noOfWheels = company.noOfWheels;

Company getCompanyObject(String companyName) {
  Company company = Lml();
  if (companyName == "nds") {
    company = Nds();
  } else if (companyName == "simpson") {
    company = Simpson();
  } else if (companyName == "Nichesolv" || companyName == "nichesolv") {
    company = Nichesolv();
  } else if (companyName == "b2c") {
    company = B2C();
  } else if (companyName == "lapa") {
    company = Lapa();
  } else if (companyName == "prodred") {
    company = Prodred();
  }
  return company;
}

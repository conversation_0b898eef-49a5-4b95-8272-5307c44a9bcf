import 'package:flutter/material.dart';

const colorGrey900 = Color.fromRGBO(16, 24, 40, 1);
const colorGrey800 = Color.fromRGBO(29, 41, 57, 1);
const colorGrey700 = Color.fromRGBO(52, 64, 84, 1);
const colorGrey500 = Color.fromRGBO(102, 112, 133, 1);
const colorGrey600 = Color.fromRGBO(102, 112, 133, 1);
const colorGrey400 = Color.fromRGBO(152, 162, 179, 1);
const colorGrey300 = Color.fromRGBO(208, 213, 221, 1);
const colorGrey200 = Color.fromRGBO(234, 236, 240, 1);
const colorGrey100 = Color.fromRGBO(242, 244, 247, 1);
const colorGrey25 = Color.fromRGBO(252, 252, 253, 1);
const lightGray = Color.fromRGBO(247, 247, 247, 1);


const colorBlack900 = Color.fromRGBO(33, 36, 41, 1);

const colorPrimary600 = Color.fromRGBO(241, 101, 33, 1);
const colorPrimary300 = Color.fromRGBO(247, 177, 143, 1);

const colorSuccessGreen500 = Color.fromRGBO(18, 183, 106, 1);
const colorSuccessGreen600 = Color.fromRGBO(3, 152, 85, 1);

const colorError600 = Color.fromRGBO(217, 45, 32, 1);
const colorBlueFuelTile = Color.fromRGBO(0, 60, 95, 1);

const colorBlueBike400 = Color.fromRGBO(93, 170, 255, 1);

const colorRed600 = Color.fromRGBO(172, 4, 3, 1);

const colorWhite = Colors.white;

const colorRedForCharge = Color.fromRGBO(234, 74, 60, 1);
const colorOrangeForCharge = Color.fromRGBO(245, 148, 33, 1);
const colorYellowForCharge = Color.fromRGBO(245, 179, 27, 1);
const colorLightGreenForCharge = Color.fromRGBO(189, 202, 52, 1);
const colorMediumGreenForCharge = Color.fromRGBO(125, 179, 67, 1);
const colorDarkGreenForCharge = Color.fromRGBO(66, 160, 72, 1);
const batteryGradientColor2 = Color.fromRGBO(0, 255, 102, 1);
const batteryGradientColor1 = Color.fromRGBO(255, 0, 0, 1);
const speedometerColor = Color.fromRGBO(50, 81, 255, 1);
const speedometerEmptyColor = Color.fromRGBO(27, 32, 60, 1);

const primaryOrange = Color.fromRGBO(241, 101, 33, 1);
const primaryBlueLight = Color.fromRGBO(32, 66, 126, 1);
const unSelectedTextBoxColor = Color.fromRGBO(232, 236, 244, 1);
const otpDigitContainerColor = Color.fromRGBO(247, 248, 249, 1);
const otpVerificationScreenGrey = Color.fromRGBO(131, 139, 161, 1);
const secondaryBlue = Color.fromRGBO(100, 193, 216, 1);
const colorRed = Color.fromRGBO(236, 50, 87, 1);
const secondButtonColor = Color.fromRGBO(30, 35, 44, 0.3);
const colorBlack = Colors.black;
const colorRedRemoved = Color.fromRGBO(202, 54, 53, 1);

const colorDefaultVehicleThemeDark = Color.fromRGBO(36, 36, 36, 1);
const colorDefaultVehicleThemeLight = Color.fromRGBO(232, 232, 232, 1);

const colorNavigationBarIconGreyLight = Color.fromRGBO(176, 176, 176, 1);
const colorNavigationBarIconGreyDark = Color.fromRGBO(138, 138, 138, 1);

const colorBackgroundDarkMode = Color.fromRGBO(25, 28, 33, 1);

const colorBlack800 = Color.fromRGBO(69, 73, 81, 1);
const colorGreenNichesolvTheme = Color(0xFFACB546);
const colorBlueSimpsonTheme = Color.fromRGBO(0, 139, 214, 1);
const colorGreyWithOpacity = Color.fromRGBO(247, 247, 247, 0.97);
const colorDropShadowBlack = Color.fromRGBO(0, 0, 0, 0.25);
const colorGreenCircle = Color.fromRGBO(133, 217, 68, 1);
const colorDropShadowWhite = Color.fromRGBO(255, 255, 255, 0.25);
const colorWhiteLite = Color.fromRGBO(255, 255, 255, 0.85);
const colorOrangeAvgLine = Color.fromRGBO(255, 85, 48, 1);
const colorArcAmpMeter = Color.fromRGBO(217, 217, 217, 0.2);
const colorBackgroundWithLessOpacity = Color.fromRGBO(16, 24, 40, 0.45);
const colorRedError = Color.fromRGBO(255, 51, 51, 1);
const colorGreenSuccess = Color.fromRGBO(41, 204, 106, 1);
const colorYellowRefresh = Color(0xFFEDC03F);
const colorToggleButtonRider = Color.fromARGB(255, 248, 231, 181);
const colorToggleButtonWatcher = Color.fromARGB(255, 138, 255, 119);
const dialogRedColor = Color.fromRGBO(229, 8, 8, 1);
const colorLapaTheme = Color(0xFF0A1818);

const colorStatisticsContainerLight = Color.fromRGBO(153, 153, 153, 1);
const colorStatisticsContainerBlack = Color.fromRGBO(135, 135, 135, 1);

const colorBgStatisticsLight = Color.fromRGBO(244, 244, 244, 1);
const colorBgStatisticsDark = Color.fromRGBO(30, 30, 30, 1);
const colorLineGraphStroke = Color.fromRGBO(0, 60, 95, 1);
const selectedFleetBorderColor = Color.fromRGBO(200, 12, 23, 0.16);
const offlineColorRed = Color.fromRGBO(202, 54, 53, 1);
const backOnlineColorGreen = Color.fromRGBO(0, 188, 8, 1);
const fuelChartEV = Color.fromRGBO(0, 60, 95, 1);
const fuelChartPetrol = Color.fromRGBO(79, 94, 108, 1.0);

// Colors for Speed Analysis dots
const colorEcomode = Color.fromRGBO(68, 138, 17, 1.0); // Eco
const colorCitymode = Color.fromRGBO(232, 142, 8, 1.0); // City
const colorPowermode = Color.fromRGBO(210, 16, 16, 1.0); // Power

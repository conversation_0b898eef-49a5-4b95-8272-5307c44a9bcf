var dummyAvailableVehiclesDetails = {
  "nearByVehicles": [
    {
      "imei": "TEST_861100065560266",
      "latitude": 28.626230,
      "longitude": 79.800972,
      "regNo": "HR-10-V-1232",
      "charge": 67,
      "imageUrl": "https/Nichesolv.png",
      "distance": 1,
      "distanceUnit": "km"
    },
    {
      "imei": "KA-23",
      "latitude": 28.624846,
      "longitude": 79.802291,
      "regNo": "HR-10-V-5343",
      "charge": 34,
      "imageUrl": "https/Nichesolv.png",
      "distance": 3,
      "distanceUnit": "km"
    },
    {
      "imei": "KA-23",
      "latitude": 28.622878,
      "longitude": 79.797957,
      "regNo": "HR-10-V-5340",
      "charge": 93,
      "imageUrl": "https/Nichesolv.png",
      "distance": 15,
      "distanceUnit": "km"
    }
  ],
  "nearByChargingStations": [],
};

Map<String, dynamic> userData = {
  "user_name": "<PERSON>",
  "total_distance": 12000,
  "distance_unit": "km",
  "saved_money": 6000,
  "total_money_spent": 14000,
  "fuel_equivalent_money": 20000,
};

var connectedVehicleDetails = {
  "id": 1,
  "number": "MH 02 NA 4536",
  "distance": 10,
  "distance_unit": "km",
  "battery_percentage": 100,
  "color": [93, 170, 255, 1.0],
  "charge": 80,
  "time_remaining_in_min": 150,
  "status":
      "charging", //disconnected    //connected  //charging   //batteryRemoved    //batteryConnecting
  "modes": [
    {
      "id": 1,
      "name": "Sports",
      "range": 50,
      "range_unit": "Km",
      "color": [241, 101, 33, 1.0],
    },
    {
      "id": 2,
      "name": "Normal",
      "range": 80,
      "range_unit": "Km",
      "color": [211, 160, 14, 1.0],
    },
    {
      "id": 3,
      "name": "Eco",
      "range": 100,
      "range_unit": "Km",
      "color": [18, 183, 106, 1.0],
    },
  ],
};

const motorDcCurrentsDummy = <double>[
  2,
  45,
  50,
  45,
  120,
  0,
  130,
  64,
  130,
  123,
  20,
  10,
  40,
  125,
  75,
  46,
  97,
  0,
  78,
];
var vehicleList = [
  {
    "regNo": "KA 02 NA 4535",
    "model": "SQUID",
    "imageUrl": "https://nds-ev-images.s3.amazonaws.com/models/scooter2.png",
    "soc": 67,
    "distanceCovered": 100.0,
    "startDate": 1695728051373,
    "endDate": null
  },
  {
    "regNo": "KA 02 NA 4539",
    "model": "SQUID",
    "imageUrl": "https://nds-ev-images.s3.amazonaws.com/models/scooter2.png",
    "soc": 67,
    "distanceCovered": 100.0,
    "startDate": 1695715553974,
    "endDate": 1695720984308
  },
  {
    "regNo": "KA 02 NA 4534",
    "model": "SQUID",
    "imageUrl": "https://nds-ev-images.s3.amazonaws.com/models/scooter2.png",
    "soc": 67,
    "distanceCovered": 100.0,
    "startDate": 1695715459435,
    "endDate": 1695715551579
  },
  {
    "regNo": "KA 02 NA 4534",
    "model": "SQUID",
    "imageUrl": "https://nds-ev-images.s3.amazonaws.com/models/scooter2.png",
    "soc": 67,
    "distanceCovered": 100.0,
    "startDate": 1695715459435,
    "endDate": 1695715551579
  },
  {
    "regNo": "KA 02 NA 4534",
    "model": "SQUID",
    "imageUrl": "https://nds-ev-images.s3.amazonaws.com/models/scooter2.png",
    "soc": 67,
    "distanceCovered": 100.0,
    "startDate": 1695715459435,
    "endDate": 1695715551579
  },
  {
    "regNo": "KA 02 NA 4534",
    "model": "SQUID",
    "imageUrl": "https://nds-ev-images.s3.amazonaws.com/models/scooter2.png",
    "soc": 67,
    "distanceCovered": 100.0,
    "startDate": 1695715459435,
    "endDate": 1695715551579
  },
  {
    "regNo": "KA 02 NA 4534",
    "model": "SQUID",
    "imageUrl": "https://nds-ev-images.s3.amazonaws.com/models/scooter2.png",
    "soc": 67,
    "distanceCovered": 100.0,
    "startDate": 1695715459435,
    "endDate": 1695715551579
  }
];

var dummyProfileDetails = {
  "firstName": "John",
  "lastName": "Doe",
  "longAddress": "#123, Some street",
  "dob": "2020-10-19",
  "sex": "Male",
  "profileImageUrl": ""
};

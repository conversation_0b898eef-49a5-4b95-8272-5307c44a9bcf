import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/text_styles.dart';

class DarkTheme {
  static ThemeData themeData(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return ThemeData(
        useMaterial3: true,
        indicatorColor: colorBlack,
        primarySwatch: Colors.red,
        primaryColor: Colors.black,
        disabledColor: Colors.grey,
        hintColor: colorNavigationBarIconGreyDark,
        primaryColorLight: colorDefaultVehicleThemeDark,
        primaryColorDark: colorDefaultVehicleThemeLight,
        cardColor: colorGrey900,
        splashColor: colorBackgroundDarkMode,
        canvasColor: colorBackgroundDarkMode,
        secondaryHeaderColor: colorBackgroundDarkMode,
        brightness: Brightness.dark,
        scaffoldBackgroundColor: colorBgStatisticsDark,
        highlightColor: colorWhite,
        scrollbarTheme: ScrollbarThemeData(
          thumbVisibility: WidgetStateProperty.all(true),
          interactive: true,
          radius: const Radius.circular(10.0),
          thumbColor: WidgetStateProperty.all(Colors.blue),
          thickness: WidgetStateProperty.all(5.0),
          minThumbLength: 100,
        ),
        appBarTheme: const AppBarTheme(
          elevation: 0.0,
        ),
        shadowColor: colorDropShadowWhite,
        textTheme: TextTheme(
            headlineLarge: poppinsTextStyle(
                20 / 414 * dimensions.width, colorGrey25, FontWeight.w500),
            labelLarge: poppinsTextStyle(
                20 / 414 * dimensions.width, colorGrey25, FontWeight.w100),
            headlineMedium: poppinsTextStyle(
                16 / 414 * dimensions.width, colorGrey25, FontWeight.w500),
            headlineSmall: poppinsTextStyle(
                14 / 414 * dimensions.width, colorGrey200, FontWeight.w500),
            displayLarge: poppinsTextStyle(
                58 / 414 * dimensions.width, colorGrey25, FontWeight.w500),
            displayMedium: poppinsTextStyle(
                16 / 414 * dimensions.width, colorGrey25, FontWeight.w600),
            displaySmall: poppinsTextStyle(
                12 / 414 * dimensions.width, colorGrey25, FontWeight.w400),
            bodyMedium: poppinsTextStyle(
                18 / 414 * dimensions.width, colorGrey25, FontWeight.w600),
            labelSmall: poppinsTextStyle(
                14 / 414 * dimensions.width, colorGrey200, FontWeight.w500),
            bodySmall: poppinsTextStyle(
                12 / 414 * dimensions.width, colorGrey200, FontWeight.w400),
            titleMedium: poppinsTextStyle(
                14 / 414 * dimensions.width, colorGrey200, FontWeight.w400),
            labelMedium: poppinsTextStyle(
                14 / 414 * dimensions.width, colorGrey200, FontWeight.w500),
            titleSmall: poppinsTextStyle(
                12 / 414 * dimensions.width, colorGrey200, FontWeight.w400),
            titleLarge: poppinsTextStyle(
                40 / 414 * dimensions.width, colorGrey25, FontWeight.w600),
            bodyLarge: poppinsTextStyle(
                16 / 414 * dimensions.width, colorGrey25, FontWeight.w600)));
  }
}

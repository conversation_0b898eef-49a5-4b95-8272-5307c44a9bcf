/// Utility functions for determining ProdRed user types and leadership status
class ProdRedUserType {
  /// Determines if a user has leadership privileges based on roles and organizations
  /// Leadership is granted to users with Executive or OEM_EXECUTIVE roles
  static bool determineLeadership(List<dynamic>? roles) {
    if (roles == null) return false;
    
    return roles.any((role) {
      if (role is String) {
        return role == "Executive" || role == "OEM_EXECUTIVE";
      }
      return false;
    });
  }

  /// Determines if a user is a rider based on roles
  /// Riders are users with Rider or TEST_RIDE_ENGINEER roles
  static bool determineRider(List<dynamic>? roles) {
    if (roles == null) return false;
    
    return roles.any((role) {
      if (role is String) {
        return role == "Rider" || role == "TEST_RIDE_ENGINEER";
      }
      return false;
    });
  }
}

/// Determines ProdRed leadership status based on roles and organizations
/// This function can be extended to include organization-based logic if needed
Future<bool> determineProdRedLeadership({
  List<dynamic>? roles,
  List<dynamic>? organisations,
}) async {
  // For now, leadership is determined solely by roles
  // Can be extended to include organization-based logic
  return ProdRedUserType.determineLeadership(roles);
}

/// Normalizes role names for API calls
/// Always sends the new role names: TEST_RIDE_ENGINEER instead of Rider, OEM_EXECUTIVE instead of Executive
String normalizeRoleForApi(List<dynamic>? roles) {
  if (roles == null) return 'TEST_RIDE_ENGINEER'; // Default to rider role
  
  // Check if user has any executive role (Executive or OEM_EXECUTIVE)
  bool hasExecutiveRole = roles.any((role) {
    if (role is String) {
      return role == "Executive" || role == "OEM_EXECUTIVE";
    }
    return false;
  });
  
  // Return normalized role names for API
  return hasExecutiveRole ? 'OEM_EXECUTIVE' : 'TEST_RIDE_ENGINEER';
}
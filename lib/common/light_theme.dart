import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/text_styles.dart';

class LightTheme {
  static ThemeData themeData(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return ThemeData(
        indicatorColor: colorNavigationBarIconGreyLight,
        primaryColor: colorWhite,
        useMaterial3: true,
        brightness: Brightness.light,
        cardColor: colorGrey200,
        canvasColor: colorGrey25,
        splashColor: colorWhite,
        hintColor: colorNavigationBarIconGreyLight,
        primaryColorLight: colorDefaultVehicleThemeLight,
        primaryColorDark: colorDefaultVehicleThemeDark,
        secondaryHeaderColor: colorGreyWithOpacity,
        highlightColor: colorBlack,
        scaffoldBackgroundColor: colorBgStatisticsLight,
        scrollbarTheme: ScrollbarThemeData(
          thumbVisibility: WidgetStateProperty.all(true),
          interactive: true,
          radius: const Radius.circular(10.0),
          thumbColor: WidgetStateProperty.all(Colors.blue),
          thickness: WidgetStateProperty.all(10.0),
          minThumbLength: 100,
        ),
        appBarTheme: const AppBarTheme(
          elevation: 0.0,
        ),
        shadowColor: colorDropShadowBlack,
        primaryTextTheme: TextTheme(
          headlineLarge: poppinsTextStyle(
              24 / 414 * dimensions.width, colorGrey800, FontWeight.w500),
        ),
        textTheme: TextTheme(
            headlineLarge: poppinsTextStyle(
                20 / 414 * dimensions.width, colorGrey800, FontWeight.w500),
            labelLarge: poppinsTextStyle(
                20 / 414 * dimensions.width, colorGrey800, FontWeight.w100),
            headlineMedium: poppinsTextStyle(
                16 / 414 * dimensions.width, colorGrey800, FontWeight.w500),
            headlineSmall: poppinsTextStyle(
                14 / 414 * dimensions.width, colorGrey500, FontWeight.w500),
            displayLarge: poppinsTextStyle(
                58 / 414 * dimensions.width, colorGrey800, FontWeight.w500),
            displayMedium: poppinsTextStyle(
                16 / 414 * dimensions.width, colorGrey800, FontWeight.w600),
            displaySmall: poppinsTextStyle(
                12 / 414 * dimensions.width, colorGrey800, FontWeight.w400),
            bodyMedium: poppinsTextStyle(
                18 / 414 * dimensions.width, colorGrey800, FontWeight.w600),
            labelSmall: poppinsTextStyle(
                14 / 414 * dimensions.width, colorGrey600, FontWeight.w500),
            bodySmall: poppinsTextStyle(
                12 / 414 * dimensions.width, colorGrey500, FontWeight.w400),
            titleMedium: poppinsTextStyle(
                14 / 414 * dimensions.width, colorGrey400, FontWeight.w400),
            labelMedium: poppinsTextStyle(
                14 / 414 * dimensions.width, colorGrey800, FontWeight.w500),
            titleSmall: poppinsTextStyle(
                12 / 414 * dimensions.width, colorGrey600, FontWeight.w400),
            titleLarge: poppinsTextStyle(
                40 / 414 * dimensions.width, colorGrey900, FontWeight.w600),
            bodyLarge: poppinsTextStyle(
                16 / 414 * dimensions.width, colorGrey900, FontWeight.w600)));
  }
}

import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../common/shared_preferences_keys.dart';

class PushNotificationService {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  Future<void> initialize() async {
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      announcement: true,
      carPlay: true,
      criticalAlert: true,
      provisional: true,
    );
    _firebaseMessaging.setForegroundNotificationPresentationOptions(
        alert: true, sound: true, badge: true);
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      sendTokenToServer(getToken());
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      sendTokenToServer(getToken());
    } else {
      openAppSettings();
      debugPrint('Permission for push notifications not granted.');
    }
  }

  Future<String?> getToken() async {
    String? fcmToken = await _firebaseMessaging.getToken();
    debugPrint("fcmToken :$fcmToken");
    return fcmToken;
  }

  Future<void> sendTokenToServer(Future<String?> deviceToken) async {
    String? accualToken = await deviceToken;
    await Future.delayed(const Duration(seconds: 1), () async {
      final request = {"token": accualToken};

      Map<String, String> putRequestHeaders = {
        "Content-Type": "application/json",
        "manufacturerId": organisationId.toString(),
        "appType": (isB2CUser || isLapaUser) ? "B2C" : "B2B",
      };
      final pref = await SharedPreferences.getInstance();
      String token = pref.getString(authTokenKey) ?? "";
      if (token.isNotEmpty) {
        putRequestHeaders.addAll({"Authorization": "Bearer $token"});
      }

      http.Response? response;
      debugPrint(ApiUrls.saveDeviceToken.getUrl());
      JsonEncoder encoder = const JsonEncoder();
      response = await http
          .post(Uri.parse(ApiUrls.saveDeviceToken.getUrl()),
              headers: putRequestHeaders, body: encoder.convert(request))
          .timeout(const Duration(seconds: 10));

      debugPrint("===== url : ${ApiUrls.saveDeviceToken.getUrl()} \n ===== status : ${response.statusCode} \n ===== response : ${response.body}");
      //debugPrint("======= response :${response.body}");

      if (response.statusCode == 200) {
        debugPrint("Device Token saved for user");
      }
    });
  }

  void isTokenRefresh() {
    _firebaseMessaging.onTokenRefresh.listen((event) {
      event.toString();
      debugPrint("Rrefrshed");
    });
  }
}

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:nds_app/common/constant.dart';

class LogScreenTrackingEvent {
  final FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  void logScreenView(
      {required String eventName, Map<String, Object>? parameters = const {}}) {
    if (env == 'prod') {
      debugPrint(
          "Event Log \n Name : $eventName \n Parameters : ${parameters.toString()}");
      analytics.logEvent(
        name: eventName,
        parameters: parameters,
      );
    }
  }
}

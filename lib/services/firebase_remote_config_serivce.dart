import 'dart:io';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/services/package_info_setup.dart';

class FirebaseRemoteConfigService {
  static final FirebaseRemoteConfigService _singleton =
  FirebaseRemoteConfigService._internal();
  final packageInfo = PackageInfoSetup();
  String minVersionRemoteConfigSettingKey = "minVersion";

  factory FirebaseRemoteConfigService() {
    return _singleton;
  }
  final remoteConfig = FirebaseRemoteConfig.instance;
  FirebaseRemoteConfigService._internal();

  Future<void> initialize() async {
    final remoteConfig = FirebaseRemoteConfig.instance;

    // Set configuration
    await remoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 10),
        minimumFetchInterval: const Duration(minutes: 5),
      ),
    );

    createMinVersionKey();

    await remoteConfig.setDefaults({
      minVersionRemoteConfigSettingKey: '0.0.1',
    });

    try {
      await remoteConfig.fetchAndActivate();
    } catch (e) {
      debugPrint('[firebase_remote_config] fetchAndActivate failed: $e');
      // Continue with defaults/cached values
    }

    remoteConfig.onConfigUpdated.listen((event) async {
      await remoteConfig.activate();
    });
  }

  void createMinVersionKey() {
    String packageKey = "";

    if (Platform.isAndroid) {
      packageKey = packageInfo.packageName;
    } else if (Platform.isIOS) {
      packageKey = iosAppId;
    }

    packageKey.split(".").forEach((element) {
      minVersionRemoteConfigSettingKey =
      "${minVersionRemoteConfigSettingKey}_$element";
    });

    debugPrint(
        "------min version remote config setting key : $minVersionRemoteConfigSettingKey");
  }

  String getMinRequiredVersion() =>
      remoteConfig.getString(minVersionRemoteConfigSettingKey);
}
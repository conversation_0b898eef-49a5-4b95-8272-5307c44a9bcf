import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/models/enums/organisation_type.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:developer' as developer;

class BackendApi {
  static int timeoutInSecond = 10;

  static dynamic initiateGetCall(ApiUrls apiUrl, {Map? params, String? pathSuffix}) async {
    Map<String, String> putRequestHeaders = {
      "Content-Type": "application/json",
      "manufacturerId": organisationId.toString(),
      "appType": (isB2CUser || isLapaUser) ? "B2C" : "B2B",
    };
    params ??= {};

    params.putIfAbsent(
        "organisationType",
        () => ((isB2CUser || isLapaUser)
            ? OrganisationType.b2c.fullName
            : OrganisationType.b2b.fullName));
    params.putIfAbsent("orgId", () => organisationId.toString());
    final pref = await SharedPreferences.getInstance();
    String token = pref.getString(authTokenKey) ?? "";
    if (token.isNotEmpty) {
      debugPrint(token);
      putRequestHeaders.addAll({"Authorization": "Bearer $token"});
    }

    String paramString = "";
    if (params.keys.isNotEmpty) {
      for (int i = 0; i < params.keys.length; i++) {
        paramString += params.keys.elementAt(i) +
            "=" +
            params[params.keys.elementAt(i)].toString();
        if (i + 1 < params.keys.length) {
          paramString += "&";
        }
      }
    }
    String url = apiUrl.getUrl() + (pathSuffix ?? "") + (paramString.isNotEmpty ? "?" : "") + paramString;
    //debugPrint("===== url : $url");
    http.Response response = await http
        .get(Uri.parse(url), headers: putRequestHeaders)
        .timeout(Duration(seconds: timeoutInSecond));
    developer.log(
        "===== url : $url \n ===== status : ${response.statusCode} \n ===== response : ${response.body}");
    //debugPrint("======= response :${response.body}");
    return response;
  }

  static Future<http.Response> initiatePostCall(
    ApiUrls apiUrl, {
    Map? params,
    Map<String, dynamic>? body,
    Map? headers,
  }) async {
    body ??= {};
    body.putIfAbsent(
        "organisationType",
        () => ((isB2CUser || isLapaUser)
            ? OrganisationType.b2c.fullName
            : OrganisationType.b2b.fullName));
    body.putIfAbsent("orgId", () => organisationId.toString());

    Map<String, String> putRequestHeaders = {
      "Content-Type": "application/json",
    };
    final pref = await SharedPreferences.getInstance();
    String token = pref.getString(authTokenKey) ?? "";
    if (token.isNotEmpty) {
      putRequestHeaders.addAll({"Authorization": "Bearer $token"});
    }

    String paramString = "";
    if (params != null && params.keys.isNotEmpty) {
      for (int i = 0; i < params.keys.length; i++) {
        paramString += params.keys.elementAt(i) +
            "=" +
            params[params.keys.elementAt(i)].toString();
        if (i + 1 < params.keys.length) {
          paramString += "&";
        }
      }
    }
    String url =
        apiUrl.getUrl() + (paramString.isNotEmpty ? "?" : "") + paramString;
    // debugPrint("===== url : $url");

    http.Response? response;
    debugPrint("body  :  $body");
    JsonEncoder encoder = const JsonEncoder();
    response = await http
        .post(Uri.parse(url),
            headers: putRequestHeaders, body: encoder.convert(body))
        .timeout(Duration(seconds: timeoutInSecond));

    developer.log(
        "===== url : $url \n ===== status : ${response.statusCode} \n ===== response : ${response.body}");
    //debugPrint("======= response :${response.body}");
    debugPrint(token);
    return response;
  }

  static Future<http.Response> initiatePutCall(
    ApiUrls apiUrl, {
    Map? params,
    Map<String, dynamic>? body,
    Map? headers,
  }) async {
    Map<String, String> putRequestHeaders = {
      "Content-Type": "application/json",
      "manufacturerId": organisationId.toString(),
      "appType": (isB2CUser || isLapaUser) ? "B2C" : "B2B",
    };
    body ??= {};
    body.putIfAbsent(
        "organisationType",
        () => ((isB2CUser || isLapaUser)
            ? OrganisationType.b2c.fullName
            : OrganisationType.b2b.fullName));
    body.putIfAbsent("orgId", () => organisationId.toString());

    final pref = await SharedPreferences.getInstance();
    String token = pref.getString(authTokenKey) ?? "";
    if (token.isNotEmpty) {
      putRequestHeaders.addAll({"Authorization": "Bearer $token"});
    }

    String paramString = "";
    if (params != null && params.keys.isNotEmpty) {
      for (int i = 0; i < params.keys.length; i++) {
        paramString += params.keys.elementAt(i) +
            "=" +
            params[params.keys.elementAt(i)].toString();
        if (i + 1 < params.keys.length) {
          paramString += "&";
        }
      }
    }
    String url =
        apiUrl.getUrl() + (paramString.isNotEmpty ? "?" : "") + paramString;
    //debugPrint("===== url : $url");
    debugPrint("===== request body : $body");

    http.Response? response;
    JsonEncoder encoder = const JsonEncoder();
    response = await http
        .put(Uri.parse(url),
            headers: putRequestHeaders, body: encoder.convert(body))
        .timeout(Duration(seconds: timeoutInSecond));
    developer.log(
        "===== url : $url \n ===== status : ${response.statusCode} \n ===== response : ${response.body}");
    //debugPrint("======= response :${response.body}");

    return response;
  }

  static Future<http.StreamedResponse> initiateImageUpload(
      ApiUrls apiUrl, File? image) async {
    final pref = await SharedPreferences.getInstance();
    String token = pref.getString(authTokenKey) ?? "";

    Map<String, String> putRequestHeaders = {
      "accept": "*/*",
      "manufacturerId": organisationId.toString(),
      "appType": (isB2CUser || isLapaUser) ? "B2C" : "B2B",
    };
    if (token.isNotEmpty) {
      putRequestHeaders.addAll({"Authorization": "Bearer $token"});
    }

    final uri = Uri.parse(apiUrl.getUrl());
    //debugPrint("===== url : $uri");
    final request = http.MultipartRequest('POST', uri);
    request.headers.addAll(putRequestHeaders);

    final File file = File(image!.path);
    final fileStream = http.ByteStream(file.openRead());
    final length = await file.length();
    final fileUpload = http.MultipartFile('file', fileStream, length,
        filename: 'profile.jpeg', contentType: MediaType('image', 'jpeg'));

    request.fields['type'] = 'image/jpeg';
    request.files.add(fileUpload);
    final response = await request.send();
    developer.log("===== url : $uri \n ===== status : ${response.statusCode}");

    return response;
  }
}

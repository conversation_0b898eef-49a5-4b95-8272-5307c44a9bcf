import 'dart:io';
import 'package:location/location.dart';
import 'package:nds_app/utils/toast.dart';
import 'dart:developer' as developer;

Future<LocationData> determineLocation() async {
  Location location = Location();
  int maxRetries = 5;
  int currentRetry = 0;

  // Check if location service is enabled
  bool serviceEnabled = await location.serviceEnabled();
  if (!serviceEnabled) {
    serviceEnabled = await location.requestService();
    if (!serviceEnabled) {
      String msg = 'Location services are disabled.';
      CustomToast.error(msg);
      return Future.error(msg);
    }
  }

  // Check location permissions
  PermissionStatus permissionGranted = await location.hasPermission();
  if (permissionGranted == PermissionStatus.denied) {
    permissionGranted = await location.requestPermission();
    if (permissionGranted != PermissionStatus.granted) {
      String msg = 'Location permissions are denied';
      CustomToast.error(msg);
      return Future.error(msg);
    }
  } else if (permissionGranted == PermissionStatus.deniedForever) {
    String msg =
        'Location permissions are permanently denied, we cannot request permissions.';
    CustomToast.error(msg);
    return Future.error(msg);
  }

  // Set iOS-specific location options
  if (Platform.isIOS) {
    await location.changeSettings(
      accuracy: LocationAccuracy.balanced,
      // Use balanced instead of high for faster response
      interval: 10000,
      // 10 seconds
      distanceFilter: 0, // Accept any location update
    );
  } else {
    await location.changeSettings(
      accuracy: LocationAccuracy.high,
      interval: 5000, // 5 seconds
      distanceFilter: 10, // 10 meters
    );
  }

  // Implement retry mechanism with progressive timeout
  while (currentRetry < maxRetries) {
    try {
      developer
          .log('determineLocation: Attempt ${currentRetry + 1}/$maxRetries');

      // Progressive timeout - start with shorter timeout and increase
      Duration currentTimeout = Duration(
          seconds: Platform.isIOS
              ? (10 + (currentRetry * 5)) // iOS: 10s, 15s, 20s, 25s, 30s
              : (5 + (currentRetry * 3)) // Android: 5s, 8s, 11s, 14s, 17s
          );

      // Attempt to get location with a timeout
      LocationData locationData = await location.getLocation().timeout(
        currentTimeout,
        onTimeout: () {
          String timeoutMsg =
              'Location timeout after ${currentTimeout.inSeconds}s (attempt ${currentRetry + 1})';
          developer.log('determineLocation: $timeoutMsg');
          throw 'timeout';
        },
      );

      developer.log(
          'determineLocation: Success! lat=${locationData.latitude}, lon=${locationData.longitude}');
      return locationData;
    } catch (e) {
      developer
          .log('determineLocation: Error on attempt ${currentRetry + 1}: $e');

      // If it's our thrown timeout error, retry
      if (e == 'timeout') {
        currentRetry++;
        if (currentRetry < maxRetries) {
          developer.log('determineLocation: Retrying in 2 seconds...');
          await Future.delayed(const Duration(seconds: 2));
          continue;
        }
      } else {
        // For other errors, retry with a delay
        currentRetry++;
        if (currentRetry < maxRetries) {
          developer
              .log('determineLocation: Retrying after error in 2 seconds...');
          await Future.delayed(const Duration(seconds: 2));
          continue;
        }
      }

      // If we've exhausted all retries
      String msg = 'Failed to get location after $maxRetries attempts: $e';
      developer.log('determineLocation: $msg');
      return Future.error(msg);
    }
  }

  // If all retries failed (shouldn't reach here)
  String msg = 'Failed to get location after $maxRetries attempts';
  developer.log('determineLocation: $msg');
  return Future.error(msg);
}

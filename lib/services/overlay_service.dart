import 'package:flutter/material.dart';
import 'package:nds_app/common/dimensions.dart';

class ShowOverlay {
  static void getBottomCenterOverlay(
      BuildContext context, Widget content, double leftMargin,) async {
    OverlayState overlayState = Overlay.of(context);
    Dimensions dimensions = Dimensions(context);
    OverlayEntry overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
            top: 745 / 896 * dimensions.height,
            left: leftMargin / 414 * dimensions.width,
            child: content));
    overlayState.insert(overlayEntry);
    await Future.delayed(const Duration(seconds: 3));
    overlayEntry.remove();
  }

  static OverlayEntry getDropDownOverlayEntry(
      BuildContext context, Widget content, GlobalKey overlayButtonKey,int index) {
    final renderBox = overlayButtonKey.currentContext?.findRenderObject();

    if (renderBox == null || renderBox is! RenderBox) {
      debugPrint(
          "RenderBox is not available or the widget hasn't been rendered yet.");
      return OverlayEntry(builder: (context) => Container());
    }
    Dimensions dimensions = Dimensions(context);
    final position = renderBox.localToGlobal(Offset.zero);
    final overlayState = Overlay.of(context);
    OverlayEntry overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
            right: 16,
            top: ((position.dy) / 896 * dimensions.height)-index,
            child: content));

    overlayState.insert(overlayEntry);
    return overlayEntry;
  }

  static OverlayEntry getB2CConnectPopupOverlayEntry(
      BuildContext context, Widget content, GlobalKey overlayButtonKey) {
    final renderBox = overlayButtonKey.currentContext?.findRenderObject();

    if (renderBox == null || renderBox is! RenderBox) {
      debugPrint(
          "RenderBox is not available or the widget hasn't been rendered yet.");
      return OverlayEntry(builder: (context) => Container());
    }
    Dimensions dimensions = Dimensions(context);
    final position = renderBox.localToGlobal(Offset.zero);
    final overlayState = Overlay.of(context);
    OverlayEntry overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
            left: 20 / 414 * dimensions.width,
            top: (position.dy + 58) / 896 * dimensions.height,
            child: content));

    overlayState.insert(overlayEntry);
    return overlayEntry;
  }
}

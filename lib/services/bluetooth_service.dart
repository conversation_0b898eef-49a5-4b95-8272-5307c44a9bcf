import 'dart:async';
import 'dart:io';

import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:nds_app/utils/toast.dart';
import 'package:permission_handler/permission_handler.dart';

class BluetoothService {
  static final BluetoothService _singleton = BluetoothService._internal();

  factory BluetoothService() {
    return _singleton;
  }

  BluetoothService._internal();

  static final flutterReactiveBle = FlutterReactiveBle();
  static bool permGranted = false;
  StreamSubscription<ConnectionStateUpdate>?
      listenAndRemoveConnectedDeviceSubscription;

  static bool isBleReady = false;

  reset() async {
    Stream<ConnectionStateUpdate> stream =
        flutterReactiveBle.connectedDeviceStream;
    await stream.listen((event) {}).cancel();
  }

  void resetService() {
    flutterReactiveBle.deinitialize();
    flutterReactiveBle.initialize();
    permGranted = false;
  }

  bool isBleReadyForUse() {
    return isBleReady;
  }

  checkBleStatus() {
    flutterReactiveBle.statusStream.listen((event) {
      isBleReady = false;
      if (event.name == "ready") {
        isBleReady = true;
      }
      if (event.name != "ready") {
        CustomToast.error("Bluetooth is Off");
      }
    });
  }

  Stream<DiscoveredDevice> scanDevices() {
    Stream<DiscoveredDevice>? scanStream;

    scanStream = flutterReactiveBle.scanForDevices(withServices: []).distinct();
    return scanStream;
  }

  Future<bool> checkPermission() async {
    bool permGranted = false;
    if (Platform.isIOS) {
      permGranted = true;
    } else {
      PermissionStatus bluetoothScanStatus =
          await Permission.bluetoothScan.status;
      PermissionStatus bluetoothConnectStatus =
          await Permission.bluetoothConnect.status;
      PermissionStatus locationWhenInUseStatus =
          await Permission.locationWhenInUse.status;

      if (bluetoothConnectStatus == PermissionStatus.permanentlyDenied ||
          bluetoothScanStatus == PermissionStatus.permanentlyDenied ||
          locationWhenInUseStatus == PermissionStatus.permanentlyDenied) {
        openAppSettings();
      } else {
        permGranted = true;
        PermissionStatus status = await Permission.locationWhenInUse.request();
        if (!status.isGranted && !status.isLimited) {
          permGranted = false;
        }
        if (permGranted) {
          status = await Permission.bluetoothScan.request();
          if (!status.isGranted && !status.isLimited) {
            permGranted = false;
          }
          if (permGranted) {
            status = await Permission.bluetoothConnect.request();
            if (!status.isGranted && !status.isLimited) {
              permGranted = false;
            }
          }
        }
      }
    }
    return permGranted;
  }

  bool checkIsBluetoothReady() {
    return (flutterReactiveBle.status.name) == "ready";
  }
}

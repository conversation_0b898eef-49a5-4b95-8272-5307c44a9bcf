import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/models/add_rider_body.dart';
import 'package:nds_app/models/enums/verification_status.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/utils/toast.dart';

class UserRiderRepository {
  Future<Map<String, List<Rider>>> fetchRiders() async {
    http.Response response = await BackendApi.initiateGetCall(
      ApiUrls.userRiders,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      List<Rider> dataList = [];
      List<Rider> authorizedRiders = [];
      List<Rider> verificationPendingRiders = [];

      try {
        for (Map<String, dynamic> rider in data) {
          dataList.add(Rider.fromJson(
              rider)); // Assuming Rider.fromJson() might throw an error
        }
      } catch (e) {
        debugPrint("Error occurred while processing data: $e");
        rethrow;
      }

      for (Rider rider in dataList) {
        if (rider.verificationStatus == VerificationStatus.verified) {
          authorizedRiders.add(rider);
        } else {
          verificationPendingRiders.add(rider);
        }
      }

      return {
        'authorizedRiders': authorizedRiders,
        'verificationPendingRiders': verificationPendingRiders,
      };
    } else {
      throw Exception('Failed to load riders');
    }
  }

  Future<void> addRider(VehicleRiderBody riderBody) async {
    http.Response response = await BackendApi.initiatePutCall(
      ApiUrls.addUserRider,
      body: riderBody.toJson(),
    );

    if (response.statusCode != 200) {
      String message = 'Failed to add rider';
      CustomToast.error(message);
      throw Exception(message);
    }
  }
}

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:nds_app/models/statistics.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:http/http.dart' as http;

import '../constant/api_urls.dart';

class StatisticsRepository {
  Future<Statistics?> fetchData(DateTime startTime, DateTime endTime) async {
    JsonDecoder decoder = const JsonDecoder();
    debugPrint(
        "Statistics start_time : $startTime    end_time : $endTime \n Statistics epoch_start_time : ${startTime.millisecondsSinceEpoch}    epoch_end_time : ${endTime.millisecondsSinceEpoch}");
    try {
      http.Response response =
          await BackendApi.initiateGetCall(ApiUrls.statistics, params: {
        "startTime": startTime.millisecondsSinceEpoch,
        "endTime": endTime.millisecondsSinceEpoch
      });
      if (response.statusCode == 200) {
        Map<String, dynamic> responseMap = decoder.convert(response.body);

        return Statistics.fromJson(responseMap);
      } else if (response.statusCode == 403) {
        throw Exception("Failed to fetch ride details");
      }
    } on Exception {
      throw Exception("error while fetching data");
    }
    return null;
  }
}

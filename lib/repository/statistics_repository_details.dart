import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:nds_app/models/enums/date_time_type.dart';
import 'package:nds_app/models/enums/statistics_data_type.dart';
import 'package:nds_app/models/statistics_details.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:http/http.dart' as http;

import '../constant/api_urls.dart';

class StatisticsDetailsRepository {
  Future<StatisticsDetails?> fetchData(DateTime startTime, DateTime endTime,
      StatisticsDataType statisticsDataType, DateTimeType dateTimeType) async {
    JsonDecoder decoder = const JsonDecoder();
    debugPrint(
        "Statistics start_time : $startTime    end_time : $endTime \n Statistics epoch_start_time : ${startTime.millisecondsSinceEpoch}    epoch_end_time : ${endTime.millisecondsSinceEpoch}     Data Type : $statisticsDataType");
    
    // Add retry logic
    int maxRetries = 2;
    int retryCount = 0;
    
    while (retryCount <= maxRetries) {
      try {
        http.Response response =
            await BackendApi.initiateGetCall(ApiUrls.statisticsDetails, params: {
          "startTime": startTime.millisecondsSinceEpoch,
          "endTime": endTime.millisecondsSinceEpoch,
          "dataType": statisticsDataType.name,
          "period": dateTimeType.name.toLowerCase()
        });
        
        if (response.statusCode == 200) {
          Map<String, dynamic> responseMap = decoder.convert(response.body);
          return StatisticsDetails.fromJson(responseMap);
        } else if (response.statusCode == 403) {
          throw Exception(
              "Failed to fetch ride data details : $statisticsDataType");
        } else {
          // For other status codes, retry the request
          retryCount++;
          if (retryCount > maxRetries) {
            throw Exception("Failed to fetch statistics data after $maxRetries retries");
          }
          // Wait before retrying
          await Future.delayed(const Duration(seconds: 1));
        }
      } catch (e) {
        retryCount++;
        if (retryCount > maxRetries) {
          throw Exception("Error while fetching data: ${e.toString()}");
        }
        // Wait before retrying
        await Future.delayed(const Duration(seconds: 1));
      }
    }
    
    return null;
  }
}

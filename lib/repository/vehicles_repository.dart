import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/models/vehicle_details.dart';
import 'package:nds_app/services/api_service.dart';

class VehiclesRepository {
  Future<VehicleDetailsResponse?> fetchVehicles({
    required int page,
    int size = 10,
    String sort = '',
  }) async {
    JsonDecoder decoder = const JsonDecoder();
    try {
      final http.Response response = await BackendApi.initiateGetCall(
        ApiUrls.vehiclesDetails,
        params: {
          'page': page,
          'size': size,
          'sort': sort,
        },
      );
      if (response.statusCode == 200) {
        final Map<String, dynamic> map = decoder.convert(response.body);
        return VehicleDetailsResponse.fromJson(map);
      } else if (response.statusCode == 403) {
        throw Exception('Failed to fetch vehicles - Access denied');
      } else {
        throw Exception('Failed to fetch vehicles - Status: ${response.statusCode}');
      }
    } on Exception catch (e) {
      debugPrint('Error while fetching vehicles: $e');
      throw Exception('Error while fetching vehicles: ${e.toString()}');
    }
  }
}

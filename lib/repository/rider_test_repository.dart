import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/models/rider_test_details.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/prodred_usertype.dart';
import 'package:nds_app/services/login_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class VehicleTestRepository {
  Future<VehicleTestDetails> getVehicleTestDetails({
    String? imei, 
    int? startTime, 
    int? endTime
  }) async {
    try {
      Map<String, dynamic> params = {};
      
      // Add vIdVal parameter only if imei is provided and not empty
      if (imei != null && imei.isNotEmpty) {
        params['vIdVal'] = imei;
      }
      
      // Add time parameters
      if (startTime != null) {
        params['startTime'] = startTime.toString();
      }
      if (endTime != null) {
        params['endTime'] = endTime.toString();
      }
      
      // Add normalized role parameter from JWT token
      final prefs = await SharedPreferences.getInstance();
      final String? authToken = prefs.getString(authTokenKey);
      List<dynamic>? roles;
      
      if (authToken != null) {
        try {
          final Map<String, dynamic> token = parseJwt(authToken);
          roles = token["roles"];
        } catch (e) {
          debugPrint('Error parsing JWT token for roles: $e');
        }
      }
      
      // Use normalized role names for API calls
      params['role'] = normalizeRoleForApi(roles);
      
      // Add orgId parameter (always 2 for this app)
      params['orgId'] = '2';

      // Debug logging for API parameters
/*      debugPrint('VehicleTestRepository API Call Parameters:');
      debugPrint('vIdVal: ${params['vIdVal'] ?? 'Not provided'}');
      debugPrint('startTime: ${params['startTime'] ?? 'Not provided'}');
      debugPrint('endTime: ${params['endTime'] ?? 'Not provided'}');
      debugPrint('role: ${params['role']}');
      debugPrint('orgId: ${params['orgId']}');
      debugPrint('Full API URL: ${ApiUrls.userVehicleTestDetails.getUrl()}');*/

      http.Response response = await BackendApi.initiateGetCall(
        ApiUrls.userVehicleTestDetails,
        params: params,
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return VehicleTestDetails.fromJson(data);
      } else {
        return VehicleTestDetails.empty();
      }
    } catch (e) {
      return VehicleTestDetails.empty();
    }
  }
}

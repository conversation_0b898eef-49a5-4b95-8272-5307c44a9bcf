import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:nds_app/models/fleet.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:http/http.dart' as http;
import '../constant/api_urls.dart';

class FleetRepository {
  Future<FleetsResponse?> fetchFleets({
    int page = 0,
    int size = 10,
    String sort = ''
  }) async {
    JsonDecoder decoder = const JsonDecoder();
    debugPrint("Fetching fleets with page: $page, size: $size, sort: $sort");
    
    try {
      final http.Response response = await BackendApi.initiateGetCall(
        ApiUrls.fleets,
        params: {
          "page": page,
          "size": size,
          "sort": sort,
        },
      );
      
      if (response.statusCode == 200) {
        Map<String, dynamic> responseMap = decoder.convert(response.body);
        return FleetsResponse.fromJson(responseMap);
      } else if (response.statusCode == 403) {
        throw Exception("Failed to fetch fleets - Access denied");
      } else {
        throw Exception("Failed to fetch fleets - Status: ${response.statusCode}");
      }
    } on Exception catch (e) {
      debugPrint("Error while fetching fleets: $e");
      throw Exception("Error while fetching fleets: ${e.toString()}");
    }
  }
}

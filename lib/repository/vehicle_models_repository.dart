import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:nds_app/models/vehicle_model.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:http/http.dart' as http;

import '../constant/api_urls.dart';

class VehicleModelsRepository {
  Future<VehicleModelsResponse?> fetchVehicleModels({
    int page = 0,
    int size = 10,
    String sort = ''
  }) async {
    JsonDecoder decoder = const JsonDecoder();
    debugPrint("Fetching vehicle models with page: $page, size: $size, sort: $sort");
    
    try {
      final http.Response response = await BackendApi.initiateGetCall(
        ApiUrls.vehicleModels,
        params: {
          "page": page,
          "size": size,
          "sort": sort,
        },
      );
      
      if (response.statusCode == 200) {
        Map<String, dynamic> responseMap = decoder.convert(response.body);
        return VehicleModelsResponse.fromJson(responseMap);
      } else if (response.statusCode == 403) {
        throw Exception("Failed to fetch vehicle models - Access denied");
      } else {
        throw Exception("Failed to fetch vehicle models - Status: ${response.statusCode}");
      }
    } on Exception catch (e) {
      debugPrint("Error while fetching vehicle models: $e");
      throw Exception("Error while fetching vehicle models: ${e.toString()}");
    }
  }
}

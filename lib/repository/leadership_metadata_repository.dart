import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/models/leadership_metadata.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/prodred_usertype.dart';
import 'package:nds_app/services/login_service.dart';

class LeadershipMetadataRepository {
  Future<LeadershipMetadata?> getMetadata({
    required int startTime,
    required int endTime,
    String role = 'OEM_EXECUTIVE',
    String orgId = '2',
  }) async {
    try {
      // Derive normalized role from JW<PERSON> token when available
      List<dynamic>? roles;
      try {
        final prefs = await SharedPreferences.getInstance();
        final String? authToken = prefs.getString(authTokenKey);
        if (authToken != null) {
          final Map<String, dynamic> token = parseJwt(authToken);
          roles = token["roles"];
        }
      } catch (_) {}

      final String normalizedRole = normalizeRoleForApi(roles);

      final Map<String, dynamic> params = {
        'startTime': startTime.toString(),
        'endTime': endTime.toString(),
        'role': normalizedRole,
        'orgId': orgId,
      };

      http.Response response = await BackendApi.initiateGetCall(
        ApiUrls.leadershipMetadata,
        params: params,
      );

      if (response.statusCode != 200) {
        return null;
      }

      final Map<String, dynamic> jsonMap = json.decode(response.body);
      return LeadershipMetadata.fromJson(jsonMap);
    } catch (_) {
      return null;
    }
  }
}

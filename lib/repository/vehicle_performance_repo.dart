import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/services/api_service.dart';

import '../models/alarm_performance_model.dart';
import '../models/battery_performance_model.dart';
import '../models/enums/vehicle_performace_type.dart';
import '../models/range_performance_model.dart';


class VehiclePerformanceRepo {
  Future<T?> getPerformance<T>({
    required int entityId,
    required int startTime,
    required int endTime,
    required VehicleType vehicleType,
    required SummaryType summaryType,
  }) async {
    try {
      final Map<String, dynamic> params = {
        'entityId': entityId.toString(),
        'startTime': startTime.toString(),
        'endTime': endTime.toString(),
        'vehicleType': vehicleType.value,
        'summaryType': summaryType.value,
      };

      http.Response response = await BackendApi.initiateGetCall(
        ApiUrls.performanceSummary,
        params: params,
      );

      if (response.statusCode != 200) return null;

      final Map<String, dynamic> jsonMap = json.decode(response.body);

      if (T == MotorPerformanceModel && summaryType == SummaryType.motor) {
        return MotorPerformanceModel.fromJson(jsonMap) as T;
      } else if (T == BatteryPerformanceModel && summaryType == SummaryType.battery) {
        return BatteryPerformanceModel.fromJson(jsonMap) as T;
      } else if (T == RangePerformanceModel && summaryType == SummaryType.range) {
        return RangePerformanceModel.fromJson(jsonMap) as T;
      }

      return null;
    } catch (_) {
      return null;
    }
  }
}

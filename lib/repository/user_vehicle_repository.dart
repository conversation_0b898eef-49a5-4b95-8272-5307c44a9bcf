import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/models/enums/verification_status.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:http/http.dart' as http;

class UserVehicleRepository {
  Future<List<Rider>> fetchUserVehicles() async {
    http.Response response = await BackendApi.initiateGetCall(
      ApiUrls.userVehiclesB2C,
    );
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      List<Rider> dataList = [];
      try {
        for (Map<String, dynamic> rider in data) {
          dataList.add(Rider.fromJson(
              rider)); // Assuming Rider.fromJson() might throw an error
        }
        dataList
            .sort((a, b) => (b.isOwner ? 1 : 0).compareTo(a.isOwner ? 1 : 0));
      } catch (e) {
        debugPrint("Error occurred while processing data: $e");
        rethrow;
      }

      return dataList;
    } else {
      throw Exception('Failed to load riders');
    }
  }

  Future<void> connectRider(String riderId) async {
    // final response = await BackendApi.initiatePostCall(

    // );

    // if (response.statusCode != 200) {
    //   throw Exception('Failed to connect rider');
    // }
  }

  Future<void> disconnectRider(String riderId) async {
    // final response = await BackendApi.initiatePostCall(
    //   '${ApiUrls.disconnectRider}/$riderId',
    // );

    // if (response.statusCode != 200) {
    //   throw Exception('Failed to disconnect rider');
    // }
  }

  Future<void> acceptInvite(
      String regNo, VerificationStatus verificationStatus) async {
    http.Response response =
        await BackendApi.initiatePutCall(ApiUrls.acceptVehicleInvite, body: {
      "regNo": regNo,
      "verificationStatus": verificationStatus.name.toUpperCase()
    });

    if (response.statusCode != 200) {
      throw Exception('Failed to load riders');
    }
  }
}

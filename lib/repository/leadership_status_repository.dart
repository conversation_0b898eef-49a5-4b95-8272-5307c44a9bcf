import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/models/leadership_status.dart';
import 'package:nds_app/services/api_service.dart';

class LeadershipStatusRepository {
  Future<LeadershipStatusResponse?> getStatus({
    required int startTime,
    required int endTime,
    int page = 0,
    int size = 10,
    String orgId = '2',
  }) async {
    try {
      final Map<String, dynamic> params = {
        'orgId': orgId,
        'startTime': startTime.toString(),
        'endTime': endTime.toString(),
        'page': page.toString(),
        'size': size.toString(),
      };

      http.Response response = await BackendApi.initiateGetCall(
        ApiUrls.leadershipVehiclesStatus,
        params: params,
      );

      if (response.statusCode != 200) {
        return null;
      }

      final Map<String, dynamic> jsonMap = json.decode(response.body);
      return LeadershipStatusResponse.fromJson(jsonMap);
    } catch (_) {
      return null;
    }
  }
}



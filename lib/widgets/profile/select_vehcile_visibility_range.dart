import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';

class SelectVehicleVisibilityRange extends StatefulWidget {
  final int? selectedRange;
  final Function(int) onRangeSelect;

  const SelectVehicleVisibilityRange(
      {super.key, required this.selectedRange, required this.onRangeSelect});

  @override
  State<SelectVehicleVisibilityRange> createState() =>
      _SelectVehicleVisibilityRangeState();
}

class _SelectVehicleVisibilityRangeState
    extends State<SelectVehicleVisibilityRange> {
  late int selectedRange;

  @override
  void initState() {
    selectedRange = widget.selectedRange ?? 10;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Container(
      width: dimensions.width,
      height: 0.06 * dimensions.height,
      padding: EdgeInsets.symmetric(
          horizontal: 12.0 / 414 * dimensions.width,
          vertical: 4 / 896 * dimensions.height),
      margin: EdgeInsets.only(top: 9 / 414 * dimensions.width),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0 / 414 * dimensions.width),
        border: Border.all(color: colorGrey600),
      ),
      child: Stack(
        alignment: Alignment.centerLeft,
        children: [
          Text(
            profileScreen["setting1"]!,
            textScaler: TextScaler.noScaling,
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          Row(
            children: [
              const Expanded(child: SizedBox()),
              SizedBox(
                width: 0.2 * dimensions.width,
                child: DropdownButton(
                  value: vehicleVisibityRangeOptions.contains(selectedRange)
                      ? selectedRange
                      : null,
                  items: vehicleVisibityRangeOptions.map((int range) {
                    return DropdownMenuItem(
                      value: range,
                      child: FittedBox(
                        child: Text(
                          ("$range Km").toString(),
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    selectedRange = value ?? 10;
                    widget.onRangeSelect(int.parse(value.toString()));
                    setState(() {});
                  },
                  isExpanded: true,
                  icon: const Icon(Icons.expand_more),
                  underline: Container(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_native_contact_picker/flutter_native_contact_picker.dart';
import 'package:flutter_native_contact_picker/model/contact.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';

import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:nds_app/widgets/profile/scooter_access/rider_details.dart';
import 'package:permission_handler/permission_handler.dart';

Future getBottomSheetAddRider({required parentContext, required vehicleColor}) {
  Dimensions dimensions = Dimensions(parentContext);
  return showModalBottomSheet(
    enableDrag: true,
    isScrollControlled: false,
    isDismissible: true,
    scrollControlDisabledMaxHeightRatio: 1,
    backgroundColor: Colors.transparent,
    shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
      top: Radius.circular(20),
    )),
    context: parentContext,
    builder: (context) {
      return PopScope(
        canPop: true,
        child: Container(
          height: 296 / 896 * dimensions.height,
          width: dimensions.width,
          decoration: BoxDecoration(
              color: Theme.of(context).splashColor,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16 / 414 * dimensions.width),
                  topRight: Radius.circular(16 / 414 * dimensions.width))),
          child: Padding(
            padding: EdgeInsets.only(
              left: 16 / 414 * dimensions.width,
              right: 16 / 414 * dimensions.width,
              top: 24 / 896 * dimensions.height,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  profileScreen["scooterAccessBottomSheet1"]!,
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
                SizedBox(
                  height: 8 / 896 * dimensions.height,
                ),
                SizedBox(
                  width: 257 / 414 * dimensions.width,
                  child: Text(
                    profileScreen["scooterAccessBottomSheet2"]!,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
                SizedBox(
                  height: 20 / 896 * dimensions.height,
                ),
                Align(
                  alignment: Alignment.bottomCenter,
                  child: CustomButton.gesture(
                    onTap: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => ScooterAccessRiderDetails(
                                    null,
                                    null,
                                    isEditScreen: false,
                                    editRider: null,
                                    lastContext: parentContext,
                                  )));
                    },
                    text: profileScreen["scooterAccessBottomSheet3"]!,
                    width: 350,
                    height: 56,
                    backgroundColor: colorGrey200,
                    textColor: colorGrey800,
                    borderRadius: 1000,
                    margin: EdgeInsets.symmetric(horizontal: 8),
                  ),
                ),
                SizedBox(
                  height: 16 / 896 * dimensions.height,
                ),
                Align(
                  alignment: Alignment.bottomCenter,
                  child: CustomButton.gesture(
                    onTap: () async {
                      final FlutterNativeContactPicker contactPicker =
                          FlutterNativeContactPicker();

                      await _requestPermission();
                      Contact? contact = await contactPicker.selectContact();
                      if (contact != null) {
                        String phoneNumber = getRefactoredPhoneNumber(contact);
                        Navigator.push(
                            // ignore: use_build_context_synchronously
                            context,
                            MaterialPageRoute(
                                builder: (context) => ScooterAccessRiderDetails(
                                      contact.fullName,
                                      phoneNumber,
                                      isEditScreen: false,
                                      lastContext: parentContext,
                                    )));
                      }
                    },
                    text: profileScreen["scooterAccessBottomSheet4"]!,
                    width: 350,
                    height: 56,
                    backgroundColor: vehicleColor,
                    textColor: colorWhite,
                    borderRadius: 1000,
                    margin: EdgeInsets.symmetric(horizontal: 8),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
}

String getRefactoredPhoneNumber(Contact contact) {
  String number = contact.phoneNumbers!.first;
  if (number.startsWith("+")) {
    number = number.substring(3, number.length);
  } else if (number.startsWith("0")) {
    number = number.substring(1, number.length);
  }
  number = number.replaceAll("-", "");
  number = number.replaceAll(" ", "");
  return number;
}

Future<void> _requestPermission() async {
  var status = await Permission.contacts.status;
  if (!status.isGranted) {
    await Permission.contacts.request();
  }
}

import 'package:flutter/material.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/models/enums/relation.dart';
import 'package:nds_app/widgets/profile/scooter_access/relation_container.dart';

class RelationSelection extends StatelessWidget {
  const RelationSelection({super.key});

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 32,
        ),
        Text(
          profileScreen["scooterAccessRiderDetails3"]!,
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        Padding(
            padding: const EdgeInsets.only(top: 9.0),
            child: SizedBox(
              height: 40 / 896 * dimensions.height,
              width: 370 / 414 * dimensions.width,
              child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      RelationContainer(
                        relation: Relation.family,
                        width: 107 / 414 * dimensions.width,
                        imagePath: profileScreenImages["family"]!,
                      ),
                      SizedBox(
                        width: 8 / 414 * dimensions.width,
                      ),
                      RelationContainer(
                        relation: Relation.friend,
                        width: 104 / 414 * dimensions.width,
                        imagePath: profileScreenImages["friend"]!,
                      ),
                      SizedBox(
                        width: 8 / 414 * dimensions.width,
                      ),
                      RelationContainer(
                        relation: Relation.relatives,
                        width: 124 / 414 * dimensions.width,
                        imagePath: profileScreenImages["relative"]!,
                      ),
                      SizedBox(
                        width: 8 / 414 * dimensions.width,
                      ),
                      RelationContainer(
                        relation: Relation.others,
                        width: 107 / 414 * dimensions.width,
                        imagePath: profileScreenImages["others"]!,
                      ),
                    ],
                  )),
            ))
      ],
    );
  }
}

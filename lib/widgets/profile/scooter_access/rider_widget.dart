import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_event.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_state.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/services/overlay_service.dart';
import 'package:nds_app/widgets/profile/scooter_access/drop_down.dart';

class RiderItem extends StatefulWidget {
  final Rider rider;
   final int index;
  final Color iconColor;
  final Widget subWidget;
  final GlobalKey overlayMenuButtonKey;

  const RiderItem({
    required this.rider,
    required this.iconColor,
    required this.subWidget,
    required this.index,
    Key? key,
    required this.overlayMenuButtonKey,
  }) : super(key: key);

  @override
  State<RiderItem> createState() => _RiderItemState();
}

class _RiderItemState extends State<RiderItem> {
  OverlayEntry? overlayEntry;
  late Rider rider;

  @override
  void initState() {
    rider = widget.rider;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return Container(
      margin: EdgeInsets.symmetric(vertical: 12 / 896 * dimensions.height),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            width: 50 / 414 * dimensions.width,
            height: 50 / 896 * dimensions.height,
            child: CircleAvatar(
              child: rider.profileUrl == null
                  ? const Icon(Icons.person)
                  : Image.network(rider.profileUrl!, fit: BoxFit.fill,height: 28,width: 28,),
            ),
          ),
          SizedBox(width: 12 / 414 * dimensions.width),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(rider.ownerName,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.headlineMedium),
                SizedBox(height: 4 / 896 * dimensions.height),
                Text(
                  rider.riderPhoneNumber,
                  style: Theme.of(context)
                      .textTheme
                      .labelSmall
                      ?.copyWith(fontWeight: FontWeight.w400),
                ),
              ],
            ),
          ),
          const SizedBox(
            width: 8,
          ),
          widget.subWidget,
          const SizedBox(
            width: 8,
          ),
          BlocBuilder<EditRiderDropDownBloc, EditRiderDropDownState>(
            builder: (context, state) {
              return InkWell(
                key: widget.overlayMenuButtonKey,
                onTap: () {
                  OverlayEntry? entry = state.overlayEntry;
                  if (entry != null) {
                    entry.remove();
                    entry = null;
                  } else {
                    entry = ShowOverlay.getDropDownOverlayEntry(
                      context,
                      EditRiderDropDown(
                          rider: rider,
                          onClickEdit: () async {
                            OverlayEntry? entry = context
                                .read<EditRiderDropDownBloc>()
                                .state
                                .overlayEntry;
                            if (entry != null) {
                              entry.remove();
                              entry = null;
                              context.read<EditRiderDropDownBloc>().add(
                                  const EditRiderDropDownEvent(
                                      overlayEntry: null));
                            }
                          }),
                      widget.overlayMenuButtonKey,
                      widget.index,
                    );
                  }
                  context
                      .read<EditRiderDropDownBloc>()
                      .add(EditRiderDropDownEvent(overlayEntry: entry));
                },
                child: SizedBox(
                  width: 30 / 414 * dimensions.width,
                  height: 28 / 896 * dimensions.height,
                  child: Icon(Icons.more_vert,
                      size: 28 / 414 * dimensions.width,
                      color: widget.iconColor),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    OverlayEntry? entry =
        context.read<EditRiderDropDownBloc>().state.overlayEntry;
    if (entry != null) {
      entry.remove();
      entry = null;
      context
          .read<EditRiderDropDownBloc>()
          .add(const EditRiderDropDownEvent(overlayEntry: null));
    }

    super.dispose();
  }
}

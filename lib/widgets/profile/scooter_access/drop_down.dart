import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_event.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_rider/user_rider_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_rider/user_rider_event.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/models/add_rider_body.dart';
import 'package:nds_app/models/enums/permission_status.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/widgets/common/progress_indicator.dart';
import 'package:nds_app/widgets/profile/scooter_access/rider_details.dart';
import 'package:share_plus/share_plus.dart';

import '../../../models/enums/color_type.dart';
import '../../../services/overlay_service.dart';
import 'invite_sent.dart';

class EditRiderDropDown extends StatelessWidget {
  final Rider rider;
  final Function onClickEdit;

  const EditRiderDropDown(
      {super.key, required this.onClickEdit, required this.rider
     });

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Material(
      color: Colors.transparent,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          SizedBox(
            height: 0.15 * dimensions.height,
            width: 0.3 * dimensions.width,
            child: Image.asset(
              profileScreenImages["rider_edit_drop_down"]!,
              fit: BoxFit.fill,
            ),
          ),
          Column(
            crossAxisAlignment:  CrossAxisAlignment.start,
            children: [
              InkWell(
                onTap: () {
                  onClickEdit.call();
                  Navigator.push(
                      // ignore: use_build_context_synchronously
                      context,
                      MaterialPageRoute(
                          builder: (context) =>
                              ScooterAccessRiderDetails(
                                rider.ownerName,
                                rider.riderPhoneNumber,
                                isEditScreen: true,
                                editRider: rider,
                                lastContext:  context,
                              )));
                },
                child: Row(
                  children: [
                    SizedBox(
                      height: 20 / 896 * dimensions.height,
                      width: 20 / 414 * dimensions.width,
                      child: Image.asset(
                        profileScreenImages["edit"]!,
                        fit: BoxFit.fill,
                      ),
                    ),
                    SizedBox(
                      width: 8 / 414 * dimensions.width,
                    ),
                    Text(
                      commonText["EditTitle"]!,
                      style: poppinsTextStyle(
                          16 / 414 * dimensions.width,
                          colorGrey800,
                          FontWeight.w400),
                    )
                  ],
                ),
              ),
              SizedBox(
                height: 8 / 896 * dimensions.height,
              ),
              InkWell(
                onTap: () {
                  OverlayEntry? entry = context
                      .read<EditRiderDropDownBloc>()
                      .state
                      .overlayEntry;
                  if (entry != null) {
                    entry.remove();
                    entry = null;
                    context.read<EditRiderDropDownBloc>().add(
                        const EditRiderDropDownEvent(
                            overlayEntry: null));
                  }
                  getCircularProgressIndicator(context);
                  VehicleRiderBody vehicleRiderBody = VehicleRiderBody(
                      regNo: rider.regNo,
                      riderName: rider.ownerName,
                      riderPhoneNumber: "+91${rider.riderPhoneNumber}",
                      newRiderPhoneNumber: null,
                      relationType: rider.relationType,
                      permissionStatus: RiderPermissionStatus.removed,
                      isEdit: true);

                  context
                      .read<UserRiderBloc>()
                      .add(AddOrUpdateUserRiderEvent(vehicleRiderBody));

                  Navigator.of(context).pop();
                  ShowOverlay.getBottomCenterOverlay(
                      context,
                      const InviteSent(
                        vehicleColor: colorError600,
                        colorType: ColorType.normal,
                        label: "Rider Deleted",
                      ),
                      120,
                  );

                },
                child: Row(
                  children: [
                    SizedBox(
                      height: 20 / 896 * dimensions.height,
                      width: 20 / 414 * dimensions.width,
                      child: Image.asset(
                        profileScreenImages["delete"]!,
                        fit: BoxFit.fill,
                      ),
                    ),
                    SizedBox(
                      width: 8 / 414 * dimensions.width,
                    ),
                    Text(
                      commonText["EditLabel1"]!,
                      style: poppinsTextStyle(
                          16 / 414 * dimensions.width,
                          colorError600,
                          FontWeight.w400),
                    )
                  ],
                ),
              ),
              SizedBox(
                height: 8 / 896 * dimensions.height,
              ),
              InkWell(
                onTap: () {
                  String ownerName = userInfo?.firstName ?? "";
                  String? vehicleRegNo = rider.regNo;
                  String riderPhone = rider.riderPhoneNumber;
                  String androidId = androidPackageName;
                  String iosId = iosAppId;
                  String message = sharedRiderDetailsMessageFormat;

                  message =
                      message.replaceFirst("@ownerName", ownerName);
                  message = message.replaceFirst(
                      "@vehicleRegNo", vehicleRegNo);
                  message = message.replaceFirst(
                      "@riderPhoneNumber", riderPhone);
                  message = message.replaceFirst(
                      "@androidPackageName", androidId);
                  message = message.replaceFirst("@iosAppId", iosId);
                  Share.share(message);
                },
                child: Row(
                  children: [
                    SizedBox(
                      height: 20 / 896 * dimensions.height,
                      width: 20 / 414 * dimensions.width,
                      child: Icon(
                        Icons.share,
                        size: 20 / 414 * dimensions.width,
                        color: colorGreenSuccess,
                      ),
                    ),
                    SizedBox(
                      width: 8 / 414 * dimensions.width,
                    ),
                    Text(
                      profileScreen["scooterAccessRiderDetails9"]!,
                      style: poppinsTextStyle(
                          16 / 414 * dimensions.width,
                          colorGreenSuccess,
                          FontWeight.w400),
                    )
                  ],
                ),
              ),
              SizedBox(height: 0.0125 * dimensions.height,),
            ],
          )
        ],
      ),
    );
  }
}

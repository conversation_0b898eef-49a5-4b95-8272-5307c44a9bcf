import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/relation/select_relation_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/relation/select_relation_event.dart';
import 'package:nds_app/blocs/sctoor_access/relation/select_relation_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/models/enums/relation.dart';

class RelationContainer extends StatelessWidget {
  final Relation relation;
  final double width;
  final String imagePath;

  const RelationContainer({
    Key? key,
    required this.relation,
    required this.width,
    required this.imagePath,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return BlocBuilder<SelectRelationBloc, SelectRelationState>(
      builder: (context, state) {
        return InkWell(
          onTap: () {
            context
                .read<SelectRelationBloc>()
                .add(SelectRelationEvent(relation: relation));
          },
          child: Container(
            width: width,
            height: 40 / 896 * dimensions.height,
            decoration: BoxDecoration(
                color: state.relation == relation
                    ? colorGrey800
                    : colorBgStatisticsLight,
                border: Border.all(color: colorGrey800, width: 1),
                borderRadius: BorderRadius.all(
                    Radius.circular(44 / 414 * dimensions.width))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                    width: 20 / 414 * dimensions.width,
                    height: 20 / 896 * dimensions.height,
                    child: Image.asset(
                      imagePath,
                      fit: BoxFit.fill,
                      color: state.relation == relation
                          ? colorBgStatisticsLight
                          : colorGrey800,
                    )),
                SizedBox(
                  width: 8 / 414 * dimensions.width,
                ),
                Text(
                  relation.name,
                  textAlign: TextAlign.center,
                  style: state.relation == relation
                      ? Theme.of(context)
                          .textTheme
                          .labelMedium
                          ?.copyWith(color: colorBgStatisticsLight)
                      : Theme.of(context)
                          .textTheme
                          .labelMedium
                          ?.copyWith(color: colorGrey800),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

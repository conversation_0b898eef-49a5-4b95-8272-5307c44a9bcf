import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/models/enums/color_type.dart';

class InviteSent extends StatelessWidget {
  final Color vehicleColor;
  final ColorType colorType;
  final String label;
  const InviteSent(
      {super.key,
      required this.vehicleColor,
      required this.colorType,
      required this.label});

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Material(
      color: Colors.transparent,
      child: Container(
          alignment: Alignment.centerLeft,
          height: 48 / 896 * dimensions.height,
          padding: EdgeInsets.only(
            top: 8 / 896 * dimensions.height,
            left: 12 / 414 * dimensions.width,
            bottom: 16 / 896 * dimensions.height,
          ),
          decoration: BoxDecoration(
              color: currentVehicleStatus == VehicleStatus.connected
                  ? colorType == ColorType.light
                      ? Theme.of(context).highlightColor
                      : vehicleColor
                  : loginThemeColor,
              borderRadius: BorderRadius.circular(40 / 414 * dimensions.width)),
          child: Row(
            children: [
              SizedBox(
                height: 34 / 896 * dimensions.height,
                child: Image.asset(
                  profileScreenImages["invite_sent"]!,
                  fit: BoxFit.fitHeight,
                ),
              ),
              SizedBox(
                width: 16 / 414 * dimensions.width,
              ),
              Text(
                label,
                style: poppinsTextStyle(
                    16 / 414 * dimensions.width, colorGrey25, FontWeight.w500),
                textAlign: TextAlign.center,
              ),
              SizedBox(
                width: 24 / 414 * dimensions.width,
              ),
            ],
          )),
    );
  }
}

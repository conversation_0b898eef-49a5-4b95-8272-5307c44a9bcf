import 'package:flutter/material.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/models/about_vehicle.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../common/colors.dart';
import '../../common/strings.dart';

class VehicleSpecifications extends StatefulWidget {
  final AboutVehicle aboutVehicle;
  final String imageUrl;
  const VehicleSpecifications(
      {super.key, required this.aboutVehicle, required this.imageUrl});

  @override
  State<VehicleSpecifications> createState() => _VehicleSpecificationsState();
}

class _VehicleSpecificationsState extends State<VehicleSpecifications> {
  bool isDropDownOpen = false;
  late AboutVehicle aboutVehicle;
  late String imageUrl;

  @override
  void initState() {
    aboutVehicle = widget.aboutVehicle;
    imageUrl = widget.imageUrl;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Stack(
      children: [
        Visibility(
          visible: isDropDownO<PERSON>,
          child: Container(
            width: 376 / 414 * dimensions.width,
            height: 470 / 896 * dimensions.height,
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).highlightColor),
              borderRadius: BorderRadius.vertical(
                  top: Radius.circular(8 / 414 * dimensions.width)),
              color: Theme.of(context).secondaryHeaderColor,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 376 / 414 * dimensions.width,
                    height: 98 / 896 * dimensions.height,
                  ),
                  getVehicleSpecificationsTitleRow(
                      context, dimensions, profileScreen["aboutVehicle9"]!),
                  getVehicleSpecificationsDataRow(
                      context,
                      dimensions,
                      profileScreen["aboutVehicle10"]!,
                      aboutVehicle.batteryName ?? "-"),
                  getVehicleSpecificationsDataRow(
                      context,
                      dimensions,
                      profileScreen["aboutVehicle11"]!,
                      aboutVehicle.batteryManufacturerName ?? "-"),
                  getVehicleSpecificationsDataRow(
                      context,
                      dimensions,
                      profileScreen["aboutVehicle12"]!,
                      profileScreen["aboutVehicleValue3"]!.replaceFirst(
                          "@value",
                          (aboutVehicle.batteryCapacity ?? "-").toString())),
                  getVehicleSpecificationsTitleRow(
                      context, dimensions, profileScreen["aboutVehicle13"]!),
                  getVehicleSpecificationsDataRow(
                      context,
                      dimensions,
                      profileScreen["aboutVehicle14"]!,
                      profileScreen["aboutVehicleValue4"]!.replaceFirst(
                          "@value",
                          (aboutVehicle.rearTyreDiameter ?? "-").toString())),
                  getVehicleSpecificationsDataRow(
                      context,
                      dimensions,
                      profileScreen["aboutVehicle15"]!,
                      profileScreen["aboutVehicleValue4"]!.replaceFirst(
                          "@value",
                          (aboutVehicle.frontTyreDiameter ?? "-").toString())),
                  getVehicleSpecificationsDataRow(
                      context,
                      dimensions,
                      profileScreen["aboutVehicle16"]!,
                      aboutVehicle.tyreManufacturerName ?? "-"),
                  getVehicleSpecificationsTitleRow(
                    context,
                    dimensions,
                    profileScreen["aboutVehicle17"]!,
                  ),
                ],
              ),
            ),
          ),
        ),
        Container(
          width: 374 / 414 * dimensions.width,
          height: 98 / 896 * dimensions.height,
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).highlightColor),
            borderRadius:
                BorderRadius.all(Radius.circular(8 / 414 * dimensions.width)),
            color: Theme.of(context).secondaryHeaderColor,
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: 16 / 414 * dimensions.width,
              vertical: 16 / 414 * dimensions.width,
            ),
            child: Row(
              children: [
                Container(
                  width: 64 / 414 * dimensions.width,
                  height: 64 / 896 * dimensions.height,
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).shadowColor,
                        spreadRadius: 0,
                        blurRadius: 4,
                      )
                    ],
                    borderRadius: BorderRadius.all(
                        Radius.circular(8 / 414 * dimensions.width)),
                    color: Theme.of(context).splashColor,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      memCacheWidth: 150,
                      maxWidthDiskCache: 150,
                      fadeInDuration: const Duration(milliseconds: 100),
                      placeholder: (context, url) => const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2.0),
                      ),
                      errorWidget: (context, url, error) => const Icon(Icons.electric_bike),
                    ),
                  ),
                ),
                SizedBox(
                  width: 16 / 414 * dimensions.width,
                ),
                FittedBox(
                  child: SizedBox(
                    width: 209 / 414 * dimensions.width,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          profileScreen['aboutVehicle6']!,
                          style: Theme.of(context).textTheme.bodyMedium,
                          textScaler: TextScaler.noScaling,
                        ),
                        SizedBox(
                          height: 4 / 896 * dimensions.height,
                        ),
                        SizedBox(
                            width: 120 / 414 * dimensions.width,
                            child: getVehicleDetailsRow(dimensions,
                                profileScreen["aboutVehicle7"]!, "-", context)),
                        SizedBox(
                            width: 120 / 414 * dimensions.width,
                            child: getVehicleDetailsRow(dimensions,
                                profileScreen["aboutVehicle8"]!, "-", context)),
                      ],
                    ),
                  ),
                ),
                IconButton(
                    onPressed: () {
                      if (isDropDownOpen) {
                        isDropDownOpen = false;
                      } else {
                        isDropDownOpen = true;
                      }
                      setState(() {});
                    },
                    icon: Icon(isDropDownOpen
                        ? Icons.keyboard_arrow_up_outlined
                        : Icons.keyboard_arrow_down_outlined))
              ],
            ),
          ),
        ),
      ],
    );
  }

  Container getVehicleSpecificationsTitleRow(
      BuildContext context, Dimensions dimensions, String title) {
    return Container(
      alignment: Alignment.center,
      width: 374 / 414 * dimensions.width,
      height: 46 / 896 * dimensions.height,
      decoration: BoxDecoration(
        color: Theme.of(context).secondaryHeaderColor,
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.displayMedium,
      ),
    );
  }

  Container getVehicleSpecificationsDataRow(
      BuildContext context, Dimensions dimensions, String key, String value) {
    return Container(
      alignment: Alignment.center,
      width: 374 / 414 * dimensions.width,
      height: 46 / 896 * dimensions.height,
      decoration: BoxDecoration(
        border:
            const Border.symmetric(horizontal: BorderSide(color: colorGrey400)),
        color: Theme.of(context).secondaryHeaderColor,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            width: 10 / 414 * dimensions.width,
          ),
          SizedBox(
            width: 172 / 414 * dimensions.width,
            child: Text(
              key,
              textAlign: TextAlign.start,
              style: Theme.of(context).textTheme.labelMedium,
            ),
          ),
          const VerticalDivider(
            color: colorGrey400,
          ),
          SizedBox(
            width: 172 / 414 * dimensions.width,
            child: Text(
              value,
              textAlign: TextAlign.start,
              style: Theme.of(context).textTheme.labelSmall,
            ),
          ),
        ],
      ),
    );
  }

  Row getVehicleDetailsRow(
      Dimensions dimensions, String key, String value, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(key,
            style: Theme.of(context)
                .textTheme
                .displaySmall
                ?.copyWith(fontWeight: FontWeight.w800)),
        Text(value, style: Theme.of(context).textTheme.displaySmall),
      ],
    );
  }
}

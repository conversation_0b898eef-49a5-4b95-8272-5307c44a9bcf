import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';

class SelectHomeDisplayName extends StatefulWidget {
  final String selectedHomeDisplayName;
  final Function(String) onHomeDisplayNameSelect;
  const SelectHomeDisplayName(
      {super.key,
      required this.selectedHomeDisplayName,
      required this.onHomeDisplayNameSelect});

  @override
  State<SelectHomeDisplayName> createState() => _SelectHomeDisplayNameState();
}

class _SelectHomeDisplayNameState extends State<SelectHomeDisplayName> {
  late String selectedHomeDisplayName;
  @override
  void initState() {
    selectedHomeDisplayName = widget.selectedHomeDisplayName;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Container(
      width: dimensions.width,
      height: 0.06 * dimensions.height,
      padding: EdgeInsets.symmetric(
          horizontal: 12.0 / 414 * dimensions.width,
          vertical: 4 / 896 * dimensions.height),
      margin: EdgeInsets.only(top: 9 / 414 * dimensions.width),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0 / 414 * dimensions.width),
        border: Border.all(color: colorGrey600),
      ),
      child: Stack(
        alignment: Alignment.centerLeft,
        children: [
          Text(
            profileScreen["setting2"]!,
            textScaler: TextScaler.noScaling,
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              SizedBox(
                width:
                    (homeDisplayNameOptions[0].contains(selectedHomeDisplayName)
                            ? 0.25
                            : 0.38) *
                        dimensions.width,
                child: DropdownButton(
                  value:
                      homeDisplayNameOptions.contains(selectedHomeDisplayName)
                          ? selectedHomeDisplayName
                          : null,
                  items: homeDisplayNameOptions.map((String name) {
                    return DropdownMenuItem(
                      value: name,
                      child: FittedBox(
                        child: Text(
                          name,
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    selectedHomeDisplayName = value!;
                    widget.onHomeDisplayNameSelect(value);
                    setState(() {});
                  },
                  isExpanded: true,
                  icon: const Icon(Icons.expand_more),
                  underline: Container(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

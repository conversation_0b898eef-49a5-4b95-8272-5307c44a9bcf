import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nds_app/common/colors.dart';

class EditProfileItem extends StatelessWidget {
  const EditProfileItem({super.key,
    required this.title,
    required this.hint,
    required this.controller,
    required this.onTap,
    this.onChanged,
    this.errorText,
    required this.keyboardType,
    required this.maxLength,
    this.isTextFieldEnabled,
    this.isNameTextField});

  final String title;
  final String hint;
  final TextEditingController controller;
  final void Function() onTap;
  final Function(String)? onChanged;
  final String? errorText;
  final TextInputType keyboardType;
  final int maxLength;
  final bool? isTextFieldEnabled;
  final bool? isNameTextField;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(
          height: 32,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            Padding(
              padding: const EdgeInsets.only(top: 9.0),
              child: TextField(
                enabled: isTextFieldEnabled ?? true,
                controller: controller,
                onChanged: onChanged,
                /*inputFormatters: [
                  LengthLimitingTextInputFormatter(maxLength),
                ],*/
                inputFormatters: isNameTextField == true ?  [
                  FilteringTextInputFormatter.deny(RegExp("[0-9]")),
                  LengthLimitingTextInputFormatter(maxLength),
                ] :  [
                  LengthLimitingTextInputFormatter(maxLength),
                ],
                keyboardType: keyboardType,
                style: isTextFieldEnabled ?? true ? Theme.of(context).textTheme.labelMedium : Theme.of(context).textTheme.labelSmall,
                decoration: InputDecoration(
                  hintText: hint,
                  hintStyle: Theme.of(context).textTheme.headlineSmall,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    borderSide: const BorderSide(
                      color: colorGrey600,
                      width: 1.0,
                    ),
                  ),
                ),
                onTap: onTap,
              ),
            ),
            if (errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(
                  errorText!,
                  style: const TextStyle(
                    color: Colors.red,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }
}

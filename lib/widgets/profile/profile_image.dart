import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/main.dart';
import '../../common/dimensions.dart';
import '../../common/image_urls.dart';

class ProfileImage extends StatelessWidget {
  final String? imageUrl;
  final bool? isHomeProfile;
  const ProfileImage({super.key, required this.imageUrl,required this.isHomeProfile});

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Container(
      decoration: BoxDecoration(
          color: colorGrey300,
          borderRadius: BorderRadius.circular(50),
          boxShadow: [
            BoxShadow(
                color:
                    MyApp.of(context).getCurrentThemeMode() == ThemeMode.light
                        ? colorGrey300.withOpacity(0.5)
                        : colorGrey600.withOpacity(0.5),
                offset: const Offset(3, 0),
                spreadRadius: 5,
                blurRadius: 8)
          ]),
      clipBehavior: Clip.hardEdge,
      width: isHomeProfile ?? false ? 60 : 0.4 * dimensions.width,
      height: isHomeProfile ?? false ? 60 : 0.4 * dimensions.width,
      child: imageUrl == null || imageUrl!.isEmpty
          ? Image.asset(
              profileScreenImages["user_profile_placeholder"]!,
              fit: BoxFit.contain,
            )
          : CachedNetworkImage(
              imageUrl: imageUrl!,
              fit: BoxFit.fill,
              memCacheWidth: 300,
              maxWidthDiskCache: 300,
              fadeInDuration: const Duration(milliseconds: 100),
              placeholder: (context, url) => const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2.0),
              ),
              errorWidget: (context, url, error) => Image.asset(
                profileScreenImages["user_profile_placeholder"]!,
                fit: BoxFit.contain,
              ),
            ),
    );
  }
}

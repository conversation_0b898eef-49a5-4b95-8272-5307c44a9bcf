import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/strings.dart';

final List<String> genderOptions = [
  profileScreen["male"]!,
  profileScreen["female"]!
];

class SelectGender extends StatelessWidget {
  final String? selectedGender;
  final Function(String) onGenderSelect;

  const SelectGender(
      {super.key, required this.selectedGender, required this.onGenderSelect});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 4),
      margin: const EdgeInsets.only(top: 9),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: colorGrey600),
      ),
      child: DropdownButton(
        value: genderOptions.contains(selectedGender) ? selectedGender : null,
        items: genderOptions.map((String gender) {
          return DropdownMenuItem(
            value: gender,
            child: Text(
              gender,
              style: Theme.of(context).textTheme.labelMedium,
            ),
          );
        }).toList(),
        onChanged: (value) {
          onGenderSelect(value!);
        },
        hint: Text(profileScreen["hint4"]!),
        isExpanded: true,
        icon: const Icon(Icons.expand_more),
        underline: Container(),
      ),
    );
  }
}

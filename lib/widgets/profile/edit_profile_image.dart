import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/utils/toast.dart';
import 'package:nds_app/widgets/profile/profile_image.dart';

import '../../common/dimensions.dart';

class EditProfileImage extends StatelessWidget {
  final String? imageUrl;
  final File? pickedImage;
  final Color editIconBg;
  final ImagePicker _picker = ImagePicker();
  final Function(File) onImagePicked;

  EditProfileImage(
      {super.key,
      required this.imageUrl,
      required this.editIconBg,
      required this.pickedImage,
      required this.onImagePicked});

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return GestureDetector(
      onTap: () {
        pickImage();
      },
      child: Stack(
        children: [
          if (pickedImage != null)
            Container(
              width: 0.4 * dimensions.width,
              height: 0.4 * dimensions.width,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                image: DecorationImage(
                    fit: BoxFit.fill,
                    image: FileImage(File(pickedImage!.path))),
              ),
            )
          else
            ProfileImage(imageUrl: imageUrl, isHomeProfile: false,),

         Positioned(
           top: 0,
            bottom: 0,
            right: 0,
            left: 0,
            child: Container(
              decoration: BoxDecoration(
                color: editIconBg.withOpacity(0.5),
                borderRadius: BorderRadius.circular(50),
              ),
              height: 24,
              width: 24,
              child: const Icon(
                Icons.add_a_photo,
                size: 16,
                color: colorWhite,
              ),
            ),
          )
        ],
      ),
    );
  }

  Future<void> pickImage() async {
    final XFile? pickedImage =
        await _picker.pickImage(source: ImageSource.gallery);

    if (pickedImage != null) {
      File imageFile = File(pickedImage.path);

      int maxSizeInBytes = 1 * 1024 * 1024; // 1 MB
      int fileSize = await imageFile.length();

      if (fileSize <= maxSizeInBytes) {
        onImagePicked(imageFile);
      } else {
        CustomToast.error(profileScreen["image_size_error_msg"]!);
      }
    }
  }
}

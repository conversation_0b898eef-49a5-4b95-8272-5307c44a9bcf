import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/utils/format_date.dart';

class SelectDOB extends StatelessWidget {
  final String? dob;
  final Function(String) onDateSelect;
  const SelectDOB({super.key, required this.dob, required this.onDateSelect});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 9),
      padding: const EdgeInsets.only(top: 20, bottom: 20, left: 12),
      decoration: BoxDecoration(
        border: Border.all(
          color: colorGrey500,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: GestureDetector(
        onTap: () {
          _selectDate(context);
        },
        child: Text(
          dob != null ? formatDate(dob!) : profileScreen["hint3"]!,
          style: Theme.of(context).textTheme.labelMedium,
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.now(),
        firstDate: DateTime(1900),
        lastDate: DateTime.now(),
        keyboardType: TextInputType.text);

    if (picked != null) {
      final String date =
          "${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}";
      onDateSelect(date);
    }
  }
}

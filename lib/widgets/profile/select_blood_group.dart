import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';

class SelectBloodGroup extends StatelessWidget {
  final String? selectedBloodGroup;
  final Function(String) onBloodGroupSelect;

  const SelectBloodGroup(
      {super.key,
      required this.selectedBloodGroup,
      required this.onBloodGroupSelect});

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Container(
      width: 1 * dimensions.width,
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 4),
      margin: const EdgeInsets.only(top: 9),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: colorGrey600),
      ),
      child: Stack(
        alignment: Alignment.centerLeft,
        children: [
          Text(
            selectedBloodGroup!.isEmpty
                ? profileScreen["hint10"]!
                : selectedBloodGroup ?? "",
            style: selectedBloodGroup!.isEmpty
                ? Theme.of(context).textTheme.headlineSmall
                : Theme.of(context).textTheme.headlineMedium,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              SizedBox(
                width: 0.3 * dimensions.width,
                child: DropdownButton(
                  items: bloodGroupOptions.map((String gender) {
                    return DropdownMenuItem(
                      value: gender,
                      child: Text(
                        gender,
                        style: Theme.of(context).textTheme.labelMedium,
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    onBloodGroupSelect(value!);
                  },
                  isExpanded: true,
                  icon: const Icon(Icons.expand_more),
                  underline: Container(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/riding_modes.dart';
import 'package:nds_app/utils/calculate_data.dart';

class ClusterMode extends StatefulWidget {
  final bool isPortrait;
  final RidingModes ridingMode;

  const ClusterMode(
      {super.key, required this.isPortrait, required this.ridingMode});

  @override
  State<ClusterMode> createState() => _ClusterModeState();
}

class _ClusterModeState extends State<ClusterMode> {
  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    double size = widget.isPortrait ? dimensions.width : dimensions.height;
    AssetImage image = AssetImage(
        getClusterImageUrlByMode(widget.isPortrait, widget.ridingMode));

    return widget.isPortrait
        ? Container(
            width: 126 / 360 * dimensions.width,
            height: 83 / 800 * dimensions.height,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: image,
                //   fit: BoxFit.cover,
              ),
            ),
            child: Align(
              alignment: Alignment.bottomCenter,
              child: Text(
                widget.ridingMode.name.toUpperCase(),
                style: poppinsTextStyle(
                    22 / 360 * size, colorWhite, FontWeight.w500),
              ),
            ),
          )
        : Container(
            width: 126 / 360 * dimensions.height,
            height: 120 / 360 * dimensions.height,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: image,
                //fit: BoxFit.cover,
              ),
            ),
            child: Align(
              alignment: Alignment.bottomCenter,
              child: Text(
                widget.ridingMode.name.toUpperCase(),
                style: poppinsTextStyle(
                    20 / 360 * size, colorWhite, FontWeight.w500),
              ),
            ),
          );
  }
}

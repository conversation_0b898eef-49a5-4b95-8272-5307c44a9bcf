import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';

class ClusterRange extends StatelessWidget {
  final bool isPortrait;
  const ClusterRange(
      {super.key, required this.isPortrait, required this.rangeInKm});
  final double rangeInKm;

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    double size = isPortrait ? dimensions.width : dimensions.height;
    return isPortrait
        ? getPortraitWidget(dimensions, size)
        : getLandscapeWidget(dimensions, size);
  }

  Widget getLandscapeWidget(Dimensions dimensions, double size) {
    return Container(
      height: 110 / 360 * dimensions.height,
      width: 160 / 360 * dimensions.height,
      decoration: const BoxDecoration(
        color: colorGrey800,
        borderRadius: BorderRadius.all(
          Radius.circular(10.0),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 5),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
                width: 24 / 414 * size,
                child: Image.asset(clusterScreenImages["range_icon"]!)),
            Text(
              clusterScreenText["km"]!
                  .replaceAll("@km", rangeInKm.floor().toString()),
              style: poppinsTextStyle(
                  30 / 360 * size, colorWhite, FontWeight.w500),
            ),
            Text(
              clusterScreenText["range"]!,
              style: poppinsTextStyle(
                  16 / 360 * size, colorWhite, FontWeight.w500),
            )
          ],
        ),
      ),
    );
  }

  Widget getPortraitWidget(Dimensions dimensions, double size) {
    return Container(
      width: 315 / 360 * dimensions.width,
      decoration: const BoxDecoration(
        color: colorGrey800,
        borderRadius: BorderRadius.all(
          Radius.circular(10.0),
        ),
      ),
      child:FittedBox( child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
              height: 24 / 896 * dimensions.height,
              child: Image.asset(clusterScreenImages["range_icon"]!)),
          Text(
            clusterScreenText["km"]!
                .replaceAll("@km", rangeInKm.floor().toString()),
            style:
                poppinsTextStyle(30 / 360 * size, colorWhite, FontWeight.w500),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              clusterScreenText["range"]!,
              style: poppinsTextStyle(
                  16 / 360 * size, colorWhite, FontWeight.w500),
            ),
          )
        ],
      ),),
    );
  }
}

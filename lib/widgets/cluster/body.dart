import 'package:flutter/material.dart';
import 'package:nds_app/constant/riding_modes.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/streams/vehicle_data.dart';
import 'package:nds_app/widgets/cluster/mode.dart';
import 'package:nds_app/widgets/cluster/range.dart';
import 'package:nds_app/widgets/cluster/speedometer.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/common/strings.dart';
import 'dynamic_panel.dart';

class ClusterBody extends StatefulWidget {
  final bool isPortrait;
  final VehicleInfo vehicleInfo;
  const ClusterBody(
      {super.key, required this.isPortrait, required this.vehicleInfo});

  @override
  State<ClusterBody> createState() => _ClusterBodyState();
}

class _ClusterBodyState extends State<ClusterBody> {
  VehicleDataStream stream = VehicleDataStream();
  late VehicleInfo vehicleInfo;
  @override
  void initState() {
    vehicleInfo = widget.vehicleInfo;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return widget.isPortrait
        ? Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              StreamBuilder(
                  stream: stream.vehicleInfo,
                  builder: (context, snapshot) {
                    if (snapshot.data != null) {
                      vehicleInfo = snapshot.data!;
                    }

                    RidingModes? currentRidingMode = getRideMode(vehicleInfo.currentDriveMode ?? "eco");
                    
                    // If no valid mode found, show data not available
                    if (currentRidingMode == null) {
                      return Container(
                        width: 126 / 360 * dimensions.width,
                        height: 83 / 800 * dimensions.height,
                        decoration: BoxDecoration(
                          border: Border.all(color: colorGrey200),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: Text(
                            commonText['modeDataNotAvailable']!,
                            textAlign: TextAlign.center,
                            style: poppinsTextStyle(
                              12 / 360 * dimensions.width, 
                              colorGrey400, 
                              FontWeight.w400
                            ),
                          ),
                        ),
                      );
                    }

                    return ClusterMode(
                      isPortrait: widget.isPortrait,
                      ridingMode: currentRidingMode,
                    );
                  }),
              StreamBuilder(
                  stream: stream.vehicleInfo,
                  builder: (context, snapshot) {
                    if (snapshot.data != null) {
                      vehicleInfo = snapshot.data!;
                    }
                    return ClusterSpeedometer(
                      diMotion: vehicleInfo.diMotion ?? false,
                      isPortrait: widget.isPortrait,
                      odoInKm: vehicleInfo.totalDistance ?? 0,
                      speed: vehicleInfo.speed ?? 0,
                    );
                  }),
              StreamBuilder(
                  stream: stream.vehicleInfo,
                  builder: (context, snapshot) {
                    if (snapshot.data != null) {
                      vehicleInfo = snapshot.data!;
                    }
                    return ClusterRange(
                      isPortrait: widget.isPortrait,
                      rangeInKm: getEstimatedRangeOfCurrentMode(
                          vehicleInfo, vehicleInfo.charge!),
                    );
                  }),
              StreamBuilder(
                stream: stream.vehicleInfo,
                builder: (context, snapshot) {
                  if (snapshot.data != null) {
                    vehicleInfo = snapshot.data!;
                  }
                  return ClusterDynamicPanel(
                      isPortrait: widget.isPortrait, vehicleInfo: vehicleInfo);
                },
              ),
            ],
          )
        : Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              StreamBuilder(
                  stream: stream.vehicleInfo,
                  builder: (context, snapshot) {
                    if (snapshot.data != null) {
                      vehicleInfo = snapshot.data!;
                    }
                    return ClusterSpeedometer(
                      diMotion: vehicleInfo.diMotion ?? false,
                      isPortrait: widget.isPortrait,
                      odoInKm: vehicleInfo.totalDistance ?? 0,
                      speed: vehicleInfo.speed ?? 0,
                    );
                  }),
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  StreamBuilder(
                      stream: stream.vehicleInfo,
                      builder: (context, snapshot) {
                        if (snapshot.data != null) {
                          vehicleInfo = snapshot.data!;
                        }
                        RidingModes? currentRidingMode = getRideMode(vehicleInfo.currentDriveMode ?? "");
                        
                        // If no valid mode found, show data not available
                        if (currentRidingMode == null) {
                          return Container(
                            width: 126 / 360 * dimensions.height,
                            height: 120 / 360 * dimensions.height,
                            decoration: BoxDecoration(
                              border: Border.all(color: colorGrey200),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Text(
                                commonText['modeDataNotAvailable']!,
                                textAlign: TextAlign.center,
                                style: poppinsTextStyle(
                                  12 / 360 * dimensions.height, 
                                  colorGrey400, 
                                  FontWeight.w400
                                ),
                              ),
                            ),
                          );
                        }

                        return ClusterMode(
                          isPortrait: widget.isPortrait,
                          ridingMode: currentRidingMode,
                        );
                      }),
                  StreamBuilder(
                      stream: stream.vehicleInfo,
                      builder: (context, snapshot) {
                        return ClusterRange(
                          isPortrait: widget.isPortrait,
                          rangeInKm: getEstimatedRangeOfCurrentMode(
                              vehicleInfo, vehicleInfo.charge!),
                        );
                      }),
                ],
              ),
              StreamBuilder(
                stream: stream.vehicleInfo,
                builder: (context, snapshot) {
                  if (snapshot.data != null) {
                    vehicleInfo = snapshot.data!;
                  }
                  return ClusterDynamicPanel(
                      isPortrait: widget.isPortrait, vehicleInfo: vehicleInfo);
                },
              ),
            ],
          );
  }

  RidingModes? getRideMode(String mode) {
    // Check if mode is empty or null
    if (mode.isEmpty) {
      return null; // Return null to indicate data not available
    }
    
    try {
      return RidingModes.values.byName((mode).toLowerCase());
    } catch (e) {
      // Return null to indicate data not available instead of defaulting to eco mode
      return null;
    }
  }

  double getEstimatedRangeOfCurrentMode(VehicleInfo vehicleInfo, int soc) {
    double maxRange = 0;
    double correction = 0;
    RidingModes? currentDriveMode = getRideMode(vehicleInfo.currentDriveMode ?? "");
    
    // If no valid mode found, return 0 to indicate data not available
    if (currentDriveMode == null) {
      return 0;
    }
    
    vehicleInfo.vehicleModeInfoList?.forEach((element) {
      if (element.mode?.toLowerCase() != "reverse") {
        RidingModes? mode;
        try {
          mode = RidingModes.values.firstWhere((e) =>
              e.name.toLowerCase() == (element.mode?.toString() ?? "").toLowerCase());
        } catch (e) {
          // Skip invalid modes
        }
        if (mode != null && mode.name == currentDriveMode.name) {
          maxRange = element.maxRange ?? 0;
          correction = element.rangeCorrection ?? 0;
        }
      }
    });
    return (soc / 100) * maxRange - correction;
  }
}

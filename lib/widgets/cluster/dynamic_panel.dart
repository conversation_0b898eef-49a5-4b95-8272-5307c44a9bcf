import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/widgets/cluster/dynamicPanel/amp_meter.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../common/dimensions.dart';
import '../../constant/connected_vehicle_status.dart';
import '../../models/vehicle_info.dart';
import '../../utils/calculate_data.dart';
import 'dynamicPanel/battery_meter.dart';

class ClusterDynamicPanel extends StatefulWidget {
  final bool isPortrait;
  final VehicleInfo vehicleInfo;
  const ClusterDynamicPanel(
      {super.key, required this.isPortrait, required this.vehicleInfo});

  @override
  State<ClusterDynamicPanel> createState() => _ClusterDynamicPanelState();
}

class _ClusterDynamicPanelState extends State<ClusterDynamicPanel> {
  late ConnectedVehicleStatus batteryConnectionStatus;
  int activeIndex = 0;

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    double size = widget.isPortrait ? dimensions.width : dimensions.height;
    batteryConnectionStatus = getBattryStatus(
        widget.vehicleInfo.batteryCharging ?? false,
        widget.vehicleInfo.batteryConnected ?? true);

    List widgets = [
      ClusterBatteryMeter(
        isPortrait: widget.isPortrait,
        volts: widget.vehicleInfo.batteryVoltage ?? 0,
        battery: widget.vehicleInfo.charge ?? 0,
        temp: widget.vehicleInfo.batteryTemperature ?? 0,
        tempUnit: widget.vehicleInfo.temperatureUnit ?? "",
        recordedAt: widget.vehicleInfo.aiVinRecordedTime ?? 0,
        batteryConnectionStatus: batteryConnectionStatus,
      ),
      ClusterAmpMeter(
          isPortrait: widget.isPortrait, vehicleInfo: widget.vehicleInfo)
    ];

    return SizedBox(
      width: 250 / 360 * size,
      child: Column(
        children: [
          CarouselSlider(
            items: [...widgets],
            options: CarouselOptions(
              autoPlay: false,
              height: 244 / 360 * size,
              viewportFraction: 1,
              onPageChanged: (index, reason) =>
                  setState(() => activeIndex = index),
            ),
          ),
          AnimatedSmoothIndicator(
            activeIndex: activeIndex,
            count: widgets.length,
            effect: ExpandingDotsEffect(
                dotHeight: 4 / 360 * size,
                dotWidth: 8 / 360 * size,
                expansionFactor: 2,
                spacing: 4 / 360 * size,
                dotColor: colorWhiteLite,
                activeDotColor: colorWhiteLite),
          )
        ],
      ),
    );
  }
}

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:location/location.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/streams/vehicle_data.dart';
import 'package:nds_app/widgets/cluster/trip_meter.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class ClusterSpeedometer extends StatefulWidget {
  final bool isPortrait;
  final double speed;
  final int odoInKm;
  final bool diMotion;
  const ClusterSpeedometer(
      {super.key,
      required this.isPortrait,
      required this.odoInKm,
      required this.speed,
      required this.diMotion});

  @override
  State<ClusterSpeedometer> createState() => _ClusterSpeedometerState();
}

class _ClusterSpeedometerState extends State<ClusterSpeedometer> {
  int i = 1;
  late double speed;
  late bool diMotion;
  StreamSubscription<LocationData>? subscription;
  late Location location;
  VehicleDataStream stream = VehicleDataStream();
  @override
  void initState() {
    diMotion = widget.diMotion;
    speed = widget.speed;
    WakelockPlus.enable();
    isGPSModeEnabled = true;
    if (isGPSModeEnabled) {
      speed = 0;
      location = Location();
      location.changeSettings(
          accuracy: LocationAccuracy.navigation, interval: 500);
      subscription =
          location.onLocationChanged.listen((LocationData currentLocation) {
        if ((currentLocation.speedAccuracy ?? 0) > 0 &&
            (currentLocation.speed ?? 0) > 0) {
          setState(() {
            speed = (currentLocation.speed ?? 0) * 3.6;
          });
          if (diMotion == false) {
            speed = 0;
          }
        }
      });
    }
    super.initState();
  }

  Widget getSpeedoMeterWidget(double size) {
    Widget speedometer = StreamBuilder(
      stream: stream.vehicleInfo,
      builder: (context, snapshot) {
        if (snapshot.data != null) {
          VehicleInfo vehicleInfo = snapshot.data!;
          diMotion = vehicleInfo.diMotion ?? false;
          if (diMotion == false) {
            speed = 0;
          }
        }

        return SfRadialGauge(
          axes: <RadialAxis>[
            RadialAxis(
              interval: 20,
              startAngle: 150,
              endAngle: 30,
              maximum: 160,
              showTicks: true,
              showFirstLabel: true,
              showLastLabel: true,
              majorTickStyle: const MajorTickStyle(color: colorWhite),
              axisLineStyle: const AxisLineStyle(
                thickness: 25,
                color: speedometerEmptyColor,
              ),
              axisLabelStyle: GaugeTextStyle(
                  color: colorWhite,
                  fontSize: 14 / 360 * size,
                  fontFamily: "Poppins",
                  fontWeight: FontWeight.w400),
              pointers: <GaugePointer>[
                RangePointer(
                  width: 25,
                  value: (speed + 0.12 * speed),
                  color: speedometerColor,
                  enableAnimation: true,
                )
              ],
            )
          ],
        );
      },
    );
    if (!isGPSModeEnabled) {
      speedometer = StreamBuilder(
        stream: stream.vehicleInfo,
        builder: (context, snapshot) {
          if (snapshot.data != null) {
            VehicleInfo vehicleInfo = snapshot.data!;
            diMotion = vehicleInfo.diMotion ?? false;
            if (diMotion == false) {
              speed = 0;
            } else {
              speed = vehicleInfo.speed ?? 0;
            }
          }
          return SfRadialGauge(
            axes: <RadialAxis>[
              RadialAxis(
                interval: 20,
                startAngle: 150,
                endAngle: 30,
                maximum: 160,
                showTicks: true,
                showFirstLabel: true,
                showLastLabel: true,
                majorTickStyle: const MajorTickStyle(color: colorWhite),
                axisLineStyle: const AxisLineStyle(
                  thickness: 25,
                  color: speedometerEmptyColor,
                ),
                axisLabelStyle: GaugeTextStyle(
                    color: colorWhite,
                    fontSize: 14 / 360 * size,
                    fontFamily: "Poppins",
                    fontWeight: FontWeight.w400),
                pointers: <GaugePointer>[
                  RangePointer(
                    width: 25,
                    value: speed + 0.12 * speed,
                    color: speedometerColor,
                    enableAnimation: true,
                  )
                ],
              )
            ],
          );
        },
      );
    }
    return speedometer;
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    double size = widget.isPortrait ? dimensions.width : dimensions.height;
    return SizedBox(
      width: 250 / 360 * size,
      height: (widget.isPortrait ? 240 : 260) / 360 * size,
      child: Stack(children: [
        getSpeedoMeterWidget(size),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: 60 / 360 * size,
            ),
            isGPSModeEnabled
                ? StreamBuilder(
                    stream: stream.vehicleInfo,
                    builder: (context, snapshot) {
                      if (snapshot.data != null) {
                        VehicleInfo vehicleInfo = snapshot.data!;
                        diMotion = vehicleInfo.diMotion ?? false;
                        if (diMotion == false) {
                          speed = 0;
                        }
                      }
                      return Text(
                        (speed + speed * 0.12).floor().toString(),
                        style: TextStyle(
                          height: 0.3,
                          color: Colors.white,
                          fontSize: 64 / 360 * size,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                        ),
                      );
                    })
                : StreamBuilder(
                    stream: stream.vehicleInfo,
                    builder: (context, snapshot) {
                      if (snapshot.data != null) {
                        VehicleInfo vehicleInfo = snapshot.data!;
                        diMotion = vehicleInfo.diMotion ?? false;
                        if (diMotion == false) {
                          speed = 0;
                        } else {
                          speed = vehicleInfo.speed ?? 0;
                        }
                      }
                      return Text(
                        (speed + speed * 0.12).floor().toString(),
                        style: TextStyle(
                          height: 0.3,
                          color: Colors.white,
                          fontSize: 64 / 360 * size,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                        ),
                      );
                    },
                  ),
            SizedBox(
              height: 20 / 360 * size,
            ),
            Text(
              clusterScreenText["kmUnit"]!,
              style: poppinsTextStyle(
                  14 / 360 * size, colorWhite, FontWeight.w500),
            ),
            SizedBox(
              height: 7 / 360 * size,
            ),
            Text(
              clusterScreenText["odo"]!
                  .replaceAll("@km", widget.odoInKm.toString()),
              style: poppinsTextStyle(
                  12 / 360 * size, colorWhite, FontWeight.w500),
            ),
            SizedBox(
              height: 8 / 360 * size,
            ),
            TripMeter(size: size, value: widget.odoInKm),
          ],
        )
      ]),
    );
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    subscription?.cancel();
    super.dispose();
  }
}

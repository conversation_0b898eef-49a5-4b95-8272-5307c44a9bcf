import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/text_styles.dart';
import '../../common/constant.dart';

class ClusterTitleRow extends StatefulWidget {
  final bool isPortrait;
  final String userName;
  const ClusterTitleRow(
      {super.key, required this.isPortrait, required this.userName});

  @override
  State<ClusterTitleRow> createState() => _ClusterTitleRowState();
}

class _ClusterTitleRowState extends State<ClusterTitleRow> {
  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    double size = widget.isPortrait ? dimensions.width : dimensions.height;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            InkWell(
              child: const Icon(
                Icons.arrow_back_ios,
                color: colorWhite,
              ),
              onTap: () {
                Navigator.of(context).pop();
              },
            ),
            //for lml
            Padding(
              padding: EdgeInsets.only(left: size * 8 / 360),
              child: Image.asset(
                isLapaUser ? splashScreenImages["lapa_logo_5"]! :
                clusterTitleRowCompanyLogo,
                height: size * 30 / 360,
                // width: size * 50 / 360,
              ),
            )
//for others
            // Padding(
            //   padding:
            //       EdgeInsets.only(left: size * 8 / 360, right: size * 8 / 360),
            //   child: Image.asset(
            //     clusterScreenImages['bike_logo']!,
            //   ),
            // ),
            // Text(
            //   clusterScreenText["lml"]!,
            //   style:
            //       poppinsTextStyle(16 / 360 * size, colorWhite, FontWeight.w600),
            // ),
          ],
        ),
        Row(
          children: [
            Padding(
              padding: EdgeInsets.only(right: size * 8 / 360),
              child: CircleAvatar(
                radius: size * 15 / 360,
                backgroundImage: AssetImage(
                  clusterScreenImages['bike_logo']!,
                ),
                backgroundColor: Colors.black,
              ),
            ),
            Text(
              widget.userName,
              style: poppinsTextStyle(
                  16 / 360 * size, colorWhite, FontWeight.w600),
            ),
          ],
        )
      ],
    );
  }
}

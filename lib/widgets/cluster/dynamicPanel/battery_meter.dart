import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/connected_vehicle_status.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

class ClusterBatteryMeter extends StatefulWidget {
  final bool isPortrait;
  final int battery;
  final int temp;
  final int volts;
  final String tempUnit;
  final int recordedAt;
  final ConnectedVehicleStatus batteryConnectionStatus;
  const ClusterBatteryMeter({
    super.key,
    required this.isPortrait,
    required this.battery,
    required this.temp,
    required this.volts,
    required this.tempUnit,
    required this.recordedAt,
    required this.batteryConnectionStatus,
  });

  @override
  State<ClusterBatteryMeter> createState() => _ClusterBatteryMeterState();
}

class _ClusterBatteryMeterState extends State<ClusterBatteryMeter> {
  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    double size = widget.isPortrait ? dimensions.width : dimensions.height;

    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(widget.recordedAt);
    String? aiVinRecordedAt = widget.recordedAt == 0
        ? ''
        : DateFormat('dd-MM-yyyy').add_jms().format(dateTime);
    bool isBatteryRemoved =
        widget.batteryConnectionStatus == ConnectedVehicleStatus.batteryRemoved;
    return SizedBox(
      width: 250 / 360 * size,
      height: 250 / 360 * size,
      child: Stack(children: [
        SfRadialGauge(
          axes: <RadialAxis>[
            RadialAxis(
                showTicks: false,
                showLabels: false,
                axisLineStyle: const AxisLineStyle(
                    thickness: 20,
                    color: colorGrey600,
                    cornerStyle: CornerStyle.bothCurve),
                pointers: <GaugePointer>[
                  RangePointer(
                    width: 20,
                    cornerStyle: CornerStyle.bothCurve,
                    value: widget.batteryConnectionStatus ==
                            ConnectedVehicleStatus.batteryRemoved
                        ? 0
                        : (widget.battery).toDouble(),
                    enableAnimation: true,
                    gradient: const SweepGradient(colors: <Color>[
                      batteryGradientColor1,
                      batteryGradientColor2
                    ], stops: <double>[
                      0.0,
                      1
                    ]),
                  )
                ])
          ],
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: isBatteryRemoved
              ? [
                  SizedBox(
                    height: 28 / 896 * size,
                  ),
                  SizedBox(
                    width: 48 / 360 * size,
                    height: 48 / 360 * size,
                    child: Image.asset(clusterScreenImages["no_battery_icon"]!),
                  ),
                  SizedBox(
                    height: 25 / 896 * size,
                  ),
                  Text(
                    widget.batteryConnectionStatus.name,
                    style: poppinsTextStyle(
                        14 / 360 * size, colorRedRemoved, FontWeight.w500),
                  ),
                  SizedBox(
                    height: 60 / 896 * size,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                          width: 15 / 360 * size,
                          height: 18 / 360 * size,
                          child: Image.asset(
                              clusterScreenImages["no_voltage_icon"]!)),
                      SizedBox(
                        width: 6 / 360 * size,
                      ),
                      Text(
                        clusterScreenText["volts"]!.replaceAll("@volts", "0"),
                        style: poppinsTextStyle(
                            14 / 360 * size, colorRedRemoved, FontWeight.w500),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 55 / 896 * size,
                  ),
                  Visibility(
                    visible: widget.recordedAt != 0,
                    child: Text(
                      aiVinRecordedAt,
                      style: poppinsTextStyle(
                          12 / 414 * size, colorGrey400, FontWeight.w300),
                    ),
                  ),
                ]
              : [
                  SizedBox(
                    height: 28 / 896 * size,
                  ),
                  SizedBox(
                    width: 24 / 360 * size,
                    height: 24 / 360 * size,
                    child: Image.asset(clusterScreenImages["battery_icon"]!),
                  ),
                  Text(
                    "${widget.battery.floor()}%",
                    style: poppinsTextStyle(
                        64 / 360 * size, colorWhite, FontWeight.w500),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                          width: 16 / 360 * size,
                          height: 16 / 360 * size,
                          child: Image.asset(
                              clusterScreenImages["voltage_icon"]!)),
                      Text(
                        clusterScreenText["volts"]!
                            .replaceAll("@volts", widget.volts.toString()),
                        style: poppinsTextStyle(
                            14 / 360 * size, colorWhite, FontWeight.w500),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 25 / 896 * size,
                  ),
                  Visibility(
                    visible: widget.recordedAt != 0,
                    child: Text(
                      aiVinRecordedAt,
                      style: poppinsTextStyle(
                          12 / 414 * size, colorGrey400, FontWeight.w300),
                    ),
                  ),
                ],
        )
      ]),
    );
  }
}

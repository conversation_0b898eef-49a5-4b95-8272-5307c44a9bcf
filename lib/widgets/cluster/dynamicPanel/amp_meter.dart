import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

import '../../../common/dimensions.dart';
import '../../../common/strings.dart';
import '../../../utils/circular_arc.dart';
import 'dart:math' as math;

class ClusterAmpMeter extends StatefulWidget {
  final bool isPortrait;
  final VehicleInfo vehicleInfo;

  const ClusterAmpMeter(
      {super.key, required this.isPortrait, required this.vehicleInfo});

  @override
  State<ClusterAmpMeter> createState() => _ClusterAmpMeterState();
}

class _ClusterAmpMeterState extends State<ClusterAmpMeter> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    double size = widget.isPortrait ? dimensions.width : dimensions.height;
    widget.vehicleInfo.motorDcCurrents =
        widget.vehicleInfo.motorDcCurrents ?? [];

    return SizedBox(
      width: 250 / 360 * size,
      height: 250 / 360 * size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SfRadialGauge(
            axes: <RadialAxis>[
              RadialAxis(
                  showTicks: false,
                  showLabels: false,
                  startAngle: 95,
                  endAngle: 27,
                  radiusFactor: 0.9,
                  axisLineStyle: AxisLineStyle(
                      color: colorGrey600,
                      thickness: 7 / 360 * size,
                      dashArray: const <double>[8, 3])),
              RadialAxis(
                  showTicks: false,
                  showLabels: false,
                  startAngle: 95,
                  endAngle: getMeterValue(widget.vehicleInfo.current ?? 0),
                  radiusFactor: 0.9,
                  axisLineStyle: AxisLineStyle(
                      color: colorWhiteLite,
                      thickness: 7 / 360 * size,
                      dashArray: const <double>[8, 3]))
            ],
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                height: 41 / 360 * size,
              ),
              Text(
                clusterScreenText["amps"]!,
                style: poppinsTextStyle(
                    23 / 360 * size, colorWhiteLite, FontWeight.w500),
              ),
              Text(
                (widget.vehicleInfo.current?.toStringAsFixed(1) ?? "NA")
                    .toString(),
                style: poppinsTextStyle(
                    60 / 360 * size,
                    widget.vehicleInfo.current == null
                        ? colorRed
                        : colorWhiteLite,
                    FontWeight.w500),
              ),
            ],
          ),
          Visibility(
            visible: widget.vehicleInfo.motorDcCurrents!.isNotEmpty,
            child: Positioned(
              left: 105 / 360 * size,
              top: 137 / 360 * size,
              child: SizedBox(
                height: 90 / 360 * size,
                width: 100 / 360 * size,
                child: Stack(
                  children: [
                    SfCartesianChart(
                      plotAreaBorderColor: colorWhiteLite,
                      plotAreaBorderWidth: 0,
                      borderWidth: 0,
                      primaryXAxis: NumericAxis(
                        interval: 1,
                        isVisible: false,
                      ),
                      primaryYAxis: NumericAxis(
                        majorGridLines: const MajorGridLines(
                          width: 1,
                          color: Colors.transparent,
                        ),
                        isVisible: true,
                        minimum: 0,
                        maximum: 100,
                        interval: 20,
                        name: "yAxis",
                      ),
                      series: _getSplineTypesSeries(),
                    ),
                    SfCartesianChart(
                      plotAreaBorderColor: colorWhiteLite,
                      plotAreaBorderWidth: 0,
                      borderWidth: 0,
                      primaryXAxis: NumericAxis(
                        interval: 1,
                        isVisible: false,
                      ),
                      primaryYAxis: NumericAxis(
                        majorGridLines: const MajorGridLines(
                          width: 1,
                          color: Colors.transparent,
                        ),
                        isVisible: true,
                        minimum: 0,
                        maximum: 100,
                        interval: 20,
                        name: "yAxis",
                      ),
                      series: _getAvgLineFromSplineTypesSeries(),
                    ),
                  ],
                ),
              ),
            ),
          ),
          CircularArc(
            color: colorArcAmpMeter,
            diameter: 185 / 360 * size,
            startAngle: math.pi / 7,
            sweepAngle: -1.35 * math.pi,
          )
        ],
      ),
    );
  }

  double getMeterValue(double current) {
    double value;
    if (current > 134) {
      value = (current - 134) * 1.946667;
    } else {
      value = -265 + current * 1.9667;
    }
    if (current >= 150) {
      value = 27;
    }
    return value;
  }

  List<SplineSeries<double, double>> _getAvgLineFromSplineTypesSeries() {
    return <SplineSeries<double, double>>[
      SplineSeries<double, double>(
        name: 'Spline',
        splineType: SplineType.monotonic,
        color: colorOrangeAvgLine,
        width: 1,
        dataSource: widget.vehicleInfo.motorDcCurrents ?? [],
        xValueMapper: (double e, index) {
          return index.toDouble();
        },
        yValueMapper: (double e, index) {
          return widget.vehicleInfo.current ?? 0;
        },
      ),
    ];
  }

  List<SplineSeries<double, double>> _getSplineTypesSeries() {
    return <SplineSeries<double, double>>[
      SplineSeries<double, double>(
        name: 'Spline',
        splineType: SplineType.monotonic,
        color: colorWhite,
        width: 1,
        dataSource: widget.vehicleInfo.motorDcCurrents ?? [],
        xValueMapper: (double e, index) {
          return index.toDouble();
        },
        yValueMapper: (double e, index) => e,
      ),
    ];
  }
}

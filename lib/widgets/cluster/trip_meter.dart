import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../common/colors.dart';
import '../../common/constant.dart';
import '../../common/image_urls.dart';
import '../../common/shared_preferences_keys.dart';
import '../../streams/vehicle_data.dart';

class TripMeter extends StatefulWidget {
  final double size;
  final int value;
  const TripMeter({super.key, required this.size, required this.value});

  @override
  State<TripMeter> createState() => _TripMeterState();
}

class _TripMeterState extends State<TripMeter> {
  VehicleDataStream stream = VehicleDataStream();

  late bool isTripStopped;
  late int? tripStartOdo;
  late List<int> odoDigits;
  late int currentOdo;

  @override
  void initState() {
    odoDigits = List.empty();
    isTripStopped = true;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double size = widget.size;

    return FutureBuilder(
      future: loadData(),
      builder: (context, snapshot) {
        Widget widget = Center(
          child:  Image.asset(
            isTwoWheels ?
            loaderGifImages['2Wheels']! : loaderGifImages['3Wheels']!,
          ),
        );
        if (ConnectionState.done == snapshot.connectionState) {}
        widget = Container(
          height: 20 / 360 * size,
          color: Colors.transparent,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ListView.builder(
                shrinkWrap: true,
                itemCount: odoDigits.length,
                scrollDirection: Axis.horizontal,
                itemBuilder: (context, index) {
                  return Row(
                    children: [
                      Container(
                        height: 16 / 360 * size,
                        width: 3 / 360 * size,
                        color: Colors.transparent,
                      ),
                      Container(
                        height: 16 / 360 * size,
                        width: 12 / 360 * size,
                        decoration: BoxDecoration(
                            color: colorWhite,
                            borderRadius: BorderRadius.all(
                                Radius.circular(2 / 360 * size))),
                        child: Center(
                          child: Text(
                            odoDigits[index].toString(),
                            style: TextStyle(
                              height: 1.4,
                              color: colorGrey900,
                              fontSize: 12 / 360 * size,
                              fontFamily: "Poppins",
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
              SizedBox(
                width: 6 / 360 * size,
              ),
              InkResponse(
                radius: 15 / 360 * size,
                onTap: () async {
                  SharedPreferences pref =
                      await SharedPreferences.getInstance();
                  if (isTripStopped) {
                    isTripStopped = false;
                    pref.setInt(tripMeterStartValueKey, currentOdo);
                  } else {
                    isTripStopped = true;
                    pref.setInt(tripMeterStartValueKey,
                        currentOdo - (tripStartOdo ?? currentOdo));
                  }

                  pref.setBool(isTripMeterStoppedKey, isTripStopped);
                  setState(() {});
                },
                child: SizedBox(
                    height: 20 / 360 * size,
                    width: 20 / 360 * size,
                    child: isTripStopped
                        ? Image.asset(
                            clusterScreenImages['red_trip_meter_button']!)
                        : Image.asset(
                            clusterScreenImages['green_trip_meter_button']!)),
              )
            ],
          ),
        );
        return widget;
      },
    );
  }

  List<int> getTripMeterDigitListByOdometerReading(int odo) {
    List<int> list = [0, 0, 0, 0, 0];
    int endIdx = list.length - 1;

    while (odo / 10 != 0) {
      list[endIdx] = odo % 10;
      odo = odo ~/ 10;
      endIdx--;
    }
    return list;
  }

  loadData() async {
    currentOdo = widget.value;
    SharedPreferences pref = await SharedPreferences.getInstance();
    isTripStopped = pref.getBool(isTripMeterStoppedKey) ?? true;
    tripStartOdo = pref.getInt(tripMeterStartValueKey);
    odoDigits = getTripMeterDigitListByOdometerReading(isTripStopped == true
        ? tripStartOdo ?? 0
        : (currentOdo - (tripStartOdo ?? currentOdo)));
  }
}

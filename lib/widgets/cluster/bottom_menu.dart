import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';

class ClusterBottomMenu extends StatefulWidget {
  final bool isPortrait;
  const ClusterBottomMenu({super.key, required this.isPortrait});

  @override
  State<ClusterBottomMenu> createState() => _ClusterBottomMenuState();
}

class _ClusterBottomMenuState extends State<ClusterBottomMenu> {
  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    double size = widget.isPortrait ? dimensions.width : dimensions.height;
    return Container(
      decoration: const BoxDecoration(
          color: colorGrey700,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.0), topRight: Radius.circular(20.0))),
      child: Padding(
        padding: EdgeInsets.only(
            top: (8 / 360 * size),
            bottom: (8 / 360 * size),
            left: (24 / 360 * size),
            right: (24 / 360 * size)),
        child: Row(
          mainAxisSize: widget.isPortrait ? MainAxisSize.max : MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            SizedBox(
                width: 24 / 414 * size,
                child: Image.asset(clusterScreenImages["bottom_item_1"]!)),
            SizedBox(width: (widget.isPortrait ? 16 : 24 / 360 * size)),
            SizedBox(
                width: 24 / 414 * size,
                child: Image.asset(clusterScreenImages["bottom_item_2"]!)),
            SizedBox(width: (widget.isPortrait ? 16 : 24 / 360 * size)),
            SizedBox(
                width: 24 / 414 * size,
                child: Image.asset(clusterScreenImages["bottom_item_3"]!)),
            SizedBox(width: (widget.isPortrait ? 16 : 24 / 360 * size)),
            SizedBox(
                width: 24 / 414 * size,
                child: Image.asset(clusterScreenImages["bottom_item_4"]!)),
            SizedBox(width: (widget.isPortrait ? 16 : 24 / 360 * size)),
            SizedBox(
                width: 24 / 414 * size,
                child: Image.asset(clusterScreenImages["bottom_item_5"]!)),
            SizedBox(width: (widget.isPortrait ? 16 : 24 / 360 * size)),
            SizedBox(
                width: 24 / 414 * size,
                child: Image.asset(clusterScreenImages["bottom_item_6"]!)),
            SizedBox(width: (widget.isPortrait ? 16 : 24 / 360 * size)),
            SizedBox(
                width: 24 / 414 * size,
                child: Image.asset(clusterScreenImages["bottom_item_7"]!))
          ],
        ),
      ),
    );
  }
}

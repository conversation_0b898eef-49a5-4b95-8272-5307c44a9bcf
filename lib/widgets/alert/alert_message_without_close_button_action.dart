import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/alert.dart';

Future<bool> getAlertMessageWithoutCloseButtonAction(
    BuildContext context, Alert alert) async {
  Dimensions dimensions = Dimensions(context);

  double height = dimensions.height;
  double width = dimensions.width;

  bool isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;

  if (!isPortrait) {
    double tempLength = width;
    width = height;
    height = tempLength;
  }
  return ((await showDialog(
          context: context,
          builder: (context) => PopScope(
                canPop: false,
                onPopInvokedWithResult: (didPop, result) {},
                child: Material(
                    type: MaterialType.transparency,
                    child: Stack(
                      children: [
                        InkWell(
                          onTap: () {
                            isTempAlertMessageExist = false;
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            color: colorBackgroundWithLessOpacity,
                          ),
                        ),
                        Container(
                          height: 100 / 896 * height,
                          alignment: Alignment.centerLeft,
                          padding: EdgeInsets.symmetric(
                            horizontal: 8 / 414 * width,
                            vertical: 8 / 414 * width,
                          ),
                          margin: EdgeInsets.only(
                            left: 24 / 414 * width,
                            right: 24 / 414 * width,
                            top: 45 / 896 * height,
                          ),
                          decoration: BoxDecoration(
                              color: colorGrey200,
                              borderRadius: BorderRadius.all(
                                  Radius.circular(8 / 414 * width))),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                  height: 14 / 414 * height,
                                  width: 14 / 414 * width,
                                  child:
                                      Image.asset(alertImages['alert_icon']!)),
                              SizedBox(
                                width: 4 / 414 * width,
                              ),
                              Flexible(
                                child: Padding(
                                  padding: EdgeInsets.only(
                                      top: 4 / 896 * height,
                                      bottom: 4 / 896 * height),
                                  child: Text(
                                    alert.message,
                                    style: poppinsTextStyle(14 / 414 * width,
                                        colorGrey500, FontWeight.w400),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    )),
              )) ??
      false));
}

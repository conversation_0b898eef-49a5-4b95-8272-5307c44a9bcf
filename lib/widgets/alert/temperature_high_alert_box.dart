import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/alert.dart';
import 'package:nds_app/models/app_alert.dart';

Future<bool> getHighTempratureAlertBox(
    BuildContext context, Alert alert, TempAlert tempAlert) async {
  return ((await showDialog(
          context: context,
          builder: (context) => PopScope(
                canPop: false,
                onPopInvokedWithResult: (didPop, result) {},
                child: OrientationBuilder(
                  builder: (context, orientation) {
                    Dimensions dimensions = Dimensions(context);
                    double currentTemp = tempAlert.currentTemp;
                    double baseTemp = tempAlert.baseTemp ?? 0;
                    double height = dimensions.height;
                    double width = dimensions.width;
                    bool isPortrait = MediaQuery.of(context).orientation ==
                        Orientation.portrait;
                    return Material(
                        type: MaterialType.transparency,
                        child: Align(
                          alignment: Alignment.topCenter,
                          child: Stack(
                            children: [
                              Container(
                                color: colorBackgroundWithLessOpacity,
                              ),
                              Center(
                                child: Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal:
                                            (isPortrait ? 20 / 414 : 20 / 896) *
                                                width),
                                    height:
                                        (isPortrait ? 500 / 896 : 290 / 414) *
                                            height,
                                    width:
                                        (isPortrait ? 354 / 414 : 300 / 896) *
                                            width,
                                    decoration: BoxDecoration(
                                        color: colorGrey25,
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(8 / 414 * width))),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Flexible(
                                          child: SizedBox(
                                            height: (isPortrait
                                                    ? 45 / 896
                                                    : 31 / 414) *
                                                height,
                                          ),
                                        ),
                                        Flexible(
                                          child: SizedBox(
                                              height: (isPortrait
                                                      ? 132 / 414
                                                      : 63 / 896) *
                                                  width,
                                              width: (isPortrait
                                                      ? 132 / 414
                                                      : 63 / 896) *
                                                  width,
                                              child: Image.asset(
                                                  alertImages['alert_icon']!)),
                                        ),
                                        Flexible(
                                          child: SizedBox(
                                            height: (isPortrait
                                                    ? 32 / 896
                                                    : 8 / 414) *
                                                height,
                                          ),
                                        ),
                                        Text(
                                          alertTempMessages['text1']!,
                                          overflow: TextOverflow.ellipsis,
                                          style: poppinsTextStyle(
                                              (isPortrait
                                                      ? 20 / 414
                                                      : 16 / 896) *
                                                  width,
                                              colorGrey600,
                                              FontWeight.w800),
                                        ),
                                        SizedBox(
                                          height: 8 / 896 * height,
                                        ),
                                        SizedBox(
                                          width: 300 / 414 * dimensions.width,
                                          child: FittedBox(
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text(
                                                  alert.message.replaceFirst(
                                                      '@value', '$currentTemp'),
                                                  style: poppinsTextStyle(
                                                      (isPortrait
                                                              ? 30 / 414
                                                              : 16 / 896) *
                                                          width,
                                                      colorRedError,
                                                      FontWeight.w800),
                                                ),
                                                SizedBox(
                                                  width: 8 / 414 * width,
                                                ),
                                                SizedBox(
                                                  width: (isPortrait
                                                          ? 15 / 414
                                                          : 10 / 896) *
                                                      width,
                                                  height: (isPortrait
                                                          ? 30 / 896
                                                          : 30 / 414) *
                                                      height,
                                                  child: Image.asset(
                                                      alertImages[
                                                          'thermometer_red']!),
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          height: 18 / 896 * height,
                                        ),
                                        FittedBox(
                                          fit: BoxFit.fitHeight,
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Text(
                                                alertTempMessages['text2']!,
                                                style: poppinsTextStyle(
                                                    (isPortrait
                                                            ? 18 / 414
                                                            : 14 / 896) *
                                                        width,
                                                    colorGrey400,
                                                    FontWeight.w500),
                                              ),
                                              Text(
                                                alertTempMessages['text3']!,
                                                style: poppinsTextStyle(
                                                    (isPortrait
                                                            ? 18 / 414
                                                            : 14 / 896) *
                                                        width,
                                                    colorRedError,
                                                    FontWeight.w600),
                                              ),
                                              Text(
                                                alertTempMessages['text4']!,
                                                style: poppinsTextStyle(
                                                    (isPortrait
                                                            ? 18 / 414
                                                            : 14 / 896) *
                                                        width,
                                                    colorGrey400,
                                                    FontWeight.w500),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Text(
                                          alertTempMessages['text5']!
                                              .replaceAll(
                                                  '@value', '$baseTemp'),
                                          textAlign: TextAlign.center,
                                          style: poppinsTextStyle(
                                              (isPortrait
                                                      ? 18 / 414
                                                      : 14 / 896) *
                                                  width,
                                              colorGrey400,
                                              FontWeight.w500),
                                        ),
                                      ],
                                    )),
                              ),
                            ],
                          ),
                        ));
                  },
                ),
              )) ??
      false));
}

import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/action.dart';

Future<bool> getAlertMessageWithCloseButtonAction(
    BuildContext context, String message) async {
  Dimensions dimensions = Dimensions(context);
  return ((await showDialog(
          context: context,
          builder: (context) => PopScope(
                canPop: false,
                onPopInvokedWithResult: (didPop, result) {},
                child: Material(
                    type: MaterialType.transparency,
                    child: Stack(
                      children: [
                        Container(
                          color: colorBackgroundWithLessOpacity,
                        ),
                        Container(
                          alignment: Alignment.centerLeft,
                          height: 76 / 896 * dimensions.height,
                          width: 406 / 414 * dimensions.width,
                          padding: EdgeInsets.symmetric(
                            horizontal: 24 / 414 * dimensions.width,
                          ),
                          margin: EdgeInsets.only(
                              left: 4 / 414 * dimensions.width,
                              right: 4 / 414 * dimensions.width,
                              top: 45 / 896 * dimensions.height,
                              bottom: 739 / 896 * dimensions.height),
                          decoration: BoxDecoration(
                              color: colorGrey800,
                              borderRadius: BorderRadius.all(
                                  Radius.circular(8 / 414 * dimensions.width))),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                message,
                                style: poppinsTextStyle(
                                    14 / 414 * dimensions.width,
                                    colorGrey25,
                                    FontWeight.w500),
                              ),
                              InkWell(
                                onTap: () async {
                                  int statusCode = await DialogAction.disconnect
                                      .action(context: context);
                                  if (statusCode == 200) {
                                    isVehicleInfoAlertMessageExist = false;
                                  }
                                },
                                child: const Icon(
                                  Icons.close,
                                  color: colorGrey25,
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    )),
              )) ??
      false));
}

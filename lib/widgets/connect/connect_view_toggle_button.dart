import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connect/toggle/connect_vehicle_toogle_bloc.dart';
import 'package:nds_app/blocs/connect/toggle/connect_vehicle_toogle_state.dart';
import 'package:nds_app/blocs/vehicle/toggle/vehicle_toogle_event.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/utils/concave_decoration.dart';

class ConnectViewToggleButton extends StatefulWidget {
  final Color color;
  final ColorType colorType;

  const ConnectViewToggleButton(
      {super.key, required this.color, required this.colorType});

  @override
  State<ConnectViewToggleButton> createState() =>
      _ConnectViewToggleButtonState();
}

class _ConnectViewToggleButtonState extends State<ConnectViewToggleButton> {
  late Color color;
  late ColorType colorType;

  @override
  void initState() {
    color = widget.color;
    colorType = widget.colorType;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Column(
      children: [
        Container(
          height: 54 / 896 * dimensions.height,
          width: 376 / 414 * dimensions.width,
          decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              /*color: currentVehicleStatus == VehicleStatus.connected
                  ? (colorType == ColorType.light
                      ? Theme.of(context).highlightColor
                      : color)
                  : loginThemeColor,*/
              border: Border.all(color: Theme.of(context).cardColor, width: 2),
              borderRadius: BorderRadius.circular(8 / 414 * dimensions.width),
              boxShadow: [
                BoxShadow(
                    color: colorBlack.withOpacity(0.25),
                    offset: const Offset(1, 3),
                    blurRadius: 3,
                    spreadRadius: 1),
                BoxShadow(
                    color: colorWhite.withOpacity(0.25),
                    offset: const Offset(-1, -3),
                    blurRadius: 3,
                    spreadRadius: 1)
              ]),
          child:
              BlocBuilder<ConnectVehicleToogleBloc, ConnectVehicleToogleState>(
                  builder: (context, state) {
            return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      if (state.isSwitchRight) {
                        context
                            .read<ConnectVehicleToogleBloc>()
                            .add(SwitchEvent());
                      }
                    },
                    child: getToggleLabelContainer(
                      dimensions,
                      connectVehicleText['text3']!,
                      !state.isSwitchRight,
                      Icons.qr_code_scanner_sharp,
                    ),
                  ),
                  InkWell(
                      onTap: () {
                        if (!state.isSwitchRight) {
                          context
                              .read<ConnectVehicleToogleBloc>()
                              .add(SwitchEvent());
                        }
                      },
                      child: getToggleLabelContainer(
                          dimensions,
                          connectVehicleText['text4']!,
                          state.isSwitchRight,
                          Icons.password)),
                ]);
          }),
        ),
      ],
    );
  }

  SizedBox getToggleLabelContainer(
      Dimensions dimensions, String label, bool isSelected, IconData icon) {
    return SizedBox(
        height: 50 / 896 * dimensions.height,
        width: 184 / 414 * dimensions.width,
        child: isSelected
            ? Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    height: 50 / 896 * dimensions.height,
                    width: 184 / 414 * dimensions.width,
                    decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.circular(6 / 414 * dimensions.width),
                        color: currentVehicleStatus == VehicleStatus.connected
                            ? (colorType == ColorType.light
                                ? Theme.of(context).highlightColor
                                : color)
                            : loginThemeColor),
                  ),
                  Container(
                      height: 52 / 896 * dimensions.height,
                      width: 184 / 414 * dimensions.width,
                      decoration: ConcaveDecoration(
                        alignment: MyApp.of(context).getCurrentThemeMode() ==
                                ThemeMode.dark
                            ? Alignment.topRight
                            : Alignment.topLeft,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(6 / 414 * dimensions.width),
                        ),
                        depth: 3,
                        colors: [
                          colorWhite,
                          Colors.black.withOpacity(0.25),
                        ],
                        opacity: 0.7,
                      ),
                      child: getToggleLabelContainerUnselected(
                          dimensions, label, icon, colorType, true)),
                ],
              )
            : getToggleLabelContainerUnselected(
                dimensions, label, icon, colorType, false));
  }

  getToggleLabelContainerUnselected(Dimensions dimensions, String label,
      IconData icon, ColorType colorType, bool isEnabled) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
            height: 20 / 896 * dimensions.height,
            width: 20 / 414 * dimensions.width,
            child: Icon(icon,
                color: isEnabled || (colorType == ColorType.light && !isEnabled)
                    ? colorWhite
                    : Theme.of(context).textTheme.headlineMedium?.color)),
        SizedBox(
          width: 12 / 414 * dimensions.width,
        ),
        Align(
          alignment: Alignment.center,
          child: SizedBox(
            width: 90 / 414 * dimensions.width,
            child: Center(
              child: AutoSizeText(
                label,
                style: isEnabled || (colorType == ColorType.light && !isEnabled)
                    ? Theme.of(context)
                        .textTheme
                        .headlineMedium
                        ?.copyWith(color: colorWhite)
                    : Theme.of(context).textTheme.headlineMedium,
                maxLines: 1,
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

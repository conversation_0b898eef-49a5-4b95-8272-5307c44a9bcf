import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/streams/privacy_policy_and_terms_check_box_data.dart';
import 'package:nds_app/widgets/login/bottom_sheet_privacy_policy_and_toc.dart';

class CheckBoxPrivacyPolicyAndToc extends StatefulWidget {
  final List<UserActivitySetting> settings;
  final List<ActivityType> activities;
  const CheckBoxPrivacyPolicyAndToc({
    super.key,
    required this.activities,
    required this.settings,
  });

  @override
  State<CheckBoxPrivacyPolicyAndToc> createState() =>
      _CheckBoxPrivacyPolicyAndTocState();
}

class _CheckBoxPrivacyPolicyAndTocState
    extends State<CheckBoxPrivacyPolicyAndToc> {
  bool isPrivacyPolicyAndTocChecked = false;
  CheckBoxPrivacyPolicyAndTosStream stream =
      CheckBoxPrivacyPolicyAndTosStream();
  late UserActivitySetting privacyPolicySettings;
  late UserActivitySetting termsAndConditionsSettings;

  @override
  void initState() {
    for (UserActivitySetting element in widget.settings) {
      if (element.activityType == ActivityType.privacyPolicyAcceptance) {
        privacyPolicySettings = element;
      } else {
        termsAndConditionsSettings = element;
      }
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Row(
      children: [
        StreamBuilder<bool>(
            stream: stream.checkBox,
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                isPrivacyPolicyAndTocChecked = snapshot.data!;
              }
              return Checkbox(
                side: const BorderSide(color: colorGrey400, width: 2),
                overlayColor:
                    WidgetStateColor.resolveWith((states) => colorGrey200),
                checkColor: colorGrey25,
                activeColor: Colors.green,
                value: isPrivacyPolicyAndTocChecked,
                onChanged: (value) {
                  stream.updateCheckBox(
                      isPrivacyPolicyAndTocChecked == true ? false : true);
                },
              );
            }),
        SizedBox(
          width: 300 / 414 * dimensions.width,
          child: Column(
            children: [
              Visibility(
                visible: widget.activities
                        .contains(ActivityType.privacyPolicyAcceptance) &&
                    widget.activities
                        .contains(ActivityType.termsConditionsAcceptance),
                child: RichText(
                    text: TextSpan(children: [
                  TextSpan(
                    text: loginScreen['text7']!,
                    style: poppinsTextStyle(14 / 414 * dimensions.width,
                        colorGrey400, FontWeight.w400),
                  ),
                  TextSpan(
                      text: loginScreen['text8']!,
                      style: TextStyle(
                        fontFamily: 'Poppins',
                        color: colorGrey600,
                        fontSize: 14 / 414 * dimensions.width,
                        fontWeight: FontWeight.w400,
                        decoration: TextDecoration.underline,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () async {
                          getBotttomSheetPrivacyPolicyAndToc(
                              heading: loginScreen['text8'],
                              context: context,
                              userActivitySetting: privacyPolicySettings);
                        }),
                  TextSpan(
                    text: loginScreen['text9']!,
                    style: poppinsTextStyle(14 / 414 * dimensions.width,
                        colorGrey400, FontWeight.w400),
                  ),
                  TextSpan(
                      text: loginScreen['text10']!,
                      style: TextStyle(
                        color: colorGrey600,
                        fontFamily: 'Poppins',
                        fontSize: 14 / 414 * dimensions.width,
                        fontWeight: FontWeight.w400,
                        decoration: TextDecoration.underline,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () async {
                          getBotttomSheetPrivacyPolicyAndToc(
                              heading: loginScreen['text10'],
                              context: context,
                              userActivitySetting: termsAndConditionsSettings);
                        }),
                  TextSpan(
                    text: loginScreen['text11']!,
                    style: poppinsTextStyle(14 / 414 * dimensions.width,
                        colorGrey400, FontWeight.w400),
                  ),
                ])),
              ),
              Visibility(
                visible: widget.activities
                        .contains(ActivityType.privacyPolicyAcceptance) &&
                    !widget.activities
                        .contains(ActivityType.termsConditionsAcceptance),
                child: RichText(
                    text: TextSpan(children: [
                  TextSpan(
                    text: loginScreen['text7']!,
                    style: poppinsTextStyle(14 / 414 * dimensions.width,
                        colorGrey400, FontWeight.w400),
                  ),
                  TextSpan(
                    text: loginScreen['text8']!,
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      color: colorGrey600,
                      fontSize: 14 / 414 * dimensions.width,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  TextSpan(
                    text: loginScreen['text11']!,
                    style: poppinsTextStyle(14 / 414 * dimensions.width,
                        colorGrey400, FontWeight.w400),
                  ),
                ])),
              ),
              Visibility(
                visible: !widget.activities
                        .contains(ActivityType.privacyPolicyAcceptance) &&
                    widget.activities
                        .contains(ActivityType.termsConditionsAcceptance),
                child: RichText(
                    text: TextSpan(children: [
                  TextSpan(
                    text: loginScreen['text7']!,
                    style: poppinsTextStyle(14 / 414 * dimensions.width,
                        colorGrey400, FontWeight.w400),
                  ),
                  TextSpan(
                    text: loginScreen['text10']!,
                    style: TextStyle(
                      color: colorGrey600,
                      fontFamily: 'Poppins',
                      fontSize: 14 / 414 * dimensions.width,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  TextSpan(
                    text: loginScreen['text11']!,
                    style: poppinsTextStyle(14 / 414 * dimensions.width,
                        colorGrey400, FontWeight.w400),
                  ),
                ])),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

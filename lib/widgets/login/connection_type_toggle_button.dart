import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/user_connection_type.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../common/constant.dart';

class UserConnectionTypeToggleButton extends StatefulWidget {
  final Color textColor;

  const UserConnectionTypeToggleButton({super.key, required this.textColor});

  @override
  State<UserConnectionTypeToggleButton> createState() =>
      _UserConnectionTypeToggleButtonState();
}

class _UserConnectionTypeToggleButtonState
    extends State<UserConnectionTypeToggleButton> {
  bool isSwitched = false;
  late Color textColor;

  @override
  void initState() {
    textColor = widget.textColor;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return FutureBuilder(
        future: loadData(),
        builder: (context, snapshot) {
          Widget widget =  Center(
            child:  Image.asset(
              isTwoWheels ?
              loaderGifImages['2Wheels']! : loaderGifImages['3Wheels']!,
            ),
          );
          if (snapshot.connectionState == ConnectionState.done) {
            widget = GestureDetector(
              onTap: () async {
                SharedPreferences pref = await SharedPreferences.getInstance();
                if (isSwitched) {
                  setState(() {
                    isSwitched = !isSwitched;
                    pref.setString(
                        userConnectionTypeKey, UserConnectionType.rider.name);
                  });
                }
              },
              child: Row(
                children: [
                  GestureDetector(
                    child: Text(
                      UserConnectionType.rider.displayName,
                      style: poppinsTextStyle(16 / 414 * dimensions.width,
                          textColor, FontWeight.w600),
                    ),
                  ),
                  SizedBox(
                    width: 8 / 414 * dimensions.width,
                  ),
                  SizedBox(
                    width: 105 / 414 * dimensions.width,
                    child: GestureDetector(
                      onTap: () async {
                        SharedPreferences pref =
                            await SharedPreferences.getInstance();

                        setState(() {
                          isSwitched = !isSwitched;

                          isSwitched
                              ? pref.setString(userConnectionTypeKey,
                                  UserConnectionType.watcher.name)
                              : pref.setString(userConnectionTypeKey,
                                  UserConnectionType.rider.name);
                        });
                      },
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        height: 40.0 / 896 * dimensions.height,
                        width: 105.0 / 414 * dimensions.width,
                        padding: EdgeInsets.all(2 / 414 * dimensions.width),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              25.0 / 414 * dimensions.width),
                          color: isSwitched
                              ? colorToggleButtonWatcher
                              : colorToggleButtonRider,
                          border: Border.all(
                              color: colorGrey400,
                              width: 1 / 414 * dimensions.width),
                        ),
                        child: Stack(
                          children: [
                            AnimatedPositioned(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeIn,
                              left: (isSwitched ? 60.0 : 0.0) /
                                  414 *
                                  dimensions.width,
                              right: (isSwitched ? 0.0 : 60.0) /
                                  414 *
                                  dimensions.width,
                              child: AnimatedSwitcher(
                                  duration: const Duration(milliseconds: 300),
                                  transitionBuilder: (Widget child,
                                      Animation<double> animation) {
                                    return RotationTransition(
                                      turns: animation,
                                      child: child,
                                    );
                                  },
                                  child: SizedBox(
                                      width: 34 / 414 * dimensions.width,
                                      child: Image.asset(!isSwitched
                                          ? loginScreenImages[
                                              'riderToggleButtonIcon']!
                                          : loginScreenImages[
                                              'watcherToggleButtonIcon']!))),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 8 / 414 * dimensions.width,
                  ),
                  GestureDetector(
                    onTap: () async {
                      SharedPreferences pref =
                          await SharedPreferences.getInstance();
                      if (!isSwitched) {
                        setState(() {
                          isSwitched = !isSwitched;
                          pref.setString(userConnectionTypeKey,
                              UserConnectionType.watcher.name);
                        });
                      }
                    },
                    child: Text(
                      UserConnectionType.watcher.displayName,
                      style: poppinsTextStyle(16 / 414 * dimensions.width,
                          textColor, FontWeight.w600),
                    ),
                  ),
                ],
              ),
            );
          }
          return widget;
        });
  }

  loadData() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    String connectionType = pref.getString(userConnectionTypeKey) ?? 'rider';
    if (connectionType == 'rider') {
      pref.setString(userConnectionTypeKey, connectionType);
      isSwitched = false;
    } else {
      isSwitched = true;
    }
  }
}

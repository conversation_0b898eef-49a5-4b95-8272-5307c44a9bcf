import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/streams/privacy_policy_and_terms_check_box_data.dart';
import 'package:nds_app/streams/web_view_loader_state.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:nds_app/widgets/common/progress_indicator.dart';
import 'package:nds_app/widgets/login/privacy_policy_and_toc_check_box.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../common/image_urls.dart';
import '../../constant/api_urls.dart';

WebViewLoaderStateStream loaderStateStream = WebViewLoaderStateStream();

Future getBotttomSheetPrivacyPolicyAndToc(
    {required heading,
    required context,
    required UserActivitySetting userActivitySetting}) {
  Dimensions dimensions = Dimensions(context);
  CheckBoxPrivacyPolicyAndTosStream checkBoxStream =
      CheckBoxPrivacyPolicyAndTosStream();

  loaderStateStream.updateLoaderState(true);
  bool isLoading = true;

  checkBoxStream.updateCheckBox(false);
  bool isUpdated = userActivitySetting.isNewSettingExist;

  WebViewWidget webView = loadWebView(
      userActivitySetting.activityType, userActivitySetting.message);

  return showModalBottomSheet(
    enableDrag: false,
    isScrollControlled: false,
    isDismissible: !isUpdated,
    scrollControlDisabledMaxHeightRatio: 1,
    backgroundColor: Colors.transparent,
    shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
      top: Radius.circular(20),
    )),
    context: context,
    builder: (context) {
      return PopScope(
        canPop: !isUpdated,
        child: Container(
          alignment: Alignment.topLeft,
          height: 800 / 896 * dimensions.height,
          decoration: BoxDecoration(
            color: colorWhite,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(
                dimensions.width * 40 / 414,
              ),
              topRight: Radius.circular(
                dimensions.width * 40 / 414,
              ),
            ),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(
              vertical: 24 / 896 * dimensions.height,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: 24 / 414 * dimensions.width),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      FittedBox(
                        child: Text(
                          heading,
                          style: poppinsTextStyle(
                            26 / 414 * dimensions.width,
                            colorGrey800,
                            FontWeight.w600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Visibility(
                        visible: !isUpdated,
                        child: Container(
                            alignment: Alignment.centerRight,
                            width: 40 / 414 * dimensions.width,
                            height: 40 / 414 * dimensions.width,
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                const CircleAvatar(
                                  backgroundColor: colorGrey100,
                                ),
                                InkResponse(
                                    onTap: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: const Icon(Icons.close))
                              ],
                            )),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 8 / 896 * dimensions.height,
                ),
                SizedBox(
                    height: !isUpdated
                        ? 670 / 896 * dimensions.height
                        : 565 / 896 * dimensions.height,
                    width: dimensions.width,
                    child: StreamBuilder(
                      stream: loaderStateStream.loaderState,
                      builder: (context, snapshot) {
                        if (snapshot.hasData) {
                          isLoading = snapshot.data!;
                        }
                        return isLoading
                            ? Center(
                                child: Image.asset(
                                  isTwoWheels
                                      ? loaderGifImages['2Wheels']!
                                      : loaderGifImages['3Wheels']!,
                                ),
                              )
                            : webView;
                      },
                    )),
                Visibility(
                  visible: isUpdated,
                  child: Column(
                    children: [
                      Container(
                        alignment: Alignment.centerLeft,
                        padding: EdgeInsets.symmetric(
                          vertical: 12 / 896 * dimensions.height,
                          horizontal: 24 / 896 * dimensions.width,
                        ),
                        child: FittedBox(
                          child: CheckBoxPrivacyPolicyAndToc(
                              settings: [userActivitySetting],
                              activities: [userActivitySetting.activityType]),
                        ),
                      ),
                      StreamBuilder(
                        stream: checkBoxStream.checkBox,
                        builder: (context, snapshot) {
                          return snapshot.data == true
                              ? GestureDetector(
                                  onTap: () async {
                                    getCircularProgressIndicator(context);

                                    final request = {
                                      "activityType": userActivitySetting
                                          .activityType.requestName,
                                      "value": userActivitySetting.value,
                                      "organisationId": organisationId
                                    };
                                    http.Response response1 =
                                        await BackendApi.initiatePostCall(
                                      ApiUrls.saveUserKeyActivity,
                                      body: request,
                                    );

                                    if (response1.statusCode != 200) {
                                      debugPrint(
                                          "user activity type ${ActivityType.termsConditionsAcceptance.requestName} failed for value ${userActivitySetting.value}");
                                    }

                                    // ignore: use_build_context_synchronously
                                    Navigator.of(context)
                                        .popUntil((route) => route.isFirst);
                                  },
                                  child: CustomButton.gesture(
                                    text: loginScreen["text14"]!,
                                    backgroundColor: loginThemeColor,
                                    size: CustomButtonSize.fullWidth,
                                    textColor: colorWhite,
                                    fontWeight: FontWeight.w600,
                                  ))
                              : CustomButton.gesture(
                                  text: loginScreen["text14"]!,
                                  backgroundColor: secondButtonColor,
                                  size: CustomButtonSize.fullWidth,
                                  textColor: colorWhite,
                                  fontWeight: FontWeight.w600,
                                );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
}

WebViewWidget loadWebView(ActivityType? activityType, String content) {
  WebViewController webViewController = WebViewController();

  return WebViewWidget(
    controller: webViewController
      ..setJavaScriptMode(JavaScriptMode.disabled)
      ..setNavigationDelegate(
        NavigationDelegate(
          onNavigationRequest: (NavigationRequest request) {
            return NavigationDecision.prevent;
          },
          onPageFinished: (url) => loaderStateStream.updateLoaderState(false),
        ),
      )
      ..loadRequest(Uri.parse(content)),
  );
}

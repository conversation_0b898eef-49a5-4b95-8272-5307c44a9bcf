import 'package:flutter/material.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/user_info.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/streams/vehicle_data.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/utils/oval_shape_vehicle_base.dart';
import 'package:nds_app/widgets/dashboard/home/<USER>';
import 'package:nds_app/widgets/dashboard/home/<USER>';
import 'package:nds_app/widgets/dashboard/home/<USER>';
import '../../../branding/branding.dart';
import '../../../common/colors.dart';
import '../../../common/image_urls.dart';
import '../../../common/strings.dart';
import '../../../common/text_styles.dart';
import '../../../constant/connected_vehicle_status.dart';
import '../../../utils/calculate_data.dart';
import '../../../utils/gradient_circular_charge_indicator.dart';
import '../../../utils/gradient_triangle.dart';
import '../../common/common_widget.dart';

class BatteryAndRidingModes extends StatefulWidget {
  final VehicleInfo vehicleInfo;
  final UserInfo? userInfo;
  const BatteryAndRidingModes({
    super.key,
    required this.vehicleInfo,
    required this.userInfo,
  });

  @override
  State<BatteryAndRidingModes> createState() => _BatteryAndRidingModesState();
}

class _BatteryAndRidingModesState extends State<BatteryAndRidingModes> {
  late ConnectedVehicleStatus status;
  late ThemeMode themeMode;
  VehicleDataStream stream = VehicleDataStream();
  String? ridingMode;
  VehicleInfo? vehicle;
  int? charge;


  @override
  void initState() {
    status = getBattryStatus(widget.vehicleInfo.batteryCharging ?? false,
        widget.vehicleInfo.batteryConnected ?? true);

    themeMode = MyApp.of(context).getCurrentThemeMode();
    charge = widget.vehicleInfo.charge;
    ridingMode = widget.vehicleInfo.currentDriveMode;
    vehicle = widget.vehicleInfo;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    Color bikeColor = colorDefaultVehicleThemeDark;
    if (widget.vehicleInfo.color != null &&
        widget.vehicleInfo.color!.isNotEmpty) {
      bikeColor = (widget.vehicleInfo.color)?.toColor();
    }
    int fuelSaving =
        (widget.userInfo?.equivalent ?? 0) - (widget.userInfo?.spent ?? 0);

    String? imageUrl;

    widget.vehicleInfo.images?.forEach(
      (element) {
        if (element.tag == defaultImageTag) {
          imageUrl = element.url;
        }
      },
    );

    return Column(
      children: [
        getChargingAnimationWithDetails(
            dimensions, bikeColor, status, imageUrl, stream.vehicleInfo),
        StreamBuilder(
            stream: stream.vehicleInfo,
            builder: (context, snapshot) {
              if (snapshot.data != null) {
                ridingMode = snapshot.data?.currentDriveMode ?? "eco";
                charge = snapshot.data?.charge ?? 0;
                vehicle = snapshot.data ?? vehicle;
              }
              return RideMode(
                vehicleModesInfo: vehicle!.vehicleModeInfoList,
                currentDriveMode: ridingMode,
                charge: charge ?? 0,
              );
            }),
        SizedBox(
          height: 16 / 896 * dimensions.height,
        ),
        Visibility(
          visible: !isProdRedUser,
          child: getFuelSavingAndTotalDistanceRow(dimensions, true, fuelSaving,
              widget.userInfo?.distanceCovered ?? 0, "km", context),
        )
      ],
    );
  }

  String? getStatusIcon() {
    if (status == ConnectedVehicleStatus.batteryRemoved) {
      return "battery_unplug";
    } else if (status == ConnectedVehicleStatus.charging) {
      return "charging";
    } else if (status == ConnectedVehicleStatus.connected) {
      if (ridingMode?.isNotEmpty == true) {
        return ridingMode?.toLowerCase();
      }
      return "eco";
    }
    return "eco";
  }

  Widget getChargingAnimationWithDetails(
      Dimensions dimensions,
      Color bikeColor,
      ConnectedVehicleStatus status,
      String? imageUrl,
      Stream<VehicleInfo> stream) {
    return StreamBuilder<VehicleInfo>(
      stream: stream,
      builder: (context, snapshot) {
        final vehicleInfo = snapshot.data ?? widget.vehicleInfo;
        final int charge = vehicleInfo.charge ?? 0;
        final String vehicleNum = vehicleInfo.modelNo ?? '';
        final bool batteryActive = vehicleInfo.batteryConnected ?? true;
        /*int timeRemainingInMin = widget.vehicleInfo.remainingTimeForCharge ?? 0;
     String vehicleImageUrl = widget.vehicleInfo.imageUrl ?? "";*/
        /*return Stack(children: [
      HomeBatteryMeter(charge: charge, status: status),
      getChargeForDisplay(widget.vehicleInfo.charge, dimensions),
      getBatteryUnplugIcon(dimensions),
      getGradientTriangleForChargingVehicle(dimensions, bikeColor),
      getOvalShapeBaseForVehicle(dimensions, bikeColor),
      getVehicleImage(dimensions, imageUrl),
      getCompanyLabel(dimensions),
      Visibility(
          visible: [
            ConnectedVehicleStatus.charging,
            ConnectedVehicleStatus.batteryConnecting,
          ].contains(status),
          child: ChargingCircularProgressIndicator(bikeColor: bikeColor)),
      getRadialGradientForBatteryRemoved(dimensions),
      getVehicleStatusIcon(dimensions, bikeColor),
      getRemainingTimeDisplayWidget(dimensions, timeRemainingInMin),
      getBatteryConnectingText(dimensions, charge)
    ]);*/

        return Stack(
          children: [
            getCircularGradientBG(dimensions),
            HomeBatteryMeter(charge: charge, status: status),
            getVehicleInfoDetails(charge, vehicleNum, bikeColor, dimensions),

            // Battery Removed View
            Visibility(
              visible: !batteryActive,
              child: ChargingCircularProgressIndicator(
                bikeColor: bikeColor,
                isBatteryRemoved: !batteryActive,
              ),
            ),

            // Battery Charging
            Visibility(
              visible: [
                ConnectedVehicleStatus.charging,
                ConnectedVehicleStatus.batteryConnecting,
              ].contains(status),
              child: ChargingCircularProgressIndicator(
                bikeColor: bikeColor,
                isBatteryRemoved: !batteryActive,
              ),
            ),
          ],
        );
      },
    );
  }

  // current new UI
  Widget getCircularGradientBG(Dimensions dimensions) {
    return Positioned(
        child: Container(
          height: 395 / 414 * dimensions.width,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
                colors: themeMode == ThemeMode.dark
                    ? [
                  Colors.transparent,
                  colorBackgroundDarkMode,
                  colorBlack800,
                  colorBackgroundDarkMode,
                  Colors.transparent,
                ]
                    : [
                  Colors.transparent,
                  colorGrey300,
                  colorWhite,
                  colorGrey300,
                  Colors.transparent,
                ],
                stops: const [0.92, 0.94, 0.98, 1.99, 1.0]),
          ),
        ));
  }

  Widget getStatusText(ConnectedVehicleStatus status) {
    final Map<ConnectedVehicleStatus, String?> statusMessages = {
      ConnectedVehicleStatus.batteryRemoved: homeScreenText["text21"],
      ConnectedVehicleStatus.charging: homeScreenText["text22"],
      ConnectedVehicleStatus.connected: afterConnectionCompanyLabel,
    };

    final String? text = statusMessages[status];

    return text != null
        ? Align(
      alignment: Alignment.topCenter,
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall,
        overflow: TextOverflow.ellipsis,
      ),
    )
        : const SizedBox.shrink();
  }

  Widget getVehicleInfoDetails(int? charge, String? vehicleNumber, Color bikeColor, Dimensions dimensions) {
    return Align(
      alignment: Alignment.topCenter,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 100 / 896 * dimensions.height),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                charge == null
                    ? "?"
                    : homeScreenText["text20"]!
                        .replaceAll("@value", charge.toString()),
                style: poppinsTextStyle(88 / 414 * dimensions.width,
                    colorBlueFuelTile, FontWeight.w500),
              ),
              Text(
                charge == null ? "" : "%",
                style: poppinsTextStyle(28 / 414 * dimensions.width,
                    colorBlueFuelTile, FontWeight.w500),
              ),
            ],
          ),
          Text(
            vehicleNumber ?? "",
            style: Theme.of(context).textTheme.headlineLarge,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: 10 / 896 * dimensions.height),
          getStatusText(status),
          SizedBox(height: 10 / 896 * dimensions.height),
          Container(
            height: 50 / 414 * dimensions.width,
            width: 50 / 414 * dimensions.width,
            decoration: BoxDecoration(
              color: themeMode == ThemeMode.dark ? colorBlack800 : colorWhite,
              shape: BoxShape.circle,
              border: Border.all(
                color:
                    themeMode == ThemeMode.dark ? colorWhiteLite : colorGrey300,
              ),
            ),
            child: Center(
              child: Image.asset(
                homeScreenImages[getStatusIcon()] ?? homeScreenImages["eco"]!,
                height: 24,
                width: 24,
                fit: BoxFit.contain,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // old UI
  Widget getChargeForDisplay(int? charge, Dimensions dimensions) {
    return Visibility(
      visible: [
        ConnectedVehicleStatus.connected,
        ConnectedVehicleStatus.charging,
      ].contains(status),
      child: Align(
        alignment: Alignment.topCenter,
        child: StreamBuilder(
          stream: stream.vehicleInfo,
          builder: (context, snapshot) {
            if (snapshot.data != null) {
              charge = snapshot.data?.charge ?? 0;
            }
            return Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 75 / 896 * dimensions.height,
                ),
                Text(
                    charge == null
                        ? "?"
                        : homeScreenText["text11"]!
                        .replaceAll("@value", charge!.round().toString()),
                    style: Theme.of(context).textTheme.displayLarge),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget getBatteryUnplugIcon(Dimensions dimensions) {
    return Visibility(
      visible: status == ConnectedVehicleStatus.batteryRemoved,
      child: Positioned(
          left: 155 / 414 * dimensions.width,
          top: 90 / 896 * dimensions.height,
          child: SizedBox(
            width: 56 / 414 * dimensions.width,
            height: 56 / 896 * dimensions.height,
            child: Image.asset(homeScreenImages["battery_unplug"]!),
          )),
    );
  }

  Widget getGradientTriangleForChargingVehicle(
      Dimensions dimensions, Color bikeColor) {
    return Visibility(
      visible: [
        ConnectedVehicleStatus.charging,
        ConnectedVehicleStatus.batteryRemoved,
        ConnectedVehicleStatus.batteryConnecting
      ].contains(status),
      child: Positioned(
          left: 110 / 414 * dimensions.width,
          top: 265 / 896 * dimensions.height,
          child: GradientTriangle(
              width: 150 / 414 * dimensions.width,
              height: 100 / 896 * dimensions.height,
              colors: [
                themeMode == ThemeMode.dark
                    ? colorBackgroundDarkMode
                    : colorWhite,
                bikeColor
              ])),
    );
  }

  Widget getOvalShapeBaseForVehicle(Dimensions dimensions, Color bikeColor) {
    return Positioned(
        left: 109 / 414 * dimensions.width,
        top: 247 / 896 * dimensions.height,
        child: Column(
          children: [
            SizedBox(
              height: 45 / 896 * dimensions.height,
              width: 152 / 414 * dimensions.width,
              child: OvalShapeBase(
                width: 152 * dimensions.width,
                height: 45 * dimensions.height,
                gradientColors: [colorWhite, bikeColor],
              ),
            ),
          ],
        ));
  }

  Widget getVehicleImage(Dimensions dimensions, String? imageUrl) {
    return Positioned(
        left: 120 / 414 * dimensions.width,
        top: 148 / 896 * dimensions.height,
        child: SizedBox(
          height: 140 / 896 * dimensions.height,
          width: 128 / 414 * dimensions.width,
          child: imageUrl == null
              ? Icon(Icons.electric_bike, size: 128 / 414 * dimensions.width)
              : Image.network(imageUrl, fit: BoxFit.fill),
        ));
  }

  Widget getCompanyLabel(Dimensions dimensions) {
    return Visibility(
      visible: status == ConnectedVehicleStatus.connected,
      child: Positioned.fill(
        top: 320 / 896 * dimensions.height,
        child: Align(
          alignment: Alignment.topCenter,
          child: Text(
            afterConnectionCompanyLabel,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ),
      ),
    );
  }

  Widget getRadialGradientForBatteryRemoved(Dimensions dimensions) {
    return Visibility(
      visible: status == ConnectedVehicleStatus.batteryRemoved,
      child: Positioned(
          child: Container(
        height: 380 / 414 * dimensions.width,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: RadialGradient(
              colors: themeMode == ThemeMode.dark
                  ? [
                      Colors.transparent,
                      colorBackgroundDarkMode,
                      colorBlack800,
                      colorBackgroundDarkMode,
                      Colors.transparent,
                    ]
                  : [
                      Colors.transparent,
                      colorGrey300,
                      colorWhite,
                      colorGrey300,
                      Colors.transparent,
                    ],
              stops: const [0.94, 0.94, 0.98, 1.99, 1.0]),
        ),
      )),
    );
  }

  Widget getVehicleStatusIcon(Dimensions dimensions, Color bikeColor) {
    return Visibility(
      visible: [
        ConnectedVehicleStatus.charging,
        ConnectedVehicleStatus.batteryConnecting,
        ConnectedVehicleStatus.batteryRemoved
      ].contains(status),
      child: Positioned(
          left: 176 / 414 * dimensions.width,
          top: 303 / 896 * dimensions.height,
          child: SizedBox(
            width: 20 / 414 * dimensions.width,
            height: 20 / 896 * dimensions.height,
            child: Image.asset(
                homeScreenImages[status == ConnectedVehicleStatus.charging
                    ? "charge_icon"
                    : "battery_removing_icon"]!,
                color: themeMode == ThemeMode.dark ? bikeColor : colorBlack,
                fit: BoxFit.fill),
          )),
    );
  }

  Widget getRemainingTimeDisplayWidget(
      Dimensions dimensions, int timeRemainingInMin) {
    return Visibility(
      visible: status == ConnectedVehicleStatus.charging,
      child: Positioned.fill(
          left: 285 / 414 * dimensions.width,
          top: 30 / 896 * dimensions.height,
          child: SizedBox(
            width: 44 / 414 * dimensions.width,
            height: 44 / 414 * dimensions.height,
            child: GradientCircleChargeIndicator(
              radius: 44 / 414 * dimensions.width,
              colors: const [colorWhite, colorGrey400],
              timeRemainingInMin: timeRemainingInMin,
            ),
          )),
    );
  }

  Widget getBatteryConnectingText(Dimensions dimensions, int charge) {
    return Positioned(
      top: 110 / 896 * dimensions.height,
      left: 110 / 414 * dimensions.width,
      child: Visibility(
        visible: [
          ConnectedVehicleStatus.batteryConnecting,
        ].contains(status),
        child: Center(
            child: Text(
                homeScreenText["text13"]!
                    .replaceAll("@value", charge.round().toString()),
                style: poppinsTextStyle(14 / 414 * dimensions.width,
                    colorSuccessGreen500, FontWeight.w600))),
      ),
    );
  }
}

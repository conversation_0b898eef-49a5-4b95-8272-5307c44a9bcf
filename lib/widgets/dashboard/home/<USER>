import 'package:flutter/material.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/utils/concave_decoration.dart';
import 'package:nds_app/utils/convax_decoration_rectangle.dart';
import 'package:nds_app/utils/extension.dart';

import '../../../common/colors.dart';
import '../../../constant/riding_modes.dart';
import 'package:nds_app/widgets/common/data_not_available_widget.dart';

class RideMode extends StatelessWidget {
  final List<VehicleModeInfo>? vehicleModesInfo;
  final String? currentDriveMode;
  final int charge;

  const RideMode(
      {super.key,
      required this.vehicleModesInfo,
      required this.currentDriveMode,
      required this.charge});

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    List<VehicleModeInfo>? modes = [];
    List<RidingModes> rideModes = [];

    // Check if vehicle mode info is available
    if (vehicleModesInfo == null || vehicleModesInfo!.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0 / 414 * dimensions.width),
            child: Text(
              clusterScreenText["range"]!,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
          ),
          DataNotAvailableWidget.ridingMode(),
        ],
      );
    }

    vehicleModesInfo?.forEach((element) {
      if (element.mode?.toLowerCase() != 'reverse') {
        try {
          rideModes.add(RidingModes.values.byName((element.mode!).toLowerCase()));
        } catch (e) {
          // Skip invalid modes
          debugPrint('Invalid mode found: ${element.mode}');
        }
      }
    });
    rideModes.sort((a, b) => b.index.compareTo(a.index));

    for (var element in rideModes) {
      try {
        final modeInfo = vehicleModesInfo?.firstWhere(
            (e) => e.mode?.toLowerCase() == element.name.toLowerCase());
        if (modeInfo != null) {
          modes.add(modeInfo);
        }
      } catch (e) {
        // Skip if mode not found - but log for debugging
        debugPrint('Mode not found: ${element.name}');
      }
    }

    // If no valid modes found, show data not available message
    if (modes.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0 / 414 * dimensions.width),
            child: Text(
              clusterScreenText["range"]!,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
          ),
          DataNotAvailableWidget.ridingMode(),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: 8.0 / 414 * dimensions.width),
          child: Text(
            clusterScreenText["range"]!,
            style: Theme.of(context).textTheme.headlineMedium,
          ),
        ),
        Container(
          height: 101 / 896 * dimensions.height,
          width: 374 / 414 * dimensions.width,
          alignment: Alignment.centerLeft,
          child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: modes.length,
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              itemBuilder: (context, index) {
                VehicleModeInfo? modeInfo = modes[index];

                RidingModes? mode;
                try {
                  mode = RidingModes.values.firstWhere((element) =>
                      element.name == (modeInfo.mode?.toString() ?? "").toLowerCase());
                } catch (e) {
                  // Show data not available instead of defaulting to eco mode
                  return DataNotAvailableWidget(
                    message: commonText['modeDataNotAvailable']!,
                    height: 101 / 896 * dimensions.height,
                    width: 120 / 414 * dimensions.width,
                    padding: EdgeInsets.all(8 / 414 * dimensions.width),
                    fontSize: 10 / 414 * dimensions.width,
                  );
                }

                // At this point, mode is guaranteed to be non-null
                String url = mode.url;

                bool isCurrentVehicleMode =
                    modeInfo.mode.toString().toLowerCase() ==
                        currentDriveMode?.toLowerCase();

                List<Widget> list = [
                  Stack(
                    children: [
                      Container(
                        height: 98 / 896 * dimensions.height,
                        width: 120 / 414 * dimensions.width,
                        decoration: ConcaveDecoration(
                          alignment: Alignment.topLeft,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                                8 / 414 * dimensions.width),
                          ),
                          depth: 5,
                          colors: [
                            colorGrey200.withOpacity(0.3),
                            colorBlack.withOpacity(0.7),
                          ],
                          opacity: 0.4,
                        ),
                      ),
                      Visibility(
                        visible: isCurrentVehicleMode,
                        child: Container(
                          height: 96 / 896 * dimensions.height,
                          width: 118 / 414 * dimensions.width,
                          margin: EdgeInsets.only(
                              left: 1 / 414 * dimensions.width,
                              top: 3 / 896 * dimensions.height),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(
                                  8 / 414 * dimensions.width),
                              color: vehicleInfoConstant?.color?.toColor(),
                              boxShadow: [
                                BoxShadow(
                                    color: MyApp.of(context)
                                                .getCurrentThemeMode() ==
                                            ThemeMode.dark
                                        ? colorGrey500
                                        : colorGrey300,
                                    offset: const Offset(3, 3),
                                    spreadRadius: 2,
                                    blurRadius: 5)
                              ]),
                        ),
                      ),
                      Visibility(
                        visible: isCurrentVehicleMode,
                        child: Container(
                          height: 96 / 896 * dimensions.height,
                          width: 118 / 414 * dimensions.width,
                          margin: EdgeInsets.symmetric(
                              horizontal: 1 / 414 * dimensions.width,
                              vertical: 1 / 896 * dimensions.height),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                8 / 414 * dimensions.width),
                            color: Theme.of(context).scaffoldBackgroundColor,
                          ),
                        ),
                      ),
                      Visibility(
                        visible: isCurrentVehicleMode,
                        child: Container(
                          height: 95 / 896 * dimensions.height,
                          width: 117 / 414 * dimensions.width,
                          margin: EdgeInsets.symmetric(
                              horizontal: 1 / 414 * dimensions.width,
                              vertical: 1 / 896 * dimensions.height),
                          decoration: ConvexDecorationRectangle(
                            depth: 5,
                            colors: [
                              colorBlack.withOpacity(0.3),
                              colorGrey300.withOpacity(0.3),
                            ],
                            opacity: 0.4,
                            borderRadius: BorderRadius.circular(
                                8 / 414 * dimensions.width),
                          ),
                        ),
                      ),
                      Container(
                        height: 98 / 896 * dimensions.height,
                        width: 120 / 414 * dimensions.width,
                        padding: EdgeInsets.symmetric(
                            vertical: 11 / 896 * dimensions.height),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(
                              Radius.circular(8 / 414 * dimensions.width)),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                                width: 20 / 414 * dimensions.width,
                                height: 20 / 896 * dimensions.height,
                                child: Image.asset(url)),
                            Flexible(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    "${((charge / 100) * modeInfo.maxRange! - modeInfo.rangeCorrection!).floor()}",
                                    style: Theme.of(context)
                                        .textTheme
                                        .displayMedium,
                                  ),
                                  Text(
                                    " ${modeInfo.rangeUnit?.toLowerCase()}",
                                    style:
                                        Theme.of(context).textTheme.bodySmall,
                                  )
                                ],
                              ),
                            ),
                            Text(
                              modeInfo.mode.toString(),
                              style: Theme.of(context).textTheme.labelMedium,
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ];

                if (index != modes.length - 1) {
                  list.add(SizedBox(
                    width: 7 / 414 * dimensions.width,
                  ));
                }
                return Row(
                  children: list,
                );
              }),
        ),
      ],
    );
  }
}

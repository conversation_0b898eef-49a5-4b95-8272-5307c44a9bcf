import 'package:flutter/material.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/streams/vehicle_data.dart';

import '../../../common/image_urls.dart';
import '../../../constant/connected_vehicle_status.dart';

class HomeBatteryMeter extends StatelessWidget {
  final int charge;
  final ConnectedVehicleStatus status;

  const HomeBatteryMeter(
      {super.key, required this.charge, required this.status});

  @override
  Widget build(BuildContext context) {
    VehicleDataStream stream = VehicleDataStream();
    Dimensions dimensions = Dimensions(context);
    int chargeIndex = (charge / 10).round();
    return StreamBuilder(
      stream: stream.vehicleInfo,
      builder: (context, snapshot) {
        if (snapshot.data != null) {
          chargeIndex = ((snapshot.data?.charge ?? 0) / 10).round();
        }
        return Stack(
          children: [
            SizedBox(
              width: 374 * dimensions.width,
              child:
                  Image.asset(batteryChargeParcentage["0"]!, fit: BoxFit.fill),
            ),
            Visibility(
              visible: status != ConnectedVehicleStatus.batteryRemoved,
              child: SizedBox(
                width: 374 * dimensions.width,
                child: Image.asset(
                    batteryChargeParcentage[chargeIndex.toString()]!,
                    fit: BoxFit.fill),
              ),
            ),
          ],
        );
      },
    );
  }
}

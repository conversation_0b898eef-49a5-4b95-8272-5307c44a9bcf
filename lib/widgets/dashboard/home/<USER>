import 'package:flutter/material.dart';

import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/constant/connected_vehicle_status.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/models/vehicle_info.dart';

import '../../../common/colors.dart';
import '../../../common/dummy_data.dart';
import '../../../common/strings.dart';
import '../../../main.dart';
import '../../../utils/calculate_data.dart';
import 'package:nds_app/utils/extension.dart';

class UserAndVehicleName extends StatefulWidget {
  final VehicleStatus currentVehicleStatus;
  final VehicleInfo vehicleInfo;
  final String firstName;
  const UserAndVehicleName(
      {super.key,
      required this.currentVehicleStatus,
      required this.vehicleInfo,
      required this.firstName});

  @override
  State<UserAndVehicleName> createState() => _UserAndVehicleNameState();
}

class _UserAndVehicleNameState extends State<UserAndVehicleName> {
  ConnectedVehicleStatus status = ConnectedVehicleStatus.connected;
  Color bikeColor = colorDefaultVehicleThemeDark;
  late ThemeMode themeMode;

  @override
  void initState() {
    status = getVehicleStatusConstantFromString(
        connectedVehicleDetails["status"]! as String);
    themeMode = MyApp.of(context).getCurrentThemeMode();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    if (widget.vehicleInfo.color != null &&
        widget.vehicleInfo.color!.isNotEmpty) {
      bikeColor = (widget.vehicleInfo.color)?.toColor();
    }

    return Padding(
      padding: EdgeInsets.symmetric(
          vertical: 16.5 / 896 * dimensions.height,
          horizontal: 6 / 414 * dimensions.width),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  homeScreenText["user_name"]!
                      .replaceFirst('@value', widget.firstName),
                  style: Theme.of(context).textTheme.headlineLarge,
                  overflow: TextOverflow.ellipsis,
                  softWrap: false,
                ),
              ),
/*              Visibility(
                visible: status != ConnectedVehicleStatus.connected &&
                    widget.currentVehicleStatus == VehicleStatus.connected,
                child: Text(
                  widget.vehicleInfo.regNo ?? "",
                  style: Theme.of(context).textTheme.labelLarge,
                ),
              ),*/
            ],
          ),
          SizedBox(
            height: 4 / 896 * dimensions.height,
          ),
          Visibility(
            visible: status != ConnectedVehicleStatus.connected &&
                widget.currentVehicleStatus == VehicleStatus.connected,
            child: Text(
              getBattryStatus(widget.vehicleInfo.batteryCharging ?? false,
                      widget.vehicleInfo.batteryConnected ?? true)
                  .name,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
          ),
        ],
      ),
    );
  }
}

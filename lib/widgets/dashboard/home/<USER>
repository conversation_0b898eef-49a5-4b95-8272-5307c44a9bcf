import 'package:flutter/material.dart';
import 'package:nds_app/common/dimensions.dart';

import '../../../common/colors.dart';
import '../../../main.dart';
import '../../../utils/gradient_circular_progress_indicator.dart';

class ChargingCircularProgressIndicator extends StatefulWidget {
  final Color bikeColor;
  final bool isBatteryRemoved;
  const ChargingCircularProgressIndicator({super.key, required this.bikeColor, required this.isBatteryRemoved});

  @override
  State<ChargingCircularProgressIndicator> createState() =>
      _ChargingCircularProgressIndicatorState();
}

class _ChargingCircularProgressIndicatorState
    extends State<ChargingCircularProgressIndicator>
    with TickerProviderStateMixin {
  late AnimationController _chargingProgressIndicatorController;
  late ThemeMode themeMode;

  @override
  void initState() {
    _chargingProgressIndicatorController =
        AnimationController(vsync: this, duration: const Duration(seconds: 4));
    _chargingProgressIndicatorController.addListener(() => setState(() {}));
    themeMode = MyApp.of(context).getCurrentThemeMode();
    if (!widget.isBatteryRemoved) {
      _chargingProgressIndicatorController.repeat();
    }
    super.initState();
  }
  @override
  void didUpdateWidget(covariant ChargingCircularProgressIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isBatteryRemoved) {
      _chargingProgressIndicatorController.stop(); // STOP ROTATION
    } else {
      _chargingProgressIndicatorController.repeat();
    }
  }
  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return RotationTransition(
      turns: widget.isBatteryRemoved
          ? const AlwaysStoppedAnimation(0) // NO ROTATION
          : Tween(begin: 0.0, end: 1.0)
          .animate(_chargingProgressIndicatorController),
      child: GradientCircularProgressIndicator(
        radius: 194 / 414 * dimensions.width,
        gradientColors: widget.isBatteryRemoved
            ? [Colors.red, Colors.red] // RED COLOR WHEN BATTERY REMOVED
            : [
          themeMode == ThemeMode.dark
              ? colorBackgroundDarkMode
              : Colors.white,
          widget.bikeColor == Colors.white || widget.bikeColor.computeLuminance() > 0.9
              ? Colors.green
              : widget.bikeColor,
        ],
        strokeWidth: 10.0, isBatteryRemoved: widget.isBatteryRemoved,
      ),
    );
  }

  @override
  void dispose() {
    _chargingProgressIndicatorController.dispose();
    super.dispose();
  }
}

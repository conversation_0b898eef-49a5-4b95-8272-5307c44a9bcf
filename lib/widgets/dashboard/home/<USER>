import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/dummy_data.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/models/nearby_vehicle.dart';
import 'package:nds_app/utils/device_battery_image.dart';
import '../../../common/constant.dart';
import '../../../common/image_urls.dart';

class MapAndAvailableVehicle extends StatefulWidget {
  const MapAndAvailableVehicle({super.key});

  @override
  State<MapAndAvailableVehicle> createState() => _MapAndAvailableVehicleState();
}

class _MapAndAvailableVehicleState extends State<MapAndAvailableVehicle> {
  final Completer<GoogleMapController> _controller =
      Completer<GoogleMapController>();
  Set<Marker> markers = {};

  List<NearByVehicle> nearbyVehicles = [];
  late LocationData locationData;

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return FutureBuilder(
      builder: (context, snapshot) {
        Widget widget =  Center(child:Image.asset(
          isTwoWheels
              ? loaderGifImages['2Wheels']!
              : loaderGifImages['3Wheels']!,
        ),);
        if (snapshot.connectionState == ConnectionState.done) {
          widget = Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                homeScreenText["text2"]!,
                style: poppinsTextStyle(
                    16 / 414 * dimensions.width, colorGrey800, FontWeight.w500),
              ),
              SizedBox(
                height: 12 / 896 * dimensions.width,
              ),
              SizedBox(
                height: 380 / 896 * dimensions.height,
                width: 374 / 414 * dimensions.width,
                child: GoogleMap(
                  mapType: MapType.normal,
                  initialCameraPosition: CameraPosition(
                    target: LatLng(locationData.latitude ?? 0,
                        locationData.longitude ?? 0),
                    zoom: 14.4746,
                  ),
                  myLocationEnabled: true,
                  markers: markers,
                  onMapCreated: (GoogleMapController controller) {
                    _controller.complete(controller);
                  },
                  gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
                    Factory<OneSequenceGestureRecognizer>(
                      () => EagerGestureRecognizer(),
                    ),
                  },
                ),
              ),
              SizedBox(
                height: 16 / 896 * dimensions.height,
              ),
              getAvailableVehicles(dimensions),
              SizedBox(
                height: 100 / 896 * dimensions.height,
              ),
            ],
          );
        }
        return widget;
      },
      future: getNearbyVehicles(),
    );
  }

  getUserLocation() async {
    Location location = Location();
    locationData = await location.getLocation();
  }

  getNearbyVehicles() async {
    await getUserLocation();
    NearbyPOI vehicleInfo = NearbyPOI.fromJson(dummyAvailableVehiclesDetails);
    nearbyVehicles = vehicleInfo.nearByVehicles ?? [];
    for (NearByVehicle vehicle in nearbyVehicles) {
      markers.add(Marker(
        markerId: MarkerId(vehicle.regNo ?? DateTime.now().toString()),
        position: LatLng(vehicle.latitude ?? 0, vehicle.longitude ?? 0),
        infoWindow: InfoWindow(
          title: vehicle.regNo ?? "",
        ),
      ));
    }
  }

  getAvailableVehicles(Dimensions dimensions) {
    return ListView.builder(
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: nearbyVehicles.length,
      itemBuilder: (context, index) {
        NearByVehicle vehicleDetails = nearbyVehicles[index];

        return Column(
          children: [
            Container(
              height: 72 / 896 * dimensions.height,
              padding:
                  EdgeInsets.symmetric(horizontal: 12 / 414 * dimensions.width),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(
                      Radius.circular(8 / 414 * dimensions.width)),
                  border: Border.all(color: colorGrey200)),
              child: Row(
                children: [
                  const Icon(Icons.electric_bike),
                  SizedBox(
                    width: 8 / 414 * dimensions.width,
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            vehicleDetails.regNo ?? "",
                            style: poppinsTextStyle(14 / 414 * dimensions.width,
                                colorGrey800, FontWeight.w500),
                          ),
                          SizedBox(width: 140 / 414 * dimensions.width),
                          getBatteryPercentageContainer(
                              dimensions, vehicleDetails.charge ?? 0, context),
                        ],
                      ),
                      Text(
                        "${vehicleDetails.distance ?? 0}${vehicleDetails.distanceUnit}",
                        style: poppinsTextStyle(12 / 414 * dimensions.width,
                            colorGrey400, FontWeight.w300),
                      )
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(height: 12 / 896 * dimensions.height)
          ],
        );
      },
    );
  }
}

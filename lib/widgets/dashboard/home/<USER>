import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nds_app/common/dimensions.dart';

import '../../../common/colors.dart';
import '../../../common/image_urls.dart';
import '../../../common/strings.dart';
import '../../../main.dart';

class FlueSavingAndTotalDistanceRow extends StatefulWidget {
  final bool isConnected;
  final int savedAmount;
  final int totalDistance;
  final String distanceUnit;

  const FlueSavingAndTotalDistanceRow(
      {super.key,
      required this.isConnected,
      required this.savedAmount,
      required this.totalDistance,
      required this.distanceUnit});

  @override
  State<FlueSavingAndTotalDistanceRow> createState() =>
      _FlueSavingAndTotalDistanceRowState();
}

class _FlueSavingAndTotalDistanceRowState
    extends State<FlueSavingAndTotalDistanceRow> {
  @override
  Widget build(BuildContext context) {
    ThemeMode themeMode = MyApp.of(context).getCurrentThemeMode();
    Dimensions dimensions = Dimensions(context);
    String formattedSavedAmount = NumberFormat.decimalPattern('en_IN').format(widget.savedAmount);

    return Row(
      children: [
        Visibility(
            visible: widget.isConnected == true,
            child: Row(
              children: [
                Text(
                  homeScreenText["text5"]!,
                  style: Theme.of(context).textTheme.displaySmall,
                ),
                SizedBox(
                  width: 8 / 896 * dimensions.width,
                ),
                SizedBox(
                    width: 10 / 414 * dimensions.width,
                    height: 10 / 896 * dimensions.height,
                    child: Image.asset(
                      homeScreenImages["rupee_icon"]!,
                      color: themeMode == ThemeMode.dark
                          ? colorGrey200
                          : colorGrey600,
                    )),
                Text(
                  formattedSavedAmount,
                  style: Theme.of(context).textTheme.labelSmall,
                ),
                SizedBox(
                  width: 41 / 414 * dimensions.width,
                ),
              ],
            )),
        Text(
          homeScreenText["text1"]!,
          style: Theme.of(context).textTheme.displaySmall,
        ),
        Flexible(
          child: SizedBox(
            width: 8 / 896 * dimensions.width,
          ),
        ),
        Flexible(
          child: SizedBox(
            width: 14 / 896 * dimensions.width,
          ),
        ),
        Text(
          "${widget.totalDistance} ${widget.distanceUnit}",
          style: Theme.of(context).textTheme.labelSmall,
        ),
      ],
    );
  }
}

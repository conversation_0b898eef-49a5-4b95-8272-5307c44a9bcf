import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_event.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_event.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/action.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/enums/permission_status.dart';
import 'package:nds_app/models/enums/verification_status.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/services/overlay_service.dart';
import 'package:nds_app/streams/vehicle_status_data.dart';
import 'package:nds_app/utils/toast.dart';
import 'package:nds_app/widgets/profile/profile_image.dart';

class ConnectPopupB2C extends StatefulWidget {
  final bool isPromotionalScreen;
  final GlobalKey overlayMenuButtonKey;

  const ConnectPopupB2C(
      {super.key,
      required this.overlayMenuButtonKey,
      required this.isPromotionalScreen});

  @override
  State<ConnectPopupB2C> createState() => _ConnectPopupB2CState();
}

class _ConnectPopupB2CState extends State<ConnectPopupB2C> {
  final List<Rider> rider = [];

  @override
  void initState() {
    context.read<UserVehicleBloc>().add(SelectRiderEvent(null));
    super.initState();
  }

  @override
  Widget build(BuildContext buildContext) {
    Dimensions dimensions = Dimensions(context);
    if (buildContext
            .read<UserVehicleBloc>()
            .state
            .riders
            .any((e) => e.verificationStatus == VerificationStatus.pending) &&
        // ignore: unrelated_type_equality_checks
        VehicleStatusDataStream().vehicleStatus == VehicleStatus.disconnected) {
      ShowOverlay.getB2CConnectPopupOverlayEntry(
        context,
        getB2CConnectPopup(context, dimensions),
        widget.overlayMenuButtonKey,
      );
    }

    return IconButton(
        key: widget.overlayMenuButtonKey,
        onPressed: () {
          OverlayEntry? entry =
              context.read<EditRiderDropDownBloc>().state.overlayEntry;

          if (entry != null) {
            entry.remove();
            entry = null;
            context
                .read<EditRiderDropDownBloc>()
                .add(const EditRiderDropDownEvent(overlayEntry: null));
          } else {
            entry = ShowOverlay.getB2CConnectPopupOverlayEntry(
              buildContext,
              getB2CConnectPopup(buildContext, dimensions),
              widget.overlayMenuButtonKey,
            );
            buildContext
                .read<EditRiderDropDownBloc>()
                .add(EditRiderDropDownEvent(overlayEntry: entry));
          }
        },
        icon: SizedBox(
            width: 32 / 414 * dimensions.width,
            height: 32 / 896 * dimensions.height,
            child: Image.asset(
              b2cHomeScreenImages["connect"]!,
              fit: BoxFit.fill,
            )));
  }

  Widget getB2CConnectPopup(BuildContext context, Dimensions dimensions) {
    context.read<UserVehicleBloc>().add(LoadUserVehicleEvent());

    return Material(
      color: Colors.transparent,
      child: ConstrainedBox(
        constraints: BoxConstraints(
            minHeight: 100 / 896 * dimensions.height,
            maxHeight: 700 / 896 * dimensions.height),
        child: Container(
          width: 374 / 414 * dimensions.width,
          decoration: BoxDecoration(
              color: Theme.of(context).splashColor,
              borderRadius: BorderRadius.circular(8 / 414 * dimensions.width),
              boxShadow: const [
                BoxShadow(
                    color: Color.fromRGBO(0, 0, 0, 0.25),
                    offset: Offset(0, 0),
                    spreadRadius: 3,
                    blurRadius: 3)
              ]),
          padding: EdgeInsets.symmetric(
              horizontal: 16 / 414 * dimensions.width,
              vertical: 24 / 896 * dimensions.height),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    b2cHomeScreenText["popup_title"]!,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  Text(
                    b2cHomeScreenText["text1"]!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: 18 / 414 * dimensions.width,
                        fontWeight: FontWeight.w400),
                  ),
                ],
              ),
              SizedBox(
                height: 12 / 896 * dimensions.height,
              ),
              BlocBuilder<UserVehicleBloc, UserVehicleFetchState>(
                builder: (context, state) {
                  Widget widget = SizedBox(
                    height: 250 / 896 * dimensions.height,
                    child: Center(
                      child: Image.asset(
                        isTwoWheels
                            ? loaderGifImages['2Wheels']!
                            : loaderGifImages['3Wheels']!,
                      ),
                    ),
                  );
                  if (state.apiStatus == ApiStatus.success) {
                    widget = Flexible(
                      child: ListView.builder(
                        padding: const EdgeInsets.all(0),
                        shrinkWrap: true,
                        itemCount: state.riders.length,
                        itemBuilder: (context, index) {
                          return _buildUserTile(
                              state.riders[index], dimensions);
                        },
                      ),
                    );
                  }
                  return widget;
                },
              ),
              SizedBox(
                height: 28 / 896 * dimensions.height,
              ),
              BlocBuilder<UserVehicleBloc, UserVehicleFetchState>(
                builder: (context, state) {
                  Rider? rider;
                  Rider? connectedRider;
                  for (var e in state.riders) {
                    if (e.regNo == state.selectedRegNo) {
                      rider = e;
                    }
                    if (e.isConnected) {
                      connectedRider = e;
                    }
                  }
                  if (connectedRider != null && !connectedRider.isOwner) {
                    context
                        .read<UserVehicleBloc>()
                        .add(SelectRiderEvent(connectedRider.regNo));
                  }

                  bool isConnectedAndOwner =
                      currentVehicleStatus == VehicleStatus.connected &&
                          connectedRider != null &&
                          connectedRider.isOwner;

                  bool isRiderConnected =
                      (isConnectedAndOwner || !isConnectedAndOwner) &&
                          rider != null &&
                          connectedRider != null &&
                          connectedRider.imei == rider.imei;

                  bool isButtonEnabled = rider != null &&
                      //!isConnectedAndOwner &&
                      rider.verificationStatus != VerificationStatus.pending &&
                      rider.permissionStatus != RiderPermissionStatus.revoked &&
                      !rider.isOwner;

                  return Align(
                    alignment: Alignment.centerRight,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        ElevatedButton(
                          onPressed: () async {
                            OverlayEntry? entry = context
                                .read<EditRiderDropDownBloc>()
                                .state
                                .overlayEntry;
                            if (entry != null) {
                              entry.remove();
                              entry = null;
                              context.read<EditRiderDropDownBloc>().add(
                                  const EditRiderDropDownEvent(
                                      overlayEntry: null));
                            }
                          },
                          child: Text(
                            b2cHomeScreenText["button6"]!,
                            style: poppinsTextStyle(
                              16 / 414 * dimensions.width,
                              Colors.red,
                              FontWeight.w500,
                            ),
                          ),
                        ),
                        ElevatedButton(
                          onPressed: isButtonEnabled
                              ? () async {
                                  if (rider == null &&
                                      connectedRider == null &&
                                      state.riders.isEmpty) {
                                    CustomToast.message(toastMessageText[
                                        'connectRiderButton8']!);
                                  } else if (rider == null) {
                                    CustomToast.message(toastMessageText[
                                        'connectRiderButton2']!);
                                  } else if (rider.verificationStatus ==
                                      VerificationStatus.pending) {
                                    CustomToast.message(toastMessageText[
                                        'connectRiderButton1']!);
                                  } else if (rider.permissionStatus ==
                                      RiderPermissionStatus.revoked) {
                                    CustomToast.message(toastMessageText[
                                        'connectRiderButton3']!);
                                  } else if (rider.isOwner) {
                                    CustomToast.message(toastMessageText[
                                        'connectRiderButton4']!);
                                  } else {
                                    if (connectedRider != null &&
                                        !connectedRider.isOwner) {
                                      await DialogAction.disconnect.action(
                                        imei: rider.imei,
                                        context: context,
                                      );
                                    } else {
                                      await DialogAction.connect.action(
                                        imei: rider.imei,
                                        context: context,
                                      );
                                    }
                                  }
                                }
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isButtonEnabled
                                ? (isRiderConnected
                                    ? colorError600
                                    : colorGreenSuccess)
                                : colorGrey400,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                          child: Text(
                            isRiderConnected
                                ? b2cHomeScreenText["button5"]!
                                : b2cHomeScreenText["button1"]!,
                            style: poppinsTextStyle(
                              16 / 414 * dimensions.width,
                              colorWhite,
                              FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserTile(Rider rider, Dimensions dimensions) {
    return BlocBuilder<UserVehicleBloc, UserVehicleFetchState>(
      builder: (context, state) {
        List<Rider> riders = context.read<UserVehicleBloc>().state.riders;
        bool unownVehicleConnected = false;
        for (Rider rider in riders) {
          if (!rider.isOwner && rider.isConnected) {
            unownVehicleConnected = true;
          }
        }
        if (state.riders.isEmpty) {
          CustomToast.message(toastMessageText['connectRiderButton8']!);
        }
        return InkWell(
          splashColor: colorGrey300,
          onTap: () {
            if (rider.permissionStatus == RiderPermissionStatus.revoked) {
              CustomToast.message(toastMessageText['connectRiderButton3']!);
            } else if (!rider.isOwner &&
                !(rider.verificationStatus == VerificationStatus.pending)) {
              context.read<UserVehicleBloc>().add(SelectRiderEvent(
                  context.read<UserVehicleBloc>().state.selectedRegNo ==
                          rider.imei
                      ? null
                      : rider.regNo));
            }
          },
          child: Container(
            /*width: 342 / 414 * dimensions.width,
            height: (rider.verificationStatus == VerificationStatus.pending
                    ? 116
                    : 66) /
                896 *
                dimensions.height,*/
            margin: EdgeInsets.symmetric(vertical: 4 / 896 * dimensions.height),
            padding: EdgeInsets.symmetric(
                horizontal: 15 / 414 * dimensions.width,
                vertical: 10 / 896 * dimensions.height),
            decoration: BoxDecoration(
              color: MyApp.of(context).getCurrentThemeMode() == ThemeMode.light
                  ? colorGrey100
                  : colorGrey900,
              borderRadius: BorderRadius.circular(8 / 414 * dimensions.width),
              border: (rider.isConnected && !rider.isOwner) ||
                      (!unownVehicleConnected && rider.isOwner)
                  ? Border.all(width: 1, color: colorSuccessGreen500)
                  : state.selectedRegNo == rider.regNo
                      ? Border.all(width: 1, color: colorGrey400)
                      : Border.all(width: 0, color: Colors.transparent),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Stack(
                      alignment: Alignment.topRight,
                      children: [
                        SizedBox(
                          width: 32 / 414 * dimensions.width,
                          height: 32 / 896 * dimensions.height,
                          child: rider.profileUrl == null
                              ? const CircleAvatar(child: Icon(Icons.person))
                              : ProfileImage(
                                  imageUrl: rider.profileUrl,
                                  isHomeProfile: false,
                                ),
                        ),
                        Align(
                          alignment: Alignment.topRight,
                          child: Icon(Icons.circle,
                              color: rider.permissionStatus ==
                                      RiderPermissionStatus.granted
                                  ? colorGreenSuccess
                                  : colorRed600,
                              size: 10),
                        ),
                      ],
                    ),
                    SizedBox(width: 12 / 414 * dimensions.width),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          rider.ownerName,
                          style: Theme.of(context)
                              .textTheme
                              .labelMedium
                              ?.copyWith(fontWeight: FontWeight.w400),
                        ),
                        Text(
                          rider.regNo,
                          style: Theme.of(context)
                              .textTheme
                              .labelSmall
                              ?.copyWith(fontWeight: FontWeight.w400),
                        ),
                      ],
                    ),
                    const Expanded(child: SizedBox.shrink()),
                    Visibility(
                      visible: rider.verificationStatus ==
                          VerificationStatus.verified,
                      child: rider.isOwner
                          ? Text(b2cHomeScreenText["text2"]!,
                              style: Theme.of(context).textTheme.labelMedium)
                          : Icon(
                              size: 18 / 414 * dimensions.width,
                              rider.permissionStatus ==
                                      RiderPermissionStatus.granted
                                  ? Icons.check_circle
                                  : Icons.cancel,
                              color: rider.permissionStatus ==
                                      RiderPermissionStatus.granted
                                  ? Colors.green
                                  : Colors.red,
                            ),
                    ),
                  ],
                ),
                Visibility(
                    visible:
                        rider.verificationStatus == VerificationStatus.pending,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        ElevatedButton(
                          onPressed: () {
                            context.read<UserVehicleBloc>().add(
                                AcceptVehicleInviteEvent(
                                    rider.regNo, VerificationStatus.verified));
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: colorGreenSuccess,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                          child: Text(
                            b2cHomeScreenText["button3"]!,
                            style: poppinsTextStyle(12 / 414 * dimensions.width,
                                colorWhite, FontWeight.w500),
                          ),
                        ),
                        SizedBox(width: 12 / 414 * dimensions.width),
                        ElevatedButton(
                          onPressed: () {
                            context.read<UserVehicleBloc>().add(
                                AcceptVehicleInviteEvent(
                                    rider.regNo, VerificationStatus.rejected));
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: colorError600,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                          child: Text(
                            b2cHomeScreenText["button4"]!,
                            style: poppinsTextStyle(12 / 414 * dimensions.width,
                                colorWhite, FontWeight.w500),
                          ),
                        ),
                      ],
                    ))
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    final bloc =
        context.read<EditRiderDropDownBloc>(); // Get the bloc reference early.
    OverlayEntry? entry = bloc.state.overlayEntry;

    if (entry != null) {
      entry.remove();
      entry = null;
      bloc.add(const EditRiderDropDownEvent(overlayEntry: null));
    }
    super.dispose(); // Call super.dispose at the end.
  }
}

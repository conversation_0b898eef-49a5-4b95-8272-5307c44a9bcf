import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nds_app/common/image_urls.dart';

class BatteryIndicator extends StatelessWidget {
  final int percentage;
  final bool isBatteryRemoved;

  const BatteryIndicator(
      {super.key, required this.percentage, required this.isBatteryRemoved});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        LayoutBuilder(builder: (context, constraints) {
          double parentWidth = constraints.maxWidth;
          // Calculate the actual percentage (clamped between 0-100)
          final clampedPercentage = percentage.clamp(0, 100);
          final displayPercentage = clampedPercentage / 100;

          Widget widget = SizedBox(
            width: parentWidth,
            child: Stack(
              children: [
                SvgPicture.asset(
                  b2cHomeScreenImages["battery_bg"]!,
                  width: parentWidth,
                  fit: BoxFit.cover,
                ),
                FractionallySizedBox(
                    widthFactor: displayPercentage, // Use the calculated percentage
                    child: SvgPicture.asset(
                      b2cHomeScreenImages["battery_full"]!,
                      fit: BoxFit.cover,
                      alignment: Alignment.centerLeft,
                      width: parentWidth,
                    )),
              ],
            ),
          );

          if (isBatteryRemoved) {
            widget = SizedBox(
              width: parentWidth,
              child: Stack(
                children: [
                  SvgPicture.asset(
                    b2cHomeScreenImages["battery_removed"]!,
                    width: parentWidth,
                    fit: BoxFit.cover,
                  ),
                ],
              ),
            );
          }
          return widget;
        }),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/strings.dart';

class BatteryStatusAlert extends StatelessWidget {
  final String message;

  const BatteryStatusAlert({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      alignment: Alignment.topCenter,
      title: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Image.asset(
                    clusterTitleRowCompanyLogo,
                    width: 20,
                    height: 20,
                  ),
                  const SizedBox(width: 7),
                  Text(afterConnectionCompanyLabel,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      )),
                ],
              ),
              Text(homeScreenText["alert_time"]!,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  )),
            ],
          ),
          const SizedBox(height: 8),
          Column(
            children: [
              Text(message,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  )),
            ],
          ),
        ],
      ),
      // actions: <Widget>[
      //   TextButton(
      //     child: const Text('OK'),
      //     onPressed: () {
      //       Navigator.of(context).pop();
      //     },
      //   ),
      // ],
    );
  }
}

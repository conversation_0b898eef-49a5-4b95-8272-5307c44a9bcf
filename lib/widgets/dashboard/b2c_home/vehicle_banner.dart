import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/utils/oval_shape_vehicle_base.dart';
import '../../../common/image_urls.dart';
import 'package:cached_network_image/cached_network_image.dart';

class VehicleBanner extends StatelessWidget {
  final String homeUserDiplayName;
  final String imageUrl;
  const VehicleBanner(
      {super.key, required this.homeUserDiplayName, required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    Color color = getColor();
    Dimensions dimensions = Dimensions(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        SizedBox(
          height: 350 / 896 * dimensions.height,
          width: 350 / 414 * dimensions.width,
          child: Stack(
            children: [
              Positioned(
                bottom: 15 / 896 * dimensions.height,
                left: -80 / 414 * dimensions.width,
                child: ClipRect(
                  clipBehavior: Clip.none,
                  child: OvalShapeBase(
                    width: 379.18 / 414 * dimensions.width,
                    height: 105.13 / 896 * dimensions.height,
                    gradientColors: [colorWhite, color],
                  ),
                ),
              ),
              Positioned(
                top: 20 / 896 * dimensions.height,
                left: -40 / 414 * dimensions.width,
                child: SizedBox(
                  height: 300 / 896 * dimensions.height,
                  width: 300 / 414 * dimensions.width,
                  child: CachedNetworkImage(
                    imageUrl: imageUrl.isNotEmpty ? imageUrl : bikeImages['blue_bike']!,
                    fit: BoxFit.fill,
                    memCacheWidth: 450,
                    maxWidthDiskCache: 450,
                    fadeInDuration: const Duration(milliseconds: 100),
                    placeholder: (context, url) => const SizedBox(
                      width: 30,
                      height: 30,
                      child: CircularProgressIndicator(strokeWidth: 2.0),
                    ),
                    errorWidget: (context, url, error) => Image.asset(bikeImages['blue_bike']!),
                  ),
                ),
              ),
              Positioned(
                  top: 60 / 896 * dimensions.height,
                  right: 20 / 414 * dimensions.width,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      ShaderMask(
                          blendMode: BlendMode.srcIn,
                          shaderCallback: (bounds) =>
                              const LinearGradient(colors: [
                                Color.fromRGBO(237, 28, 36, 1),
                                Color.fromRGBO(244, 117, 33, 1),
                              ]).createShader(
                                Rect.fromLTWH(
                                    0, 0, bounds.width, bounds.height),
                              ),
                          child: Container(
                            alignment: Alignment.centerRight,
                            width: 0.48 * dimensions.width,
                            child: Text(
                              
                                homeScreenText["user_name"]!
                                    .replaceAll("@value", homeUserDiplayName),
                                    textAlign:TextAlign.right ,
                                textScaler: MediaQuery.textScalerOf(context)
                                    .clamp(
                                        minScaleFactor: 0, maxScaleFactor: 1),
                                style: TextStyle(
                                    fontSize: 16.0 / 414 * dimensions.width,
                                    fontWeight: FontWeight.w600)),
                          )),
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Text(
                          afterConnectionCompanyLabel,
                          style: Theme.of(context).textTheme.labelSmall,
                        ),
                      ),
                    ],
                  ))
            ],
          ),
        ),
      ],
    );
  }

  Color getColor() {
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";
    Color color =
        hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;

    return color;
  }
}

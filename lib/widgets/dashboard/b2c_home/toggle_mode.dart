import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/riding_modes.dart';

class ToggleMode extends StatefulWidget {
  final RidingModes currentMode;
  final List<RidingModes> rideModes;
  const ToggleMode(
      {super.key, required this.currentMode, required this.rideModes});

  @override
  State<ToggleMode> createState() => _ToggleModeState();
}

class _ToggleModeState extends State<ToggleMode> {
  late Color color = colorGrey800;
  int selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return Container(
      height: 24 / 896 * dimensions.height,
      decoration: BoxDecoration(
          color: const Color.fromRGBO(59, 62, 66, 1),
          borderRadius: BorderRadius.circular(20.0)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [...getModes(dimensions)],
      ),
    );
  }

  List<Widget> getModes(Dimensions dimensions) {
    List<Widget> modes = [];
    for (var element in widget.rideModes) {
      if (element == widget.currentMode) {
        modes.add(_buildToggleButton(element.name, true, dimensions));
      } else {
        modes.add(_buildToggleButton(element.name, false, dimensions));
      }
    }

    return modes;
  }

  Widget _buildToggleButton(
      String mode, bool isCurrentMode, Dimensions dimensions) {
    return Container(
      height: 24 / 896 * dimensions.height,
      padding: EdgeInsets.symmetric(horizontal: 8 / 414 * dimensions.width),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: isCurrentMode ? colorGrey25 : Colors.transparent,
        borderRadius: BorderRadius.circular(20.0 / 414 * dimensions.width),
      ),
      child: Text((isCurrentMode ? mode : mode.substring(0, 1)).toUpperCase(),
          style: isCurrentMode
              ? poppinsTextStyle(
                  12 / 414 * dimensions.width, colorGrey800, FontWeight.w400)
              : poppinsTextStyle(
                  12 / 414 * dimensions.width, colorGrey25, FontWeight.w400)),
    );
  }
}

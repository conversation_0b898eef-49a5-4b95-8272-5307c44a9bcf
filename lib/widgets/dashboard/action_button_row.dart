import 'dart:core';

import 'package:flutter/material.dart';

import 'package:flutter_svg/svg.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/company/factoryFiles/cluster_factory.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/widgets/common/progress_indicator.dart';
import 'package:nds_app/widgets/dashboard/b2c_home/connect_pop_up.dart';
import 'package:nds_app/widgets/object_detection/object_detection_screen.dart';

/// Reusable ActionButtonRow widget that works for all company
/// This replaces the need for separate company-specific action button row templates
class ActionButtonRow extends StatefulWidget {
  final bool isConnected;
  final Function connectAction;
  final BuildContext context;
  final String userName;
  final bool isDashboardActionRow;

  const ActionButtonRow({
    super.key,
    required this.isConnected,
    required this.connectAction,
    required this.context,
    required this.userName,
    required this.isDashboardActionRow,
  });

  @override
  State<ActionButtonRow> createState() => _ActionButtonRowState();
}

class _ActionButtonRowState extends State<ActionButtonRow> {
  @override
  Widget build(BuildContext context) {
    ThemeMode themeMode = MyApp.of(context).getCurrentThemeMode();
    Dimensions dimensions = Dimensions(context);
    final GlobalKey key = GlobalKey();

    return Padding(
      padding: EdgeInsets.only(
        top: 28 / 896 * dimensions.height,
        left: 12 / 414 * dimensions.width,
        right: 20 / 414 * dimensions.width,
      ),
      child: widget.isDashboardActionRow
          ? Stack(
              children: [
                getDarkModeOrLightModeIcon(dimensions, themeMode),
                Visibility(
                  visible: widget.isConnected,
                  child: Align(
                    alignment: Alignment.center,
                    child: InkWell(
                      onTap: () async {
                        getCircularProgressIndicator(context);
                        LogScreenTrackingEvent onTapActionTracking =
                            LogScreenTrackingEvent();
                        onTapActionTracking.logScreenView(
                            eventName: trackingLabels['ClusterButtonAction']!);

                        int count = 0;
                        // ignore: use_build_context_synchronously
                        Navigator.pushAndRemoveUntil(
                            // ignore: use_build_context_synchronously
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  ClusterFactory.createCluster(
                                vehicleInfo: vehicleInfoConstant!,
                                userName: widget.userName,
                              ),
                            ),
                            (r) => count++ >= 1);
                      },
                      child: SizedBox(
                          width: 82 / 414 * dimensions.width,
                          height: 36 / 896 * dimensions.height,
                          child: SvgPicture.asset(
                            homeScreenImages["cluster_icon"]!,
                            fit: BoxFit.contain,
                          )),
                    ),
                  ),
                ),
                // Object Detection Button
                Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding:
                        EdgeInsets.only(right: 50 / 414 * dimensions.width),
                    child: InkWell(
                      onTap: () {
                        LogScreenTrackingEvent onTapActionTracking =
                            LogScreenTrackingEvent();
                        onTapActionTracking.logScreenView(
                            eventName: 'ObjectDetectionButtonAction');

                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ObjectDetectionScreen(),
                          ),
                        );
                      },
                      child: SizedBox(
                        width: 36 / 414 * dimensions.width,
                        height: 36 / 896 * dimensions.height,
                        child: Image.asset(
                          homeScreenImages["object_detection"]!,
                          fit: BoxFit.fill,
                        ),
                      ),
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: !(isB2CUser || isLapaUser) && !isProdRedLeadership
                      ? InkWell(
                          onTap: () async {
                            await widget.connectAction.call();
                          },
                          child: SizedBox(
                            width: 36 / 414 * dimensions.width,
                            height: 36 / 896 * dimensions.height,
                            child: Image.asset(
                              widget.isConnected
                                  ? homeScreenImages["disconnect"]!
                                  : homeScreenImages["connect"]!,
                              fit: BoxFit.fill,
                            ),
                          ),
                        )
                      : (isB2CUser || isLapaUser) && !isProdRedLeadership
                          ? ConnectPopupB2C(
                              overlayMenuButtonKey: key,
                              isPromotionalScreen: (isB2CUser || isLapaUser),
                            )
                          : const SizedBox
                              .shrink(), // Hide for leadership users
                ),
              ],
            )
          : getDarkModeOrLightModeIcon(dimensions, themeMode),
    );
  }

  Widget getDarkModeOrLightModeIcon(
    Dimensions dimensions,
    ThemeMode themeMode,
  ) {
    return Align(
      alignment: Alignment.centerLeft,
      child: InkWell(
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: () async {
          themeMode = MyApp.of(context).getCurrentThemeMode();
          if ([ThemeMode.light, ThemeMode.system].contains(themeMode)) {
            MyApp.of(context).changeTheme(ThemeMode.dark);
          } else {
            MyApp.of(context).changeTheme(ThemeMode.light);
          }
          setState(() {});
        },
        child: SizedBox(
          width: 42 / 414 * dimensions.width,
          height: 42 / 896 * dimensions.height,
          child: Image.asset(homeScreenImages[
              [ThemeMode.light, ThemeMode.system].contains(themeMode)
                  ? "night_mode"
                  : "day_mode"]!),
        ),
      ),
    );
  }
}

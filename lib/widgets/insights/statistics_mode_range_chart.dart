import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_blocs.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/constant/riding_modes.dart';
import 'package:nds_app/models/enums/date_time_type.dart';
import 'package:nds_app/models/enums/unit.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class StatisticsModeRangeChart extends StatelessWidget {
  const StatisticsModeRangeChart({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return BlocBuilder<StatisticsBloc, StatisticsState>(
      builder: (context, state) {
        Map<String, List<Map<DateTime, double>>> modeData =
            getModeRangeDataPoints(state);

        return Container(
          width: 374 / 414 * dimensions.width,
          height: 333 / 896 * dimensions.height,
          padding: EdgeInsets.symmetric(
            horizontal: 12 / 414 * dimensions.width,
            vertical: 36 / 896 * dimensions.height,
          ),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            border: Border.all(color: colorGrey300),
            borderRadius: BorderRadius.circular(30 / 414 * dimensions.width),
          ),
          child: SfCartesianChart(
            primaryXAxis: DateTimeAxis(
              edgeLabelPlacement: EdgeLabelPlacement.shift,
              isVisible: true,
              labelFormat: '{value}',
              dateFormat: state.dateTimeType == DateTimeType.day
                  ? DateFormat('hh:mma')
                  : DateFormat('d MMM'),
              majorGridLines: const MajorGridLines(width: 0),
              axisLine: const AxisLine(width: 0),
              minimum: state.startTime,
              maximum: state.endTime,
              intervalType: state.dateTimeType == DateTimeType.day
                  ? DateTimeIntervalType.hours
                  : DateTimeIntervalType.days,
            ),
            primaryYAxis: NumericAxis(
              labelFormat: '{value} ${Unit.distance.unit2}',
              majorGridLines: const MajorGridLines(
                width: 1,
                dashArray: <double>[4, 4],
                color: colorGrey300,
              ),
              axisLine: const AxisLine(width: 0),
              isVisible: true,
            ),
            plotAreaBorderWidth: 0,
            series: getModeSeries(modeData),
          ),
        );
      },
    );
  }

  Map<String, List<Map<DateTime, double>>> getModeRangeDataPoints(
      StatisticsState state) {
    Map<int, Map<String, double>> data =
        state.statisticsDetails.modeRangeDataPoints ?? {};

    if (data.length == 1) {
      data.updateAll(
        (key, value) {
          value.updateAll(
            (key, range) {
              return range.round().toDouble();
            },
          );
          return value;
        },
      );
    }

    Map<String, List<Map<DateTime, double>>> modeData = {};

    var sortedEntries = data.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));

    for (var entry in sortedEntries) {
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(entry.key);

      entry.value.forEach((mode, range) {
        if (!modeData.containsKey(mode)) {
          modeData[mode] = [];
        }
        modeData[mode]!.add({dateTime: range});
      });
    }
    return modeData;
  }

  List<LineSeries<Map<DateTime, double>, DateTime>> getModeSeries(
      Map<String, List<Map<DateTime, double>>> modeData) {
    List<LineSeries<Map<DateTime, double>, DateTime>> series = [];

    modeData.forEach((mode, dataPoints) {
      series.add(
        LineSeries<Map<DateTime, double>, DateTime>(
          name: mode,
          color: getModeColor(mode),
          dataSource: dataPoints,
          xValueMapper: (Map<DateTime, double> data, _) => data.keys.first,
          yValueMapper: (Map<DateTime, double> data, _) => data.values.first,
          markerSettings: const MarkerSettings(isVisible: true),
        ),
      );
    });

    return series;
  }

  Color getModeColor(String mode) {
    try {
      return RidingModes.values
          .firstWhere((e) => e.name.toLowerCase() == mode.toLowerCase())
          .color
          .toColor();
    } catch (e) {
      // Default to eco mode color if mode not found
      return RidingModes.eco.color.toColor();
    }
  }
}

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../common/dimensions.dart';
import '../../common/strings.dart';

class FuelCostChart extends StatelessWidget {
  final int saved;
  final int equivalent;
  final int spent;

  const FuelCostChart({
    super.key,
    required this.saved,
    required this.equivalent,
    required this.spent,
  });

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    // Check if there is data to display
    bool hasData = saved != 0 || spent != 0;

    List<ChartSampleData> chartData = [
      ChartSampleData(
          insightsText['text19']!, saved.toDouble(), fuelChartPetrol),
      ChartSampleData(insightsText['text20']!, spent.toDouble(), fuelChartEV),
    ];

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 8 / 414 * dimensions.width,
      ),
      margin: EdgeInsets.only(
          bottom: 6 / 414 * dimensions.height,
          left: 6 / 414 * dimensions.width,
          right: 6 / 414 * dimensions.width),
      decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1, color: colorGrey200),
          boxShadow: [
            BoxShadow(
                color: colorBlack.withOpacity(0.25),
                offset: const Offset(1, 3),
                blurRadius: 3,
                spreadRadius: 1),
            BoxShadow(
                color: colorWhite.withOpacity(0.25),
                offset: const Offset(-1, -3),
                blurRadius: 3,
                spreadRadius: 1)
          ]),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: hasData
            ? _buildChartContent(chartData, dimensions, context)
            : _buildNoDataContent(context, dimensions),
      ),
    );
  }

  Widget _buildNoDataContent(BuildContext context, Dimensions dimensions) {
    return SizedBox(
      height: 180,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.bar_chart_outlined,
              size: 48,
              color: colorGrey400,
            ),
            const SizedBox(height: 16),
            Text(
              insightsText['text22']!,
              style: poppinsTextStyle(
                16 / 414 * dimensions.width,
                colorGrey600,
                FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartContent(List<ChartSampleData> chartData,
      Dimensions dimensions, BuildContext context) {
    return Column(
      children: [
        SfCircularChart(
          annotations: <CircularChartAnnotation>[
            CircularChartAnnotation(
              widget: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AutoSizeText(
                    equivalent.toStringAsFixed(0),
                    maxFontSize: 28,
                    minFontSize: 10,
                    maxLines: 1,
                    style: poppinsTextStyle(28 / 414 * dimensions.width,
                        colorBlueFuelTile, FontWeight.w700),
                  ),
                  Text(
                    insightsText['text21']!,
                    style: poppinsTextStyle(10 / 414 * dimensions.width,
                        colorBlack800, FontWeight.w500),
                  ),
                ],
              ),
            ),
          ],
          series: <CircularSeries<ChartSampleData, String>>[
            DoughnutSeries<ChartSampleData, String>(
              dataSource: chartData,
              pointColorMapper: (ChartSampleData data, _) => data.color,
              xValueMapper: (ChartSampleData data, _) => data.category,
              yValueMapper: (ChartSampleData data, _) => data.amount,
              dataLabelMapper: (ChartSampleData data, _) =>
                  data.amount.toStringAsFixed(0),
              dataLabelSettings: const DataLabelSettings(
                  isVisible: true,
                  labelPosition: ChartDataLabelPosition.outside),
              explode: true,
              explodeIndex: 0,
              innerRadius: '75%',
            ),
          ],
        ),
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildLegendItem(insightsText['text19']!, fuelChartPetrol),
            const SizedBox(width: 20),
            _buildLegendItem(insightsText['text20']!, fuelChartEV),
          ],
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  Widget _buildLegendItem(String title, Color color) {
    return Row(
      children: [
        CircleAvatar(radius: 8, backgroundColor: color),
        const SizedBox(width: 6),
        Text(
          title,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
        ),
      ],
    );
  }
}

class ChartSampleData {
  final String category;
  final double amount;
  final Color color;

  ChartSampleData(this.category, this.amount, this.color);
}

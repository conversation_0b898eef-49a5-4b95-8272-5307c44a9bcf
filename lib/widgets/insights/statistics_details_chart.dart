import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_blocs.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/models/enums/date_time_type.dart';
import 'package:nds_app/models/enums/statistics_data_type.dart';
import 'package:nds_app/models/enums/unit.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class StatisticsDetailsChart extends StatelessWidget {
  const StatisticsDetailsChart({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return BlocBuilder<StatisticsBloc, StatisticsState>(
      builder: (context, state) {
        List<Map<DateTime, dynamic>> chartData = getPoints(state);
        return Container(
          width: 374 / 414 * dimensions.width,
          height: 333 / 896 * dimensions.height,
          padding: EdgeInsets.symmetric(
            horizontal: 12 / 414 * dimensions.width,
            vertical: 36 / 896 * dimensions.height,
          ),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            border: Border.all(color: colorGrey300),
            borderRadius: BorderRadius.circular(30 / 414 * dimensions.width),
          ),
          child: SfCartesianChart(
            primaryXAxis: DateTimeAxis(
              edgeLabelPlacement: EdgeLabelPlacement.shift,
              isVisible: true,
              labelFormat: '{value}',
              dateFormat: state.dateTimeType == DateTimeType.day
                  ? DateFormat('hh:mma')
                  : DateFormat('d MMM'),
              majorGridLines: const MajorGridLines(
                width: 0,
              ),
              axisLine: const AxisLine(width: 0),
              minimum: state.startTime,
              maximum: state.endTime, // Default to current time
              intervalType: state.dateTimeType == DateTimeType.day
                  ? DateTimeIntervalType.hours
                  : DateTimeIntervalType.days,
            ),
            primaryYAxis: NumericAxis(
              labelFormat: '{value} ${getValueUnit(state)}',
              majorGridLines: const MajorGridLines(
                width: 1,
                dashArray: <double>[4, 4],
                color: colorGrey300,
              ),
              axisLine: const AxisLine(width: 0),
              isVisible: true,
            ),
            plotAreaBorderWidth: 0,
            series: <LineSeries>[
              LineSeries<Map<DateTime, dynamic>, DateTime>(
                dataSource: chartData,
                xValueMapper: (Map<DateTime, dynamic> data, _) =>
                    data.entries.first.key,
                yValueMapper: (Map<DateTime, dynamic> data, _) =>
                    data.entries.first.value,
                markerSettings: const MarkerSettings(isVisible: true),
              ),
            ],
          ),
        );
      },
    );
  }

  String getValueUnit(StatisticsState state) {
    final value = state.statisticsDetails.value;

    String unit = "";

    switch (state.statisticsDataType) {
      case StatisticsDataType.distanceTravelled:
        unit = Unit.distance.unit2;
        break;
      case StatisticsDataType.rideDuration:
      case StatisticsDataType.chargingTime:
        unit = (value != null && value > 60)
            ? Unit.duration.unit2
            : Unit.duration.unit1;
        break;
      case StatisticsDataType.avgSpeed:
      case StatisticsDataType.maxSpeed:
        unit = Unit.speed.unit2;
        break;
      default:
        break;
    }
    return unit;
  }

  List<Map<DateTime, dynamic>> getPoints(StatisticsState state) {
    List<Map<DateTime, dynamic>> chartData = [];
    Map<int, dynamic> points = state.statisticsDetails.dataPoints ?? {};
    if (points.length == 1) {
      points.updateAll((key, value) {
        dynamic roundValue = value;
        if (value is double) {
          roundValue = roundValue.round();
        }
        return roundValue;
      });
    }

    List<MapEntry<int, dynamic>> sortedEntries = points.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));
    double cumulativeValue = 0;

    for (var entry in sortedEntries) {
      int epochTime = entry.key;
      dynamic value = entry.value;
      if ([StatisticsDataType.rideDuration, StatisticsDataType.chargingTime]
          .contains(state.statisticsDataType)) {
        int divideFactor = 1;
        if (state.statisticsDetails.value != null &&
            state.statisticsDetails.value > 60) {
          divideFactor = 60;
        }

        value = value / divideFactor;
      }
      if ([StatisticsDataType.avgSpeed, StatisticsDataType.maxSpeed]
          .contains(state.statisticsDataType)) {
        if (value is double) {
          cumulativeValue = value;
        } else if (value is int) {
          cumulativeValue = value.toDouble();
        }
      } else {
        cumulativeValue += value;
      }
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(epochTime);

      chartData.add({dateTime: cumulativeValue});
    }

    return chartData;
  }
}

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/utils/extension.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../common/colors.dart';
import '../../common/constant.dart';
import '../../common/dimensions.dart';
import '../../common/image_urls.dart';
import '../../common/shared_preferences_keys.dart';
import '../../common/strings.dart';
import '../../constant/action.dart';
import '../../constant/api_urls.dart';
import '../../constant/vehicle_status.dart';
import '../../models/user_info.dart';
import '../../models/vehicle_info.dart';
import '../../services/api_service.dart';
import '../../streams/vehicle_data.dart';
import '../../streams/vehicle_status_data.dart';
import '../common/common_widget.dart';
import 'fuel_pie_chart_screen.dart';

class FuelSavingsScreen extends StatefulWidget {
  const FuelSavingsScreen({super.key});

  @override
  State<FuelSavingsScreen> createState() => _FuelSavingsScreenState();
}

class _FuelSavingsScreenState extends State<FuelSavingsScreen> {
  late Future<UserInfo?> vehicleInfoFuture;
  VehicleInfo vehicleInfo = VehicleInfo();
  VehicleStatusDataStream vehicleStatusDataStream = VehicleStatusDataStream();
  VehicleDataStream vehicleDataStream = VehicleDataStream();

  @override
  void initState() {
    super.initState();
    vehicleInfoFuture = loadData();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return FutureBuilder<UserInfo?>(
      future: vehicleInfoFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: Image.asset(
              isTwoWheels
                  ? loaderGifImages['2Wheels']!
                  : loaderGifImages['3Wheels']!,
            ),
          );
        } else if (snapshot.hasError) {
          return const Center(child: Text("Error loading data"));
        } else if (!snapshot.hasData) {
          return const Center(child: Text("No data available"));
        }

        UserInfo? userInfo = snapshot.data;
        int spent = userInfo?.spent ?? 0;
        int equivalent = userInfo?.equivalent ?? 0;
        int saved = equivalent - spent;
        Color color = getColor();

        return SingleChildScrollView(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              getFuelSavingDetailsContainer(
                () {},
                color,
                dimensions,
                saved,
                equivalent,
                spent,
                context,
              ),
              SizedBox(height: 16 / 896 * dimensions.height),
              Text(
                insightsText['text18']!,
                style: Theme.of(context).textTheme.headlineLarge,
              ),
              SizedBox(height: 16 / 896 * dimensions.height),
              FuelCostChart(equivalent: equivalent, saved: saved, spent: spent),
              SizedBox(height: 100 / 896 * dimensions.height),
            ],
          ),
        );
      },
    );
  }

  Future<UserInfo?> loadData() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    JsonDecoder decoder = const JsonDecoder();

    http.Response userInfoResponse =
        await BackendApi.initiateGetCall(ApiUrls.userInfo);
    Map<String, dynamic> userResponse = decoder.convert(userInfoResponse.body);
    UserInfo? userInfo = UserInfo.fromJson(userResponse);

    if (userInfo.connectedVehicleImei != null) {
      pref.setString(connectedVehicleImeiNo, userInfo.connectedVehicleImei!);
      currentVehicleStatus = VehicleStatus.connected;
      int statusCode = await DialogAction.vehicleInfo
          .action(imei: userInfo.connectedVehicleImei);

      if (statusCode != 200) {
        isVehicleInfoAlertMessageExist = true;
      }

      vehicleStatusDataStream
          .updateVehicleStatusResponse(VehicleStatus.connected);
      vehicleInfo = vehicleInfoConstant!;
      vehicleDataStream.updateVehicleInfo(vehicleInfo);
    }

    return userInfo;
  }

  Color getColor() {
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";
    return hexColorInStr.isNotEmpty && !(isB2CUser || isLapaUser)
        ? hexColorInStr.toColor()
        : colorGrey800;
  }
}

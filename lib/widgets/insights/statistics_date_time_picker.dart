import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:nds_app/blocs/insight/statistics/day_event.dart';
import 'package:nds_app/blocs/insight/statistics/load_statistics_details_event.dart';
import 'package:nds_app/blocs/insight/statistics/load_statistics_event.dart';
import 'package:nds_app/blocs/insight/statistics/month_event.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_blocs.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_state.dart';
import 'package:nds_app/blocs/insight/statistics/week_event.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/models/enums/date_time_type.dart';
import 'package:nds_app/models/enums/statistics_data_type.dart';

class StatisticsDateTimePicker extends StatefulWidget {
  const StatisticsDateTimePicker({super.key});

  @override
  State<StatisticsDateTimePicker> createState() =>
      _StatisticsDateTimePickerState();
}

class _StatisticsDateTimePickerState extends State<StatisticsDateTimePicker> {
  final dayKey = GlobalKey();
  final weekKey = GlobalKey();
  final monthKey = GlobalKey();
  DateTime? selectedDateTime;
  bool isFirst = true;
  DateFormat monthFormatter = DateFormat('MMM');
  DateFormat dayMonthFormatter = DateFormat('d MMM');

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return SizedBox(
      width: 374 / 414 * dimensions.width,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            getDatePickerContainer(
                DateTimeType.day, dimensions, 95, 170, dayKey),
            getDatePickerContainer(
                DateTimeType.week, dimensions, 95, 240, weekKey),
            getDatePickerContainer(
                DateTimeType.month, dimensions, 95, 170, monthKey)
          ],
        ),
      ),
    );
  }

  getDatePickerContainer(DateTimeType type, Dimensions dimensions,
      double labelContainerWidth, double dateTimePickerWidth, GlobalKey key) {
    return BlocBuilder<StatisticsBloc, StatisticsState>(
      key: key,
      builder: (context, state) {
        if (state.dateTimeType == type && isFirst == true) {
          Future.delayed(const Duration(seconds: 1), () {
            switch (type) {
              case DateTimeType.day:
                Scrollable.ensureVisible(dayKey.currentContext!);
                break;
              case DateTimeType.week:
                Scrollable.ensureVisible(weekKey.currentContext!);

                break;
              case DateTimeType.month:
                Scrollable.ensureVisible(monthKey.currentContext!);

                break;
            }
          });
          isFirst = false;
        }
        return SizedBox(
          width: state.dateTimeType == type
              ? (labelContainerWidth + dateTimePickerWidth + 8) /
                  414 *
                  dimensions.width
              : (labelContainerWidth + 24) / 414 * dimensions.width,
          height: 44 / 896 * dimensions.height,
          child: Stack(
            children: [
              Positioned(
                left: 80 / 414 * dimensions.width,
                child: Visibility(
                  visible: state.dateTimeType == type,
                  child: Container(
                    width: dateTimePickerWidth / 414 * dimensions.width,
                    height: 42 / 896 * dimensions.height,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        color: Theme.of(context).scaffoldBackgroundColor,
                        border: Border.all(color: Theme.of(context).cardColor),
                        boxShadow: [
                          BoxShadow(
                              color: colorWhite.withOpacity(0.25),
                              offset: const Offset(-2, -2),
                              blurRadius: 1,
                              spreadRadius: 0),
                          BoxShadow(
                              color: colorBlack.withOpacity(0.25),
                              offset: const Offset(1, 1),
                              blurRadius: 1,
                              spreadRadius: 0),
                        ],
                        borderRadius:
                            BorderRadius.circular(8 / 414 * dimensions.width)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 20 / 414 * dimensions.width,
                        ),
                        InkWell(
                          onTap: () {
                            dateTimePreviousOrNextActionOrOnTapAction(
                                state, false, context, type);
                          },
                          child: Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: 4.0 / 414 * dimensions.width,
                                  horizontal: 10 / 896 * dimensions.height),
                              child: SizedBox(
                                width: 16 / 414 * dimensions.width,
                                height: 16 / 414 * dimensions.width,
                                child: FittedBox(
                                  child: Icon(
                                    Icons.arrow_back_ios_new_outlined,
                                    color: state.isLeftThresold
                                        ? Theme.of(context)
                                            .textTheme
                                            .bodyLarge!
                                            .color!
                                            .withOpacity(0.5)
                                        : Theme.of(context)
                                            .textTheme
                                            .bodyLarge!
                                            .color,
                                  ),
                                ),
                              )),
                        ),
                        Expanded(
                          child: SizedBox(
                            height: 28 / 896 * dimensions.height,
                            child: FittedBox(
                              fit: BoxFit.fitHeight,
                              child: Text(
                                getDateTimeLabel(
                                    state.startTime, state.endTime, type),
                                textAlign: TextAlign.center,
                                style: Theme.of(context).textTheme.bodyLarge,
                              ),
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            dateTimePreviousOrNextActionOrOnTapAction(
                                state, true, context, type);
                          },
                          child: Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: 4.0 / 414 * dimensions.width,
                                  horizontal: 10 / 896 * dimensions.height),
                              child: SizedBox(
                                width: 16 / 414 * dimensions.width,
                                height: 16 / 414 * dimensions.width,
                                child: FittedBox(
                                  child: Icon(
                                    Icons.arrow_forward_ios_outlined,
                                    color: state.isRightThresold == true
                                        ? Theme.of(context)
                                            .textTheme
                                            .bodyLarge!
                                            .color!
                                            .withOpacity(0.5)
                                        : Theme.of(context)
                                            .textTheme
                                            .bodyLarge!
                                            .color,
                                  ),
                                ),
                              )),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              InkWell(
                onTap: () async {
                  if (state.dateTimeType != type) {
                    dateTimePreviousOrNextActionOrOnTapAction(
                        state, null, context, type);
                  }
                },
                child: Container(
                  width: labelContainerWidth / 414 * dimensions.width,
                  height: 42 / 896 * dimensions.height,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(left: 2 / 414 * dimensions.width),
                  decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      boxShadow: [
                        BoxShadow(
                            color: colorWhite.withOpacity(0.25),
                            offset: const Offset(-2, -2),
                            blurRadius: 1,
                            spreadRadius: 0),
                        BoxShadow(
                            color: colorBlack.withOpacity(0.25),
                            offset: const Offset(1, 1),
                            blurRadius: 1,
                            spreadRadius: 0),
                      ],
                      borderRadius:
                          BorderRadius.circular(8 / 414 * dimensions.width),
                      border: Border.all(color: Theme.of(context).cardColor)),
                  child: Text(
                    type.name,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ),
              ),
              SizedBox(
                width: 20 / 414 * dimensions.width,
              ),
            ],
          ),
        );
      },
    );
  }

  String getDateTimeLabel(
      DateTime startTime, DateTime endTime, DateTimeType type) {
    String dateTimeValue = '';
    String displayStartDateLabel = dayMonthFormatter.format(startTime);
    String displayEndDateLabel = dayMonthFormatter.format(endTime);
    switch (type) {
      case DateTimeType.day:
        dateTimeValue = displayStartDateLabel;
        break;
      case DateTimeType.week:
        dateTimeValue = '$displayStartDateLabel - $displayEndDateLabel';
        break;
      case DateTimeType.month:
        dateTimeValue =
            "${monthFormatter.format(endTime)}'${endTime.year.toString().substring(2)}";
        break;
    }
    return dateTimeValue;
  }

  void dateTimePreviousOrNextActionOrOnTapAction(StatisticsState state,
      bool? isNextAction, BuildContext context, DateTimeType currentType) {
    DateTime now = DateTime.now();
    DateTime startOfDay = now;
    DateTime endOfDay = now;
    DateTime startOfMonth = DateTime(endOfDay.year, endOfDay.month, 1);
    int currentWeekday = endOfDay.weekday;
    DateTime startOfWeek = now;
    DateTime startOfWeekMidnight = now;
    bool isLeftThresold = false;
    bool isRightThresold = false;
    DateTime dateLeftThresoldLimit = startOfMonth;

    now = DateTime(now.year, now.month, now.day);
    if (isNextAction == null) {
      startOfDay = DateTime(now.year, now.month, now.day);
      endOfDay = DateTime.now();
      currentWeekday = endOfDay.weekday;
      startOfWeek = endOfDay.subtract(Duration(days: currentWeekday - 1));
      startOfWeekMidnight =
          DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day);
      startOfMonth = DateTime(endOfDay.year, endOfDay.month, 1);
    } else {
      startOfDay = state.startTime;
      endOfDay = state.endTime;
      startOfWeek = state.startTime;
      startOfWeekMidnight = state.startTime;
      startOfMonth = state.startTime;
    }
    switch (currentType) {
      case DateTimeType.day:
        if (isNextAction == true &&
            !(startOfDay.isAfter(now) || startOfDay == now)) {
          startOfDay = endOfDay;

          endOfDay = endOfDay.add(const Duration(days: 1));
        } else if (isNextAction == false &&
            !startOfDay.isAfter(now) &&
            startOfDay.day > 0) {
          endOfDay = startOfDay;
          startOfDay = startOfDay.subtract(const Duration(days: 1));
        }
        if (startOfDay.isBefore(dateLeftThresoldLimit) ||
            startOfDay == dateLeftThresoldLimit) {
          isLeftThresold = true;
        }
        if (endOfDay.isAfter(now)) {
          isRightThresold = true;
        }

        context.read<StatisticsBloc>().add(DayEvent(
            startTime: startOfDay,
            endTime: endOfDay,
            dateTimeType: DateTimeType.day,
            isLeftThresold: isLeftThresold,
            isRightThresold: isRightThresold));
        dataEvent(startOfDay, endOfDay, state.statisticsDataType, context);
        break;
      case DateTimeType.week:
        if (isNextAction == true &&
            !(endOfDay.isAfter(now) || endOfDay == now)) {
          startOfWeekMidnight =
              startOfWeekMidnight.add(const Duration(days: 7));
          endOfDay = endOfDay.add(const Duration(days: 7));
        } else if (isNextAction == false &&
            startOfWeekMidnight
                .isAfter(now.subtract(const Duration(days: 30)))) {
          startOfWeekMidnight =
              startOfWeekMidnight.subtract(const Duration(days: 7));
          endOfDay = startOfWeekMidnight.add(const Duration(days: 6));
        }
        if (endOfDay.day == now.day &&
            endOfDay.month == now.month &&
            endOfDay.year == now.year) {
          endOfDay = DateTime.now();
        }
        if (startOfWeekMidnight
                .isBefore(now.subtract(const Duration(days: 30))) ||
            startOfWeekMidnight == now.subtract(const Duration(days: 30))) {
          isLeftThresold = true;
        }
        if (endOfDay.isAfter(now)) {
          isRightThresold = true;
        }

        context.read<StatisticsBloc>().add(WeekEvent(
            startTime: startOfWeekMidnight,
            endTime: endOfDay,
            dateTimeType: DateTimeType.week,
            isLeftThresold: isLeftThresold,
            isRightThresold: isRightThresold));
        dataEvent(
            startOfWeekMidnight, endOfDay, state.statisticsDataType, context);
        break;
      case DateTimeType.month:
        if (isNextAction == true &&
            !(startOfMonth.month == now.month &&
                startOfMonth.year == now.year)) {
          startOfMonth =
              subtractOrAddMonth(dateTime: startOfMonth, isSubtract: false);

          DateTime firstDayOfNextMonth =
              DateTime(startOfMonth.year, startOfMonth.month + 1, 1);

          endOfDay = firstDayOfNextMonth.subtract(const Duration(days: 1));
        } else if (isNextAction == false &&
            !(startOfMonth.month == now.month + 1 &&
                startOfMonth.year < now.year)) {
          startOfMonth =
              subtractOrAddMonth(dateTime: startOfMonth, isSubtract: true);
          DateTime firstDayOfNextMonth =
              DateTime(startOfMonth.year, startOfMonth.month + 1, 1);

          endOfDay = firstDayOfNextMonth.subtract(const Duration(days: 1));
        }
        if (endOfDay.month == now.month && endOfDay.year == now.year) {
          endOfDay = DateTime.now();
        }
        if (startOfMonth.month == now.month + 1) {
          isLeftThresold = true;
        }
        if (endOfDay.isAfter(now)) {
          isRightThresold = true;
        }
        StatisticsBloc dateTimePickerBloc = context.read<StatisticsBloc>();
        if (dateTimePickerBloc.state.startTime != startOfMonth &&
            dateTimePickerBloc.state.endTime != endOfDay) {
          dateTimePickerBloc.add(MonthEvent(
              startTime: startOfMonth,
              endTime: endOfDay,
              dateTimeType: DateTimeType.month,
              isLeftThresold: isLeftThresold,
              isRightThresold: isRightThresold));
          dataEvent(startOfMonth, endOfDay, state.statisticsDataType, context);
        }

        break;
    }
  }

  DateTime subtractOrAddMonth(
      {required DateTime dateTime, required bool isSubtract}) {
    int year = dateTime.year;
    int month = isSubtract ? dateTime.month - 1 : dateTime.month + 1;
    int day = dateTime.day;

    if (month < 1 && isSubtract) {
      month = 12;
      year -= 1;
    }

    if (month > 12 && !isSubtract) {
      month = 1;
      year += 1;
    }
    int daysInNewMonth = DateTime(year, month + 1, 0).day;

    if (day > daysInNewMonth) {
      day = daysInNewMonth;
    }

    return DateTime(year, month, day, dateTime.hour, dateTime.minute,
        dateTime.second, dateTime.millisecond, dateTime.microsecond);
  }

  dataEvent(DateTime startTime, DateTime endTime,
      StatisticsDataType statisticsDataType, BuildContext context) {
    if (statisticsDataType == StatisticsDataType.statistics) {
      context.read<StatisticsBloc>().add(LoadStatistics(
            startTime: startTime,
            endTime: endTime,
          ));
    } else {
      context.read<StatisticsBloc>().add(LoadStatisticsDetails(
            statisticsDataType,
            startTime: startTime,
            endTime: endTime,
          ));
    }
  }
}

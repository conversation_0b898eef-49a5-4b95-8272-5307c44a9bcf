import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/insight/statistics/load_statistics_event.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_blocs.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/utils/convax_decoration.dart';

class StatisticsDataSquareContainer extends StatefulWidget {
  final String labelName;
  final List<String> data;
  final List<String> unit;
  final String iconPath;
  final Color iconColor;
  final Color iconBgColor;
  final Color containerColor;
  final List<Color> containerBorderColor;
  final Function action;
  final bool isStaticDetailsData;
  final String statisticsDataContainerImagePath;
  const StatisticsDataSquareContainer(
      {super.key,
      required this.labelName,
      required this.data,
      required this.iconPath,
      required this.iconColor,
      required this.iconBgColor,
      required this.action,
      required this.unit,
      required this.containerColor,
      required this.containerBorderColor,
      required this.isStaticDetailsData,
      required this.statisticsDataContainerImagePath});

  @override
  State<StatisticsDataSquareContainer> createState() =>
      _StatisticsDataSquareContainerState();
}

class _StatisticsDataSquareContainerState
    extends State<StatisticsDataSquareContainer> {
  late bool isStaticDetailsData;

  @override
  void initState() {
    isStaticDetailsData = widget.isStaticDetailsData;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    ThemeMode themeMode = MyApp.of(context).getCurrentThemeMode();

    return BlocBuilder<StatisticsBloc, StatisticsState>(
      builder: (context, state) {
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) {
            context.read<StatisticsBloc>().add(LoadStatistics(
                startTime: state.startTime, endTime: state.endTime));
          },
          child: InkWell(
            onTap: () {
              widget.action.call();
            },
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  height: (isStaticDetailsData ? 116 : 179) /
                      896 *
                      dimensions.height,
                  width: (isStaticDetailsData ? 374 : 179) /
                      414 *
                      dimensions.width,
                  decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.circular(30 / 414 * dimensions.width),
                    boxShadow: [
                      BoxShadow(
                        color: themeMode == ThemeMode.light
                            ? Colors.black.withOpacity(0.25)
                            : Colors.white.withOpacity(0.25),
                        spreadRadius: 0,
                        blurRadius: 2 / 414 * dimensions.width,
                        offset: themeMode == ThemeMode.light
                            ? Offset(-3 / 414 * dimensions.width,
                                3 / 414 * dimensions.width)
                            : Offset(3 / 414 * dimensions.width,
                                -3 / 414 * dimensions.width),
                      ),
                      BoxShadow(
                        color: widget.containerColor,
                        spreadRadius: 3 / 414 * dimensions.width,
                        blurRadius: 2 / 414 * dimensions.width,
                        offset: themeMode == ThemeMode.light
                            ? Offset(
                                3 / 414 * dimensions.width,
                                -3 / 414 * dimensions.width,
                              )
                            : Offset(
                                -3 / 414 * dimensions.width,
                                3 / 414 * dimensions.width,
                              ),
                      )
                    ],
                    gradient: LinearGradient(
                      begin: AlignmentDirectional.bottomStart,
                      end: AlignmentDirectional.topEnd,
                      colors: widget.containerBorderColor,
                    ),
                  ),
                ),
                Container(
                  height: (isStaticDetailsData ? 115 : 178) /
                      896 *
                      dimensions.height,
                  width: (isStaticDetailsData ? 373 : 178) /
                      414 *
                      dimensions.width,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      30 / 414 * dimensions.width,
                    ),
                    border: Border.all(
                      width: 1 / 414 * dimensions.width,
                      color: Colors.transparent,
                    ),
                    color: widget.iconBgColor,
                  ),
                ),
                Container(
                  height: (isStaticDetailsData ? 115 : 178) /
                      896 *
                      dimensions.height,
                  width: (isStaticDetailsData ? 373 : 178) /
                      414 *
                      dimensions.width,
                  margin: EdgeInsets.only(
                      left: 4 / 414 * dimensions.width,
                      bottom: 4 / 896 * dimensions.height),
                  decoration: BoxDecoration(
                    color: widget.containerColor,
                    borderRadius: BorderRadius.circular(
                      30 / 414 * dimensions.width,
                    ),
                    gradient: LinearGradient(
                      begin: AlignmentDirectional.bottomStart,
                      end: AlignmentDirectional.topEnd,
                      colors: widget.containerBorderColor,
                    ),
                  ),
                ),
                Container(
                  height: (isStaticDetailsData ? 113 : 176) /
                      896 *
                      dimensions.height,
                  width: (isStaticDetailsData ? 372 : 176) /
                      414 *
                      dimensions.width,
                  margin: EdgeInsets.only(
                      left: 4 / 414 * dimensions.width,
                      bottom: 4 / 896 * dimensions.height),
                  decoration: BoxDecoration(
                    color: widget.containerColor,
                    borderRadius: BorderRadius.circular(
                      30 / 414 * dimensions.width,
                    ),
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(
                        top: 8.0 / 896 * dimensions.height,
                        right: 8.0 / 414 * dimensions.width,
                        left: 16 / 414 * dimensions.width,
                        bottom: (isStaticDetailsData ? 8 : 16) /
                            896 *
                            dimensions.height),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: (isStaticDetailsData ? 40 : 76) /
                                  896 *
                                  dimensions.height,
                              width: (isStaticDetailsData ? 200 : 100) /
                                  414 *
                                  dimensions.width,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: (isStaticDetailsData ? 8 : 16) /
                                        896 *
                                        dimensions.height,
                                  ),
                                  Text(
                                    widget.labelName,
                                    style: Theme.of(context)
                                        .textTheme
                                        .headlineMedium,
                                    textAlign: TextAlign.left,
                                  ),
                                  Flexible(
                                    child: SizedBox(
                                      height: (isStaticDetailsData ? 8 : 16) /
                                          414 *
                                          dimensions.width,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Visibility(
                              visible: !isStaticDetailsData,
                              child: Stack(
                                children: [
                                  Container(
                                    width: 50 / 414 * dimensions.width,
                                    height: 50 / 414 * dimensions.width,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: widget.iconBgColor,
                                    ),
                                  ),
                                  Container(
                                    width: 50 / 414 * dimensions.width,
                                    height: 50 / 414 * dimensions.width,
                                    decoration: ConvaxDecoration(
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(25),
                                      ),
                                      depth: 3,
                                      colors: [
                                        Colors.black.withOpacity(0.5),
                                        colorGrey400.withOpacity(0.5)
                                      ],
                                      opacity: 0.7,
                                    ),
                                    child: Center(
                                      child: SizedBox(
                                          width: 20 / 414 * dimensions.width,
                                          height: 20 / 414 * dimensions.width,
                                          child: Image.asset(
                                            widget.iconPath,
                                            fit: BoxFit.fill,
                                          )),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        FittedBox(child: displayData()),
                        Visibility(
                          visible: !isStaticDetailsData,
                          child: Align(
                            alignment: Alignment.bottomRight,
                            child: InkWell(
                              onTap: (() {}),
                              child: Container(
                                  margin: EdgeInsets.only(
                                      right: 8 / 414 * dimensions.width),
                                  width: 15 / 414 * dimensions.width,
                                  height: 15 / 896 * dimensions.height,
                                  child: Image.asset(
                                    insightsScreenImages['expand_widget_icon']!,
                                    color: MyApp.of(context)
                                                .getCurrentThemeMode() ==
                                            ThemeMode.dark
                                        ? colorGrey25
                                        : colorBlack800,
                                  )),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Visibility(
                  visible: isStaticDetailsData &&
                      widget.statisticsDataContainerImagePath != '',
                  child: Align(
                    alignment: Alignment.bottomRight,
                    child: Container(
                      margin: EdgeInsets.only(
                          right: 28 / 414 * dimensions.width,
                          bottom: 8 / 896 * dimensions.height),
                      width: 150 / 414 * dimensions.width,
                      height: 133 / 896 * dimensions.height,
                      child: Image.asset(
                        widget.statisticsDataContainerImagePath,
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  Widget displayData() {
    List<TextSpan> list = [];

    for (int i = 0; i < widget.data.length; i++) {
      list.add(TextSpan(
        text: widget.data[i],
        style: Theme.of(context).textTheme.titleLarge,
      ));
      list.add(TextSpan(
        text: widget.unit[i],
        style: Theme.of(context).textTheme.bodyLarge,
      ));
    }

    return RichText(
        textDirection: TextDirection.ltr, text: TextSpan(children: list));
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nds_app/blocs/vehicle/vehicles/vehicles_bloc.dart';
import 'package:nds_app/blocs/vehicle/vehicles/vehicles_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/models/vehicle_details.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/company/templates/vehicle/prodred_leadership_vehicle_detail_screen.dart';

import '../../common/text_styles.dart';
import '../../common/image_urls.dart';
import '../../common/strings.dart';

class LeadershipVehicleView extends StatefulWidget {
  const LeadershipVehicleView({super.key});

  @override
  State<LeadershipVehicleView> createState() => _LeadershipVehicleViewState();
}

class _LeadershipVehicleViewState extends State<LeadershipVehicleView> {

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 2 / 375 * dimensions.width),
      child: BlocBuilder<VehiclesBloc, VehiclesState>(
        builder: (context, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildSummaryCard(dimensions, state),
              SizedBox(height: 24 / 896 * dimensions.height),
              _buildSectionHeader(context, dimensions),
              SizedBox(height: 16 / 896 * dimensions.height),
              _buildVehicleList(context, dimensions, state),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSummaryCard(Dimensions dimensions, VehiclesState state) {
    return Container(
      width: double.infinity,
      height: 200 / 896 * dimensions.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        image: DecorationImage(
          image: AssetImage(leadershipImages['insight_leadership_vehicle']!),
          fit: BoxFit.cover,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.black.withOpacity(0.4),
              Colors.black.withOpacity(0.2),
            ],
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(20 / 375 * dimensions.width),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Center(
                child: Text(
                  leadershipText['label_vehicles']!,
                  style: poppinsTextStyle(
                    28 / 414 * dimensions.width,
                    Colors.white,
                    FontWeight.w500,
                  ),
                ),
              ),
              SizedBox(width: 12 / 896 * dimensions.height),
              Center(
                child: Text(
                  state.apiStatus == ApiStatus.initial
                      ? '-'
                      : state.totalElements.toString(),
                  style: poppinsTextStyle(
                    28 / 414 * dimensions.width,
                    Colors.white,
                    FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, Dimensions dimensions) {
    return Row(
      children: [
        Icon(
          Icons.motorcycle,
          color: Theme.of(context).iconTheme.color ?? colorGrey600,
          size: 20 / 375 * dimensions.width,
        ),
        SizedBox(width: 8 / 375 * dimensions.width),
        Text(
          leadershipText['select_vehicle']!,
          style: poppinsTextStyle(
            20 / 414 * dimensions.width,
            Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black,
            FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildVehicleList(
      BuildContext context, Dimensions dimensions, VehiclesState state) {
    if (state.apiStatus == ApiStatus.initial ||
        (state.apiStatus == ApiStatus.loading && state.items.isEmpty)) {
      return const Center(child: CircularProgressIndicator());
    }
    if (state.apiStatus == ApiStatus.failure && state.items.isEmpty) {
      return Center(
        child: Column(
          children: [
            Icon(Icons.motorcycle, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              leadershipText['no_vehicle_data']!,
              style: poppinsTextStyle(
                16 / 414 * dimensions.width,
                Theme.of(context).textTheme.bodyMedium?.color ?? Colors.grey,
                FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    // Empty state
    if (state.items.isEmpty) {
      return Center(
        child: Column(
          children: [
            Icon(Icons.motorcycle, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              leadershipText['no_vehicle_data']!,
              style: poppinsTextStyle(
                16 / 414 * dimensions.width,
                Theme.of(context).textTheme.bodyMedium?.color ?? Colors.grey,
                FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    final hasMorePages =
        state.totalPages > 0 && (state.currentPage + 1) < state.totalPages;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Render all vehicle items
        ...state.items.map((vehicle) => 
          _buildVehicleItem(context, dimensions, vehicle)
        ).toList(),
        
        // Show loading indicator at the bottom if loading more
        if (hasMorePages && state.isLoadingMore)
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 16.0),
            child: Center(child: CircularProgressIndicator()),
          ),
      ],
    );
  }

  Widget _buildVehicleItem(
      BuildContext context, Dimensions dimensions, VehicleDetail vehicle) {
    return InkWell(
      onTap: () => _navigateToDetailScreen(context, vehicle.modelName,
          'Vehicle', int.tryParse(vehicle.vehId) ?? 0),
      child: Container(
        margin: EdgeInsets.only(
          bottom: 8,
          left: 6 / 414 * dimensions.width,
          right: 6 / 414 * dimensions.width,
        ),
        padding: EdgeInsets.all(16 / 375 * dimensions.width),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1, color: colorGrey300),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: SizedBox(
                width: 60 / 375 * dimensions.width,
                height: 60 / 375 * dimensions.width,
                child: _buildVehicleImage(vehicle.imageUrl),
              ),
            ),
            SizedBox(width: 16 / 375 * dimensions.width),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    vehicle.modelName,
                    style: poppinsTextStyle(
                      15 / 414 * dimensions.width,
                      Theme.of(context).textTheme.bodyLarge?.color ??
                          Colors.black,
                      FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 4 / 896 * dimensions.height),
                  Text(
                    vehicle.imei,
                    style: poppinsTextStyle(
                      12 / 414 * dimensions.width,
                      (Theme.of(context).brightness == Brightness.dark)
                          ? Colors.white
                          : colorGrey500,
                      FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVehicleImage(String imageUrl) {
    if (imageUrl.isNotEmpty && imageUrl.toLowerCase().endsWith('.svg')) {
      return SvgPicture.network(imageUrl, fit: BoxFit.cover);
    }
    final imageProvider = imageUrl.isNotEmpty
        ? NetworkImage(imageUrl)
        : AssetImage(leadershipImages['default_vehicle_model']!)
            as ImageProvider;
    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(image: imageProvider, fit: BoxFit.cover),
      ),
    );
  }

  void _navigateToDetailScreen(
      BuildContext context, String itemName, String itemType, int entityId) {
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";
    ColorType colorType = ColorType.values.firstWhere(
      (element) =>
          element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );
    Color color =
        hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ProdredLeadershipVehicleDetailScreen(
          color: color,
          colorType: colorType,
          selectedItemName: itemName,
          selectedItemType: itemType,
          entityId: entityId,
        ),
      ),
    );
  }
}

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/models/vehicle.dart';

class DistanceDetail extends StatelessWidget {
  const DistanceDetail({
    super.key,
    required this.backgroundColor,
    required this.themeMode,
    required this.vehicle,
    required this.borderColor,
  });

  final Color backgroundColor;
  final Color borderColor;
  final ThemeMode themeMode;
  final Vehicle vehicle;

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return Expanded(
      child: Container(
       // height: 110.0 / 896 * dimensions.height,
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          color: backgroundColor,
          border:
              Border.all(color: borderColor, width: 1 / 414 * dimensions.width),
        ),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                    child: SizedBox(
                  height: 45,
                  child: AutoSizeText(
                    vehicleScreen["label_distance"]!,
                    style: TextStyle(
                        color: colorWhite,
                        fontFamily: "Poppins",
                        fontWeight: FontWeight.w600,
                        fontSize: 14 / 414 * dimensions.width),
                  ),
                )),
                Image.asset(
                  homeScreenImages["distance_icon"]!,
                  width: 14,
                  color: Colors.white,
                )
              ],
            ),
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                vehicle.distanceCovered == null
                    ? "-"
                    : "${vehicle.distanceCovered?.round()} km",
                style: Theme.of(context).textTheme.titleMedium,
              ),
            )
          ],
        ),
      ),
    );
  }
}

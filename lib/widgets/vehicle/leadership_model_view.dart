import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nds_app/blocs/vehicle/vehicle_models/vehicle_models_bloc.dart';
import 'package:nds_app/blocs/vehicle/vehicle_models/vehicle_models_event.dart';
import 'package:nds_app/blocs/vehicle/vehicle_models/vehicle_models_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/models/vehicle_model.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/company/templates/vehicle/prodred_leadership_vehicle_detail_screen.dart';

import '../../common/text_styles.dart';
import '../../common/strings.dart';
import '../../common/image_urls.dart';

class LeadershipModelView extends StatefulWidget {
  const LeadershipModelView({super.key});

  @override
  State<LeadershipModelView> createState() => _LeadershipModelViewState();
}

class _LeadershipModelViewState extends State<LeadershipModelView> {
  late VehicleModelsBloc _vehicleModelsBloc;

  @override
  void initState() {
    super.initState();
    _vehicleModelsBloc = VehicleModelsBloc();
    _vehicleModelsBloc.add(const LoadVehicleModelsEvent(page: 0, size: 10));
  }

  @override
  void dispose() {
    _vehicleModelsBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return BlocProvider.value(
      value: _vehicleModelsBloc,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 2 / 375 * dimensions.width),
        child: BlocBuilder<VehicleModelsBloc, VehicleModelsState>(
          builder: (context, state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSummaryCard(dimensions, state),
                SizedBox(height: 24 / 896 * dimensions.height),
                _buildSectionHeader(context, dimensions),
                SizedBox(height: 16 / 896 * dimensions.height),
                _buildModelList(context, dimensions, state),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSummaryCard(Dimensions dimensions, VehicleModelsState state) {
    return Container(
      width: double.infinity,
      height: 200 / 896 * dimensions.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        image: DecorationImage(
          image: AssetImage(leadershipImages['insight_leadership_vehicle']!),
          fit: BoxFit.cover,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.black.withOpacity(0.4),
              Colors.black.withOpacity(0.2),
            ],
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(20 / 375 * dimensions.width),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    leadershipText['label_models']!,
                    style: poppinsTextStyle(
                      28 / 414 * dimensions.width,
                      Colors.white,
                      FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: 12 / 896 * dimensions.height),
                  Text(
                    leadershipText['label_vehicles']!,
                    style: poppinsTextStyle(
                      16 / 414 * dimensions.width,
                      Colors.white,
                      FontWeight.w400,
                    ),
                  ),
                ],
              ),
              SizedBox(width: 20 / 896 * dimensions.height),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    state.apiStatus == ApiStatus.initial
                        ? '-'
                        : state.vehicleModelsResponse?.totalModels.toString() ??
                            '0',
                    style: poppinsTextStyle(
                      28 / 414 * dimensions.width,
                      Colors.white,
                      FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: 12 / 896 * dimensions.height),
                  Text(
                    state.apiStatus == ApiStatus.initial
                        ? '-'
                        : state.vehicleModelsResponse?.totalVehicles
                                .toString() ??
                            '0',
                    style: poppinsTextStyle(
                      16 / 414 * dimensions.width,
                      Colors.white,
                      FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, Dimensions dimensions) {
    return Row(
      children: [
        Icon(
          Icons.motorcycle,
          color: Theme.of(context).iconTheme.color ?? colorGrey600,
          size: 20 / 375 * dimensions.width,
        ),
        SizedBox(width: 8 / 375 * dimensions.width),
        Text(
          leadershipText['select_model']!,
          style: poppinsTextStyle(
            20 / 414 * dimensions.width,
            Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black,
            FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildModelList(
      BuildContext context, Dimensions dimensions, VehicleModelsState state) {
    // Initial loading
    if (state.apiStatus == ApiStatus.initial ||
        (state.apiStatus == ApiStatus.loading && state.items.isEmpty)) {
      return const Center(child: CircularProgressIndicator());
    }

    // Error state
    if (state.apiStatus == ApiStatus.failure && state.items.isEmpty) {
      return Center(
        child: Column(
          children: [
            Icon(Icons.motorcycle, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              leadershipText['no_model_data']!,
              style: poppinsTextStyle(
                16 / 414 * dimensions.width,
                Theme.of(context).textTheme.bodyMedium?.color ?? Colors.grey,
                FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    // Empty state
    if (state.items.isEmpty) {
      return Center(
        child: Column(
          children: [
            Icon(Icons.motorcycle, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              leadershipText['no_model_data']!,
              style: poppinsTextStyle(
                16 / 414 * dimensions.width,
                Theme.of(context).textTheme.bodyMedium?.color ?? Colors.grey,
                FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    final hasMorePages = state.vehicleModelsResponse != null &&
        (state.currentPage + 1) < state.vehicleModelsResponse!.totalPages;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Render all model items
        ...state.items.map((vehicleModel) => 
          _buildModelItem(context, dimensions, vehicleModel)
        ).toList(),
        
        // Show loading indicator at the bottom if loading more
        if (hasMorePages && state.isLoadingMore)
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 16.0),
            child: Center(child: CircularProgressIndicator()),
          ),
      ],
    );
  }

  Widget _buildModelItem(
      BuildContext context, Dimensions dimensions, VehicleModel vehicleModel) {
    return InkWell(
      onTap: () => _navigateToDetailScreen(
          context, vehicleModel.name, 'Model', vehicleModel.id),
      child: Container(
        margin: EdgeInsets.only(
          bottom: 8,
          left: 6 / 414 * dimensions.width,
          right: 6 / 414 * dimensions.width,
        ),
        padding: EdgeInsets.all(16 / 375 * dimensions.width),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1, color: colorGrey300),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: SizedBox(
                width: 60 / 375 * dimensions.width,
                height: 60 / 375 * dimensions.width,
                child: _buildVehicleModelImage(vehicleModel.imageUrl),
              ),
            ),
            SizedBox(width: 16 / 375 * dimensions.width),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    vehicleModel.name,
                    style: poppinsTextStyle(
                      15 / 414 * dimensions.width,
                      Theme.of(context).textTheme.bodyLarge?.color ??
                          Colors.black,
                      FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 4 / 896 * dimensions.height),
                  Text(
                    '${vehicleModel.vehicleCount} ${leadershipText['label_vehicles']}',
                    style: poppinsTextStyle(
                      12 / 414 * dimensions.width,
                      (Theme.of(context).brightness == Brightness.dark)
                          ? Colors.white
                          : colorGrey500,
                      FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVehicleModelImage(String imageUrl) {
    if (imageUrl.isNotEmpty && imageUrl.toLowerCase().endsWith('.svg')) {
      return SvgPicture.network(
        imageUrl,
        fit: BoxFit.cover,
        placeholderBuilder: (context) => Container(color: Colors.grey.shade200),
      );
    }

    final ImageProvider imageProvider = imageUrl.isNotEmpty
        ? NetworkImage(imageUrl)
        : AssetImage(leadershipImages['default_vehicle_model']!)
            as ImageProvider;

    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(image: imageProvider, fit: BoxFit.cover),
      ),
    );
  }

  void _navigateToDetailScreen(
      BuildContext context, String itemName, String itemType, int entityId) {
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";
    ColorType colorType = ColorType.values.firstWhere(
      (element) =>
          element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );
    Color color =
        hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ProdredLeadershipVehicleDetailScreen(
          color: color,
          colorType: colorType,
          selectedItemName: itemName,
          selectedItemType: itemType,
          entityId: entityId,
        ),
      ),
    );
  }
}

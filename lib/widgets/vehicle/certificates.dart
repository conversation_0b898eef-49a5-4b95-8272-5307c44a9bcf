import 'dart:math';

import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';

class Certificates extends StatelessWidget {
  const Certificates({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CertificateItem(
            vehicleScreen["certificate1"]!, vehicleScreen["available"]!),
        // CertificateItem(
        //     vehicleScreen["certificate2"]!, vehicleScreen["available"]!),
        CertificateItem(
            vehicleScreen["certificate3"]!, vehicleScreen["expired"]!),

        CertificateItem(
            vehicleScreen["vehicleHealth"]!, vehicleScreen["vehicleHealthStatus"]!),
      ],
    );
  }
}

class CertificateItem extends StatelessWidget {
  final String title;
  final String status;

  const CertificateItem(this.title, this.status, {super.key});

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.labelMedium,
        ),
        const SizedBox(height: 4.0),
        status != vehicleScreen["expired"]
            ? Text(
                status,
                style: poppinsTextStyle(12 / 414 * dimensions.width,
                    colorSuccessGreen600, FontWeight.w400),
              )
            : Row(
                children: [
                  Transform.rotate(
                    angle: pi,
                    child: const Icon(
                      Icons.info,
                      size: 20,
                      color: Colors.red,
                    ),
                  ),
                  const SizedBox(width: 4.0),
                  Text(
                    status,
                    style: poppinsTextStyle(12 / 414 * dimensions.width,
                        colorError600, FontWeight.w400),
                  ),
                ],
              ),
        const SizedBox(height: 16.0),
        const Divider(
          color: Colors.grey,
          thickness: 1.0,
        ),
        const SizedBox(height: 16.0),
      ],
    );
  }
}

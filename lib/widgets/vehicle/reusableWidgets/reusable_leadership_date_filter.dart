import 'package:flutter/material.dart';

import '../../../common/colors.dart';
import '../../../common/dimensions.dart';

class ReusableDateFilter extends StatelessWidget {
  final Dimensions dimensions;
  final String selectedValue;
  final List<String> options;
  final ValueChanged<String> onChanged;

  const ReusableDateFilter({
    super.key,
    required this.dimensions,
    required this.selectedValue,
    required this.options,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 44 / 896 * dimensions.height,
      decoration: BoxDecoration(
        border: Border.all(color: colorGrey300),
        borderRadius: BorderRadius.circular(8),
        color: Theme.of(context).scaffoldBackgroundColor,
      ),
      padding: EdgeInsets.symmetric(horizontal: 12 / 414 * dimensions.width),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedValue,
          isDense: true,
          isExpanded: true,
          icon: Icon(
            Icons.keyboard_arrow_down,
            size: 20,
            color: Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black87,
          ),
          dropdownColor: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          itemHeight: 48,
          items: options.map<DropdownMenuItem<String>>((String value) {
            bool isSelected = value == selectedValue;
            return DropdownMenuItem<String>(
              value: value,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 2),
                child: Text(
                  value,
                  maxLines: 1,
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 13,
                    color: isSelected
                        ? (Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black)
                        : (Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.shade400
                        : Colors.grey.shade600),
                  ),
                ),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              onChanged(newValue);
            }
          },
        ),
      ),
    );
  }
}

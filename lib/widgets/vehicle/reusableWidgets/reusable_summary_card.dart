import 'package:flutter/material.dart';
import 'package:nds_app/widgets/vehicle/reusableWidgets/reusable_heading_with_icon.dart';
import 'header_trailer_text_row.dart';

class ReusableSummaryCard extends StatelessWidget {
  final String title;
  final String icon;
  final List<Map<String, String>> items;
  final Color? backgroundColor;
  final Function(String header, String trailer)? onItemTap;

  const ReusableSummaryCard({
    super.key,
    required this.title,
    required this.icon,
    required this.items,
    this.backgroundColor,
    this.onItemTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: backgroundColor ?? Colors.grey[200],
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          children: [
            ReusableHeadingWithIcon(
              icon: icon,
              title: title,
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: ListView.separated(
                itemCount: items.length,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (BuildContext context, int index) {
                  return HeaderTrailerTextRow(
                    header: items[index]["header"] ?? "",
                    trailer: items[index]["trailer"] ?? "",
                    onTap: () {
                      if (onItemTap != null) {
                        onItemTap!(
                          items[index]["header"] ?? "",
                          items[index]["trailer"] ?? "",
                        );
                      }
                    },
                  );
                },
                separatorBuilder: (BuildContext context, int index) {
                  return Column(
                    children: const [
                      Divider(thickness: 1,color: Colors.grey,),
                      SizedBox(
                        height: 5,
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

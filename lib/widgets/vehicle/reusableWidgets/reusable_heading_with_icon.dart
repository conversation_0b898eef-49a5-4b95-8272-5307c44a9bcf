import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../common/colors.dart';

class ReusableHeadingWithIcon extends StatelessWidget {
  final String icon;
  final String title;

  // Optional customization with defaults
  final double iconSize;
  final Color? iconColor;
  final double fontSize;
  final Color? fontColor;
  final FontWeight fontWeight;
  final double spacing;

  const ReusableHeadingWithIcon({
    Key? key,
    required this.icon,
    required this.title,
    this.iconSize = 24,
    this.iconColor,
    this.fontSize = 20,
    this.fontColor,
    this.fontWeight = FontWeight.w500,
    this.spacing = 6,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SvgPicture.asset(
          icon,
          color: iconColor,
          height: iconSize,
          width: iconSize,
        ),
        SizedBox(width: spacing),
        Text(
          title,
          style: TextStyle(
            fontSize: fontSize,
            color: fontColor ?? colorGrey800,
            fontWeight: fontWeight,
          ),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import '../../../common/dimensions.dart';

Widget reusableLeadershipInsightHeader({required Dimensions dimensions, required Widget child}) {
  return Container(
    width: double.infinity,
    padding: EdgeInsets.symmetric(horizontal: 24,vertical: 16),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(12),
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Colors.black.withOpacity(0.4),
          Colors.black.withOpacity(0.2),
        ],
      ),
      image: const DecorationImage(
        image: AssetImage('assets/range_screen/range_map_bg.png'),
        fit: BoxFit.cover,
      ),
    ),
    child: child,
  );
}
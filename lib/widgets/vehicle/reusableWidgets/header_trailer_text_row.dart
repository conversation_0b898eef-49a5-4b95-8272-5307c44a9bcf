import 'package:flutter/material.dart';
import '../../../common/colors.dart';
import '../../../common/text_styles.dart';

class HeaderTrailerTextRow extends StatelessWidget {
  final String header;
  final String trailer;
  final Color textColor;
  final double fontSize;
  final VoidCallback? onTap;

  const HeaderTrailerTextRow({
    super.key,
    required this.header,
    required this.trailer,
    this.textColor = colorGrey800,
    this.fontSize = 14,
    this.onTap, // optional callback
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              header,
              overflow: TextOverflow.ellipsis,
              style: poppinsTextStyle(
                fontSize,
                textColor,
                FontWeight.w500,
              ),
            ),
          ),
          SizedBox(width: 5,),
          Text(
            trailer,
            style: poppinsTextStyle(
              fontSize,
              textColor,
              FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

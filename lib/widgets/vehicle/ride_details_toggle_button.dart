import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/vehicle/ride_details/ride_details_toggle_bloc.dart';
import 'package:nds_app/blocs/vehicle/ride_details/ride_details_toggle_event.dart';
import 'package:nds_app/blocs/vehicle/ride_details/ride_details_toggle_state.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/utils/concave_decoration.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../common/strings.dart';

class RideDetailsToggleButton extends StatefulWidget {
  final Color color;
  final ColorType colorType;

  const RideDetailsToggleButton(
      {super.key, required this.color, required this.colorType});

  @override
  State<RideDetailsToggleButton> createState() =>
      _RideDetailsToggleButtonState();
}

class _RideDetailsToggleButtonState extends State<RideDetailsToggleButton> {
  late Color color;
  late ColorType colorType;
  SharedPreferences? sharedPreferences;

  @override
  void initState() {
    super.initState();
    color = widget.color;
    colorType = widget.colorType;
    _initializePreferences();
  }

  Future<void> _initializePreferences() async {
    sharedPreferences = await SharedPreferences.getInstance();
    if (mounted) {
      String? hexColorInStr =
          sharedPreferences!.getString(vehicleThemeColorInHex);
      String? storedColorType =
          sharedPreferences!.getString(vehicleThemeColorTypeKey);

      setState(() {
        if (storedColorType != null) {
          colorType = ColorType.values.firstWhere(
            (element) => element.toString() == storedColorType,
            orElse: () => ColorType.normal,
          );
        }
        if (hexColorInStr != null && hexColorInStr.isNotEmpty) {
          color = hexColorInStr.toColor();
        } else if (storedColorType == null) {
          color = colorGrey800;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Column(
      children: [
        Container(
          height: 54 / 896 * dimensions.height,
          width: 376 / 414 * dimensions.width,
          decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              border: Border.all(color: Theme.of(context).cardColor, width: 2),
              borderRadius: BorderRadius.circular(8 / 414 * dimensions.width),
              boxShadow: [
                BoxShadow(
                    color: colorBlack.withOpacity(0.25),
                    offset: const Offset(1, 3),
                    blurRadius: 3,
                    spreadRadius: 1),
                BoxShadow(
                    color: colorWhite.withOpacity(0.25),
                    offset: const Offset(-1, -3),
                    blurRadius: 3,
                    spreadRadius: 1)
              ]),
          child: BlocBuilder<RideDetailsToggleBloc, RideDetailsToggleState>(
              builder: (context, state) {
            return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      if (state.isSwitchRight) {
                        context
                            .read<RideDetailsToggleBloc>()
                            .add(SwitchEvent());
                      }
                    },
                    child: getToggleLabelContainer(
                        dimensions,rideDetailsScreenText["text2"]!, !state.isSwitchRight),
                  ),
                  InkWell(
                      onTap: () {
                        if (!state.isSwitchRight) {
                          context
                              .read<RideDetailsToggleBloc>()
                              .add(SwitchEvent());
                        }
                      },
                      child: getToggleLabelContainer(
                          dimensions, rideDetailsScreenText["text3"]!, state.isSwitchRight)),
                ]);
          }),
        ),
      ],
    );
  }

  getToggleLabelContainer(
      Dimensions dimensions, String label, bool isSelected) {
    Color bgColor = currentVehicleStatus == VehicleStatus.connected
        ? (colorType == ColorType.light
            ? Theme.of(context).highlightColor
            : color)
        : loginThemeColor;

    return isSelected
        ? Stack(
            children: [
              Container(
                height: 50 / 896 * dimensions.height,
                width: 184 / 414 * dimensions.width,
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(6 / 414 * dimensions.width),
                  color: bgColor,
                ),
              ),
              Container(
                  height: 52 / 896 * dimensions.height,
                  width: 184 / 414 * dimensions.width,
                  decoration: ConcaveDecoration(
                    alignment: MyApp.of(context).getCurrentThemeMode() ==
                            ThemeMode.dark
                        ? Alignment.topRight
                        : Alignment.topLeft,
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(6 / 414 * dimensions.width),
                    ),
                    depth: 3,
                    colors: [
                      colorWhite,
                      Colors.black.withOpacity(0.25),
                    ],
                    opacity: 0.7,
                  ),
                  child: getToggleLabelContainerUnselected(
                      dimensions, label, isSelected, bgColor)),
            ],
          )
        : getToggleLabelContainerUnselected(
            dimensions, label, isSelected, bgColor);
  }

  getToggleLabelContainerUnselected(
      Dimensions dimensions, String label, bool isSelected, Color bgColor) {
    return SizedBox(
      width: 184 / 414 * dimensions.width,
      child: Center(
        child: AutoSizeText(
          label,
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                color: isSelected
                    ? (bgColor.computeLuminance() < 0.5
                        ? Colors.white
                        : Colors.black)
                    : null,
              ),
          maxLines: 1,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

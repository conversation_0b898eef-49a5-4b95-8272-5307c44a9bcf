import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/vehicle/toggle/leadership_vehicle_toggle_bloc.dart';
import 'package:nds_app/blocs/vehicle/toggle/leadership_vehicle_toggle_event.dart';
import 'package:nds_app/blocs/vehicle/toggle/leadership_vehicle_toggle_state.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/utils/concave_decoration.dart';

class ToggleButtonLeadershipVehicle extends StatefulWidget {
  final Color color;
  final ColorType colorType;

  const ToggleButtonLeadershipVehicle({
    super.key,
    required this.color,
    required this.colorType,
  });

  @override
  State<ToggleButtonLeadershipVehicle> createState() =>
      _ToggleButtonLeadershipVehicleState();
}

class _ToggleButtonLeadershipVehicleState
    extends State<ToggleButtonLeadershipVehicle> {
  late Color color;
  late ColorType colorType;

  @override
  void initState() {
    color = widget.color;
    colorType = widget.colorType;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Column(
      children: [
        Container(
          height: 54 / 896 * dimensions.height,
          width: 376 / 414 * dimensions.width,
          decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              border: Border.all(color: Theme.of(context).cardColor, width: 2),
              borderRadius: BorderRadius.circular(8 / 414 * dimensions.width),
              boxShadow: [
                BoxShadow(
                    color: colorBlack.withOpacity(0.25),
                    offset: const Offset(1, 3),
                    blurRadius: 3,
                    spreadRadius: 1),
                BoxShadow(
                    color: colorWhite.withOpacity(0.25),
                    offset: const Offset(-1, -3),
                    blurRadius: 3,
                    spreadRadius: 1)
              ]),
          child: BlocBuilder<LeadershipVehicleToggleBloc, LeadershipVehicleToggleState>(
              builder: (context, state) {
            return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      if (state.selectedIndex != 0) {
                        context.read<LeadershipVehicleToggleBloc>().add(SwitchToModelEvent());
                      }
                    },
                    child: getToggleLabelContainer(dimensions, leadershipText['toggle_model']!, state.selectedIndex == 0),
                  ),
                  InkWell(
                    onTap: () {
                      if (state.selectedIndex != 1) {
                        context.read<LeadershipVehicleToggleBloc>().add(SwitchToFleetEvent());
                      }
                    },
                    child: getToggleLabelContainer(dimensions, leadershipText['toggle_fleet']!, state.selectedIndex == 1),
                  ),
                  InkWell(
                    onTap: () {
                      if (state.selectedIndex != 2) {
                        context.read<LeadershipVehicleToggleBloc>().add(SwitchToVehicleEvent());
                      }
                    },
                    child: getToggleLabelContainer(dimensions, leadershipText['toggle_vehicle']!, state.selectedIndex == 2),
                  ),
                ]);
          }),
        ),
      ],
    );
  }

  getToggleLabelContainer(
      Dimensions dimensions, String label, bool isSelected) {
    Color bgColor = currentVehicleStatus == VehicleStatus.connected
        ? (colorType == ColorType.light
            ? Theme.of(context).highlightColor
            : color)
        : loginThemeColor;

    return isSelected
        ? Stack(
            children: [
              Container(
                height: 50 / 896 * dimensions.height,
                width: 120 / 414 * dimensions.width,
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(6 / 414 * dimensions.width),
                  color: bgColor,
                ),
              ),
              Container(
                  height: 52 / 896 * dimensions.height,
                  width: 120 / 414 * dimensions.width,
                  decoration: ConcaveDecoration(
                    alignment: MyApp.of(context).getCurrentThemeMode() ==
                            ThemeMode.dark
                        ? Alignment.topRight
                        : Alignment.topLeft,
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(6 / 414 * dimensions.width),
                    ),
                    depth: 3,
                    colors: [
                      colorWhite,
                      Colors.black.withOpacity(0.25),
                    ],
                    opacity: 0.7,
                  ),
                  child: getToggleLabelContainerUnselected(
                      dimensions, label, isSelected, bgColor)),
            ],
          )
        : getToggleLabelContainerUnselected(
            dimensions, label, isSelected, bgColor);
  }

  getToggleLabelContainerUnselected(
      Dimensions dimensions, String label, bool isSelected, Color bgColor) {
    return SizedBox(
      width: 120 / 414 * dimensions.width,
      child: Center(
        child: AutoSizeText(
          label,
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                color: isSelected
                    ? (bgColor.computeLuminance() < 0.5
                        ? Colors.white
                        : Colors.black)
                    : null,
              ),
          maxLines: 1,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:intl/intl.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/models/ride_activity.dart';
import 'package:uuid/uuid.dart';
import '../../common/image_urls.dart';
import '../../common/strings.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../common/shared_preferences_keys.dart';
import '../../constant/api_urls.dart';
import '../../common/constant.dart';
import '../../services/api_service.dart';
import '../../models/trip.dart';
import '../../utils/toast.dart';
import 'dart:convert';
import '../../models/ride_validation_status.dart';
import '../../utils/extension.dart';

class AddRideActivityScreen extends StatefulWidget {
  final Function(RideActivity) onRideAdded;
  final Function() onCancel;
  final Map<String, dynamic>? rideDetails;
  final Trip? trip;
  final int? tripIndex;

  const AddRideActivityScreen({
    Key? key,
    required this.onRideAdded,
    required this.onCancel,
    this.rideDetails,
    this.trip,
    this.tripIndex,
  }) : super(key: key);

  @override
  State<AddRideActivityScreen> createState() => _AddRideActivityScreenState();
}

class _AddRideActivityScreenState extends State<AddRideActivityScreen> {
  final DateTime _selectedDate = DateTime.now();
  final TextEditingController _startTimeController = TextEditingController();
  final TextEditingController _endTimeController = TextEditingController();
  final TextEditingController _riderWeightController = TextEditingController();
  final TextEditingController _pillionWeightController =
      TextEditingController();
  final TextEditingController _observationsController = TextEditingController();
  bool _showMetricsTooltip = false;
  bool _showObservationsTooltip = false;
  Color _vehicleThemeColor = colorGrey800;
  SharedPreferences? sharedPreferences;

  List<Map<String, String>> metrics = [
    {'name': '', 'value': ''}
  ];

  @override
  void initState() {
    super.initState();
    setUpInitialValues();
    _initializePreferences();
  }

  Future<void> _initializePreferences() async {
    sharedPreferences = await SharedPreferences.getInstance();
    if (mounted) {
      setState(() {
        String hexColorInStr =
            sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";
        _vehicleThemeColor =
            hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
      });
    }
  }

  void setUpInitialValues() {
    _startTimeController.text = rideActivityText['text1']!;
    _endTimeController.text = rideActivityText['text2']!;
    _riderWeightController.text = rideActivityText['text3']!;
    _pillionWeightController.text = rideActivityText['text3']!;

    if (widget.trip != null) {
      // Set start time from Trip
      if (widget.trip!.startTime != null) {
        final startDateTime =
            DateTime.fromMillisecondsSinceEpoch(widget.trip!.startTime!);
        _startTimeController.text =
            DateFormat("dd'th' MMMM, yyyy 'At' h:mma").format(startDateTime);
      }

      // Set end time from Trip
      if (widget.trip!.endTime != null) {
        final endDateTime =
            DateTime.fromMillisecondsSinceEpoch(widget.trip!.endTime!);
        _endTimeController.text =
            DateFormat("dd'th' MMMM, yyyy 'At' h:mma").format(endDateTime);
      }
    } else if (widget.rideDetails != null) {
      // Set start time from rideDetails (assuming raw timestamp now)
      if (widget.rideDetails!['startTime'] != null &&
          widget.rideDetails!['startTime'] is int) {
        final startDateTime = DateTime.fromMillisecondsSinceEpoch(
            widget.rideDetails!['startTime']);
        _startTimeController.text =
            DateFormat("dd'th' MMMM, yyyy 'At' h:mma").format(startDateTime);
      }

      // Set end time from rideDetails (assuming raw timestamp now)
      if (widget.rideDetails!['endTime'] != null &&
          widget.rideDetails!['endTime'] is int) {
        final endDateTime =
            DateTime.fromMillisecondsSinceEpoch(widget.rideDetails!['endTime']);
        _endTimeController.text =
            DateFormat("dd'th' MMMM, yyyy 'At' h:mma").format(endDateTime);
      }
    }
  }

  void _addMetricField() {
    setState(() {
      metrics.add({'name': '', 'value': ''});
    });
  }

  void _removeMetricField(int index) {
    if (metrics.length > 1) {
      // Ensure at least one metric always remains
      setState(() {
        metrics.removeAt(index);
      });
    }
  }

// Function to get validation errors for the form
  String? _getValidationError() {
    // Check if we have Trip data with start and end times
    if (widget.trip != null) {
      if (widget.trip!.startTime == null) {
        return rideActivityText['text4']!;
      }
      if (widget.trip!.endTime == null) {
        return rideActivityText['text5']!;
      }
      // No need to check if end time is after start time - Trip data should be valid
    } else {
      // For manual entry (fallback)
      // Check required fields
      if (_startTimeController.text == rideActivityText['text6']! ||
          _startTimeController.text == rideActivityText['text7']!) {
        return rideActivityText['text8']!;
      }
      if (_endTimeController.text == rideActivityText['text9']! ||
          _endTimeController.text == rideActivityText['text10']!) {
        return rideActivityText['text11']!;
      }

      // Parse times to ensure end time is after start time
      final DateFormat formatter = DateFormat("dd'th' MMMM, yyyy 'At' ha");
      DateTime startTime = formatter.parse(_startTimeController.text);
      DateTime endTime = formatter.parse(_endTimeController.text);

      if (endTime.isBefore(startTime)) {
        return rideActivityText['text12']!;
      }
    }

    // Check rider weight (required in both cases)
    if (_riderWeightController.text == rideActivityText['text3']!) {
      return rideActivityText['text13']!;
    }

    // Validate rider weight is at least 10kg
    try {
      double riderWeight = double.parse(_riderWeightController.text
          .replaceAll(rideActivityText['text24']!, ""));
      if (riderWeight <= 0) {
        return rideActivityText['text14']!;
      }
      if (riderWeight < 10) {
        return rideActivityText['text15']!;
      }
    } catch (e) {
      return rideActivityText['text16']!;
    }

    return null; // No errors
  }

// Show a toast message
  void _showToast(String message, {bool isError = false}) {
    isError ? CustomToast.error(message) : CustomToast.whine(message);
  }

  Future<void> _submitRideActivity() async {
    // Validate form
    final String? validationError = _getValidationError();
    if (validationError != null) {
      _showToast(validationError, isError: true);
      return;
    }

    try {
      // Show loading indicator with non-dismissible modal barrier
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async => false, // Prevent back button
            child: Stack(
              children: [
                ModalBarrier(
                  color: Colors.black54,
                  dismissible: false,
                ),
                Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(
                        isTwoWheels
                            ? loaderGifImages['2Wheels']!
                            : loaderGifImages['3Wheels']!,
                      ),
                      SizedBox(height: 20),
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Text(
                            insightsText['text47']!,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      );

      // Get start and end times (prefer Trip data over text fields)
      DateTime startTime;
      DateTime endTime;

      if (widget.trip != null &&
          widget.trip!.startTime != null &&
          widget.trip!.endTime != null) {
        // Use Trip data directly
        startTime =
            DateTime.fromMillisecondsSinceEpoch(widget.trip!.startTime!);
        endTime = DateTime.fromMillisecondsSinceEpoch(widget.trip!.endTime!);
      } else {
        // Fallback to text fields
        final DateFormat formatter = DateFormat("dd'th' MMMM, yyyy 'At' ha");
        startTime = formatter.parse(_startTimeController.text);
        endTime = formatter.parse(_endTimeController.text);
      }

      // Parse rider weight
      double riderWeight = 0;
      if (_riderWeightController.text != rideActivityText['text3']!) {
        riderWeight = double.parse(_riderWeightController.text
            .replaceAll(rideActivityText['text24']!, ""));
      }

      // Parse pillion weight (optional)
      double? pillionWeight;
      if (_pillionWeightController.text != rideActivityText['text3']!) {
        pillionWeight = double.parse(_pillionWeightController.text
            .replaceAll(rideActivityText['text24']!, ""));
      }

      // Create list of metrics
      List<RideMetric> rideMetrics = [];
      List<String> issuesList = [];
      for (var metric in metrics) {
        if (metric['name']!.isNotEmpty && metric['value']!.isNotEmpty) {
          rideMetrics.add(RideMetric(
            name: metric['name']!,
            value: metric['value']!,
          ));

          // Format the metric for the issues list properly
          issuesList.add(metric['name']! + ": " + metric['value']!);
        }
      }

      // If issuesList is empty, add an empty string to make it a valid array with one empty element
      if (issuesList.isEmpty) {
        issuesList.add("");
      }

      // Get IMEI from shared preferences
      final pref = await SharedPreferences.getInstance();
      String imei = pref.getString(connectedVehicleImeiNo) ?? "";

      if (imei.isEmpty) {
        Navigator.of(context).pop(); // Close loading dialog
        _showToast(rideActivityText['text17']!, isError: true);
        return;
      }

      // Prepare observations
      List<String> observationsList = [];
      if (_observationsController.text.isNotEmpty) {
        observationsList.add(_observationsController.text);
      } else {
        // Add empty string to satisfy backend requirement for a non-null array
        observationsList.add("");
      }

      // Calculate total weight
      double totalWeight = riderWeight;
      if (pillionWeight != null) {
        totalWeight += pillionWeight;
      }

      // Prepare request body with all required fields for TestRideFeedbackFormDto
      final Map<String, dynamic> requestBody = {
        "formData": {
          "imei": imei,
          "startTime":
              widget.trip?.startTime ?? startTime.millisecondsSinceEpoch,
          "stopTime": widget.trip?.endTime ?? endTime.millisecondsSinceEpoch,
          "type": "TEST_RIDE",
          "riderName": userInfo?.firstName ?? "Unknown",
          "riderWeight": riderWeight,
          "totalWeight": totalWeight,
          // Required fields in backend that were missing
          "rideStartPlaceName": " ",
          "rideEndPlaceName": " ",
          "batteryManufacturer": " ",
          "observationParameters": observationsList,
          "issues": issuesList,
          // Optional fields with default values
          "roadCondition": [" "],
          "traffic": [" "],
          "climateCondition": [" "],
          "flyoverCount": 0,
          "vehicleDetailsBeforeRide": {
            "additionalProp1": {},
            "additionalProp2": {},
            "additionalProp3": {}
          },
          "vehicleDetailsAfterRide": {
            "additionalProp1": {},
            "additionalProp2": {},
            "additionalProp3": {}
          }
        },
        "userConfirm": true
      };

      // Optional fields
      if (pillionWeight != null) {
        requestBody["formData"]["pillionRiderWeight"] = pillionWeight;
        requestBody["formData"]["pillionRiderName"] = "Pillion Rider";
      }

      // If ride details has distance, add it
      if (widget.rideDetails != null &&
          widget.rideDetails!.containsKey('distance') &&
          widget.rideDetails!['distance'] != 'N/A') {
        try {
          // Try to parse the distance value properly
          String distanceStr = widget.rideDetails!['distance']
              .toString()
              .replaceAll("kms", "")
              .trim();
          double distance = double.parse(distanceStr);
          requestBody["formData"]["distance"] = distance;
        } catch (e) {
          // If parsing fails, don't include the distance
        }
      }

      final response = await BackendApi.initiatePostCall(
        ApiUrls.submitRideActivity,
        body: requestBody,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = json.decode(response.body);
        final testId = responseData['testId'];

        // Function to check validation status
        Future<bool> checkValidationStatus() async {
          try {
            final validationResponse = await BackendApi.initiateGetCall(
              ApiUrls.validateRideActivity,
              params: {'testId': testId.toString()},
            );

            if (validationResponse.statusCode == 200) {
              // Remove unused validationData variable since we don't use it
              Navigator.of(context)
                  .pop(); // Close loading dialog before showing toast
              CustomToast.whine(insightsText['text41']!);

              // Create and return ride activity
              final RideActivity rideActivity = RideActivity(
                id: const Uuid().v4(),
                startTime: startTime,
                endTime: endTime,
                riderWeight: riderWeight,
                pillionWeight: pillionWeight,
                metrics: rideMetrics,
                observations: _observationsController.text.isNotEmpty
                    ? _observationsController.text
                    : null,
              );
              widget.onRideAdded(rideActivity);
              widget.onCancel(); // Navigate back to ride history list
              return true;
            } else if (validationResponse.statusCode == 201) {
              final validationData = json.decode(validationResponse.body);
              final status =
                  RideValidationStatus.fromString(validationData['message']);

              switch (status) {
                case RideValidationStatus.aborted:
                  // Close loading dialog before showing toast
                  Navigator.of(context).pop();
                  CustomToast.error(insightsText['text42']!);
                  // Create and return ride activity for aborted status
                  final RideActivity rideActivity = RideActivity(
                    id: const Uuid().v4(),
                    startTime: startTime,
                    endTime: endTime,
                    riderWeight: riderWeight,
                    pillionWeight: pillionWeight,
                    metrics: rideMetrics,
                    observations: _observationsController.text.isNotEmpty
                        ? _observationsController.text
                        : null,
                  );
                  widget.onRideAdded(rideActivity);
                  widget.onCancel(); // Navigate back to ride history list
                  return true;
                case RideValidationStatus.failed:
                  // Close loading dialog before showing toast
                  Navigator.of(context).pop();
                  CustomToast.error(rideActivityText['text18']!);
                  widget.onCancel(); // Navigate back to ride history list
                  return true;
                case RideValidationStatus.inProgress:
                  // Don't close dialog, keep showing loader
                  return false;
                case null:
                  // Close loading dialog before showing toast
                  Navigator.of(context).pop();
                  CustomToast.error(insightsText['text44']!);
                  widget.onCancel(); // Navigate back to ride history list
                  return true;
              }
            } else {
              // Close loading dialog before showing toast
              Navigator.of(context).pop();
              CustomToast.error(rideActivityText['text18']!);
              // Create and return ride activity for failed validation
              final RideActivity rideActivity = RideActivity(
                id: const Uuid().v4(),
                startTime: startTime,
                endTime: endTime,
                riderWeight: riderWeight,
                pillionWeight: pillionWeight,
                metrics: rideMetrics,
                observations: _observationsController.text.isNotEmpty
                    ? _observationsController.text
                    : null,
              );
              widget.onRideAdded(rideActivity);
              widget.onCancel(); // Navigate back to ride history list
              return true;
            }
          } catch (e) {
            // Close loading dialog before showing toast
            Navigator.of(context).pop();
            CustomToast.error(rideActivityText['text18']!);
            widget.onCancel(); // Navigate back to ride history list
            return true;
          }
        }

        // Start polling for validation status
        bool shouldStop = false;
        while (!shouldStop) {
          shouldStop = await checkValidationStatus();
          if (!shouldStop) {
            await Future.delayed(const Duration(seconds: 10));
          }
        }
      } else if (response.statusCode == 400) {
        // Handle 400 status code
        final responseData = json.decode(response.body);
        Navigator.of(context).pop(); // Close loading dialog
        CustomToast.error(
            responseData['message'] ?? rideActivityText['text18']!);
        widget.onCancel(); // Navigate back to ride history list
      } else {
        // Close loading dialog before showing toast
        Navigator.of(context).pop();
        _showToast(rideActivityText['text18']!, isError: true);
        widget.onCancel(); // Navigate back to ride history list
      }
    } catch (e) {
      // Close loading dialog before showing toast
      Navigator.of(context, rootNavigator: true).pop();
      _showToast("${rideActivityText['text19']!}: ${e.toString()}",
          isError: true);
    }
  }

// Custom method to show custom tooltip popup
  void _showCustomTooltip(BuildContext context, String message, GlobalKey key,
      bool isMetricsTooltip) {
    final RenderBox renderBox =
        key.currentContext!.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);

    // Get the screen size to ensure tooltip stays on screen
    final Size screenSize = MediaQuery.of(context).size;

    final overlay = Overlay.of(context);
    OverlayEntry? entry;

    // Calculate tooltip width based on parent container
    const double tooltipWidth = 220;

    // Calculate left position to ensure tooltip doesn't go off screen
    double leftPosition =
        position.dx - tooltipWidth - 6; // Adjusted to account for arrow
    // Ensure tooltip is not off the left edge
    if (leftPosition < 10) leftPosition = 10;
    // Ensure tooltip is not off the right edge
    if (leftPosition + tooltipWidth > screenSize.width - 10) {
      leftPosition = screenSize.width - tooltipWidth - 10;
    }

    entry = OverlayEntry(
      builder: (context) => Positioned(
        top: position.dy - 40,
        left: leftPosition,
        child: Material(
          color: Colors.transparent,
          child: SizedBox(
            width: tooltipWidth,
            child: Stack(
              children: [
                Positioned(
                  child: CustomPaint(
                    painter: TooltipCustomPainter(),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(8, 8, 14, 8),
                      child: Text(
                        message,
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  right: 0,
                  top: 0,
                  child: GestureDetector(
                    onTap: () {
                      entry?.remove();
                      setState(() {
                        if (isMetricsTooltip) {
                          _showMetricsTooltip = false;
                        } else {
                          _showObservationsTooltip = false;
                        }
                      });
                    },
                    child: Container(
                      width: 20,
                      height: 20,
                      color: Colors.transparent,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    overlay.insert(entry);

    // Auto-dismiss after 4 seconds
    Future.delayed(const Duration(seconds: 2), () {
      entry?.remove();
      setState(() {
        if (isMetricsTooltip) {
          _showMetricsTooltip = false;
        } else {
          _showObservationsTooltip = false;
        }
      });
    });
  }

  Future<void> _selectDateTime(BuildContext context, bool isStartTime) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (pickedDate != null) {
      TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );

      if (pickedTime != null) {
        final DateTime combinedDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          pickedTime.hour,
          pickedTime.minute,
        );

        final String formattedDateTime =
            DateFormat("dd'th' MMMM, yyyy 'At' h:mma").format(combinedDateTime);

        setState(() {
          if (isStartTime) {
            _startTimeController.text = formattedDateTime;
          } else {
            _endTimeController.text = formattedDateTime;
          }
        });
      }
    }
  }

  @override
  void dispose() {
    _startTimeController.dispose();
    _endTimeController.dispose();
    _riderWeightController.dispose();
    _pillionWeightController.dispose();
    _observationsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      IconButton(
                        icon: Icon(Icons.arrow_back),
                        onPressed: widget.onCancel,
                      ),
                      Text(
                        insightsText['text29']!,
                        style: Theme.of(context).textTheme.headlineLarge,
                      ),
                    ],
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(12, 4, 8, 4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      insightsText['text30']!,
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                    SizedBox(
                      height: 8,
                    ),
                    Text(
                      widget.tripIndex != null
                          ? '${rideActivityText['text20']!} #${(widget.tripIndex! + 1)}'
                          : rideActivityText['text20']!,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontSize: 25,
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10,
          ),
          // Time Selectors Container
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: 4 / 414 * dimensions.width,
              vertical: 8 / 896 * dimensions.height,
            ),
            margin: EdgeInsets.only(
              bottom: 16,
              left: 6 / 414 * dimensions.width,
              right: 6 / 414 * dimensions.width,
            ),
            decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(width: 1, color: colorGrey200),
                boxShadow: [
                  BoxShadow(
                      color: colorBlack.withOpacity(0.25),
                      offset: const Offset(1, 3),
                      blurRadius: 3,
                      spreadRadius: 1),
                  BoxShadow(
                      color: colorWhite.withOpacity(0.25),
                      offset: const Offset(-1, -3),
                      blurRadius: 3,
                      spreadRadius: 1)
                ]),
            child: Column(
              children: [
                _buildTimeSelector(context, insightsText['text33']!,
                    _startTimeController, true, dimensions, false),
                Divider(
                    height: 1, color: colorGrey200, indent: 16, endIndent: 16),
                _buildTimeSelector(context, insightsText['text34']!,
                    _endTimeController, false, dimensions, false),
              ],
            ),
          ),
          SizedBox(height: 4),
          // Weight Selectors Container
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: 4 / 414 * dimensions.width,
              vertical: 8 / 896 * dimensions.height,
            ),
            margin: EdgeInsets.only(
              bottom: 16,
              left: 6 / 414 * dimensions.width,
              right: 6 / 414 * dimensions.width,
            ),
            decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(width: 1, color: colorGrey200),
                boxShadow: [
                  BoxShadow(
                      color: colorBlack.withOpacity(0.25),
                      offset: const Offset(1, 3),
                      blurRadius: 3,
                      spreadRadius: 1),
                  BoxShadow(
                      color: colorWhite.withOpacity(0.25),
                      offset: const Offset(-1, -3),
                      blurRadius: 3,
                      spreadRadius: 1)
                ]),
            child: Column(
              children: [
                _buildWeightSelector("${rideActivityText['text21']!}*",
                    _riderWeightController, dimensions, false),
                Divider(
                    height: 1, color: colorGrey200, indent: 16, endIndent: 16),
                _buildWeightSelector(rideActivityText['text22']!,
                    _pillionWeightController, dimensions, false),
              ],
            ),
          ),
          SizedBox(height: 20),
          _buildMetricsSection(dimensions),
          SizedBox(height: 20),
          _buildObservationsSection(dimensions),
          SizedBox(height: 30), // Spacer before submit button
          // Submit Button
          Center(
            child: CustomButton.elevated(
              onPressed: _submitRideActivity,
              backgroundColor: _vehicleThemeColor,
              foregroundColor: Colors.white,
              borderRadius: 30,
              width: 180,
              height: 50,
              padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              text: rideActivityText['text23']!,
              fontSize: 16,
            ),
          ),
          SizedBox(height: 180), // Padding at the bottom for scrolling
        ],
      ),
    );
  }

  Widget _buildTimeSelector(BuildContext context, String label,
      TextEditingController controller, bool isStartTime, Dimensions dimensions,
      [bool hasOwnContainer = true]) {
    return Container(
      margin: hasOwnContainer ? EdgeInsets.only(bottom: 8) : EdgeInsets.zero,
      padding: EdgeInsets.symmetric(vertical: 16, horizontal: 4),
      decoration: hasOwnContainer
          ? BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(width: 1, color: colorGrey200),
              boxShadow: [
                  BoxShadow(
                      color: colorBlack.withOpacity(0.25),
                      offset: const Offset(1, 3),
                      blurRadius: 3,
                      spreadRadius: 1),
                  BoxShadow(
                      color: colorWhite.withOpacity(0.25),
                      offset: const Offset(-1, -3),
                      blurRadius: 3,
                      spreadRadius: 1)
                ])
          : null,
      child: InkWell(
        onTap: widget.trip != null
            ? null
            : () => _selectDateTime(context, isStartTime),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: label,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontSize: 15,
                        ),
                  ),
                  TextSpan(
                    text: " *",
                    style: TextStyle(color: colorRedError),
                  ),
                ],
              ),
            ),
            SizedBox(width: 4),
            Row(
              children: [
                AutoSizeText(
                  controller.text,
                  minFontSize: 8,
                  maxFontSize: 13,
                  style: Theme.of(context).textTheme.displayMedium?.copyWith(
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                SizedBox(width: 2),
                Icon(
                  Icons.chevron_right,
                  color: Colors.grey,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeightSelector(
      String label, TextEditingController controller, Dimensions dimensions,
      [bool hasOwnContainer = true]) {
    // Extract asterisk from label if present and apply red color
    String displayLabel = label;
    bool hasAsterisk = label.contains('*');
    if (hasAsterisk) {
      displayLabel = label.replaceAll('*', '');
    }

    return Container(
      margin: hasOwnContainer ? EdgeInsets.only(bottom: 16) : EdgeInsets.zero,
      padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: hasOwnContainer
          ? BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(width: 1, color: colorGrey200),
              boxShadow: [
                  BoxShadow(
                      color: colorBlack.withOpacity(0.25),
                      offset: const Offset(1, 3),
                      blurRadius: 3,
                      spreadRadius: 1),
                  BoxShadow(
                      color: colorWhite.withOpacity(0.25),
                      offset: const Offset(-1, -3),
                      blurRadius: 3,
                      spreadRadius: 1)
                ])
          : null,
      child: InkWell(
        onTap: () {
          _showWeightDialog(controller, label);
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: displayLabel,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontSize: 15,
                        ),
                  ),
                  if (hasAsterisk ||
                      displayLabel == rideActivityText['text21']!)
                    TextSpan(
                      text: " *",
                      style: TextStyle(color: colorRedError),
                    ),
                ],
              ),
            ),
            Row(
              children: [
                Text(
                  controller.text,
                  style: (controller.text == rideActivityText['text3']! &&
                          displayLabel == rideActivityText['text21']!)
                      ? TextStyle(
                          color: colorRedError,
                          fontStyle: FontStyle.italic,
                          fontSize: 14)
                      : Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontSize: 15,
                            fontWeight: FontWeight.bold,
                          ),
                ),
                SizedBox(width: 2),
                Icon(
                  Icons.chevron_right,
                  color: Colors.grey,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showWeightDialog(TextEditingController controller, String title) {
    String currentValue = controller.text;
    // If the current value is the 'Required' placeholder, treat it as empty for the dialog.
    if (currentValue == rideActivityText['text3']!) {
      currentValue = "";
    } else {
      currentValue = currentValue.replaceAll(rideActivityText['text24']!, "");
    }

    TextEditingController dialogController =
        TextEditingController(text: currentValue);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(title),
              IconButton(
                icon: Icon(Icons.close),
                onPressed: () => Navigator.of(context).pop(),
                padding: EdgeInsets.zero,
                constraints: BoxConstraints(),
                iconSize: 20,
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: dialogController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  suffixText: rideActivityText['text24']!,
                  border: OutlineInputBorder(),
                ),
                autofocus: title.contains(rideActivityText[
                    'text21']!), // Auto-focus on rider weight field
              ),
              SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      if (dialogController.text.isNotEmpty) {
                        try {
                          double weight = double.parse(dialogController.text);
                          if (title.contains(rideActivityText['text21']!) &&
                              weight <= 0) {
                            CustomToast.error(rideActivityText['text27']!);
                            return;
                          }
                          if (title.contains(rideActivityText['text21']!) &&
                              weight < 10) {
                            CustomToast.error(rideActivityText['text28']!);
                            return;
                          }
                          controller.text =
                              "${dialogController.text}${rideActivityText['text24']!}";
                          Navigator.of(context).pop();
                        } catch (e) {
                          CustomToast.error(rideActivityText['text29']!);
                        }
                      } else if (title.contains(rideActivityText['text21']!)) {
                        // Don't allow empty rider weight
                        CustomToast.error(rideActivityText['text30']!);
                      } else {
                        Navigator.of(context).pop();
                      }
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: colorPrimary600,
                    ),
                    child: Text(rideActivityText['text31']!),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMetricsSection(Dimensions dimensions) {
    final GlobalKey metricsInfoKey = GlobalKey();

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 4 / 414 * dimensions.width,
        vertical: 8 / 896 * dimensions.height,
      ),
      margin: EdgeInsets.only(
        bottom: 16,
        left: 6 / 414 * dimensions.width,
        right: 6 / 414 * dimensions.width,
      ),
      decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1, color: colorGrey200),
          boxShadow: [
            BoxShadow(
                color: colorBlack.withOpacity(0.25),
                offset: const Offset(1, 3),
                blurRadius: 3,
                spreadRadius: 1),
            BoxShadow(
                color: colorWhite.withOpacity(0.25),
                offset: const Offset(-1, -3),
                blurRadius: 3,
                spreadRadius: 1)
          ]),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  rideActivityText['text32']!,
                  style: Theme.of(context).textTheme.headlineLarge,
                ),
                GestureDetector(
                  key: metricsInfoKey,
                  onTap: () {
                    setState(() {
                      _showMetricsTooltip = !_showMetricsTooltip;
                    });
                    if (_showMetricsTooltip) {
                      _showCustomTooltip(context, insightsText['text31']!,
                          metricsInfoKey, true);
                    }
                  },
                  child: Icon(
                    Icons.info_outline,
                    color: Colors.grey,
                    size: 24,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemCount: metrics.length,
              separatorBuilder: (context, index) => SizedBox(height: 12),
              itemBuilder: (context, idx) {
                return Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: TextField(
                        decoration: InputDecoration(
                          hintText: rideActivityText['text33']!,
                          hintStyle: TextStyle(
                            fontSize: 10,
                            color: colorGrey600,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                            borderSide: BorderSide(color: colorGrey300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                            borderSide: BorderSide(color: colorGrey300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                            borderSide: BorderSide(
                                color: Theme.of(context).primaryColor),
                          ),
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                        ),
                        onChanged: (value) {
                          setState(() {
                            metrics[idx]['name'] = value;
                          });
                        },
                      ),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: TextField(
                        decoration: InputDecoration(
                          hintText: rideActivityText['text34']!,
                          hintStyle: TextStyle(
                            fontSize: 10,
                            color: colorGrey600,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                            borderSide: BorderSide(color: colorGrey300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                            borderSide: BorderSide(color: colorGrey300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                            borderSide: BorderSide(
                                color: Theme.of(context).primaryColor),
                          ),
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                        ),
                        onChanged: (value) {
                          setState(() {
                            metrics[idx]['value'] = value;
                          });
                        },
                      ),
                    ),
                    SizedBox(width: 8),
                    // Button logic: only last row has plus, all previous (if more than one) have minus
                    if (metrics.length == 1 && idx == 0)
                      // Only one row: show plus
                      Container(
                        height: 48,
                        alignment: Alignment.center,
                        child: Material(
                          color: _vehicleThemeColor,
                          borderRadius: BorderRadius.circular(20),
                          child: InkWell(
                            borderRadius: BorderRadius.circular(20),
                            onTap: _addMetricField,
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Icon(Icons.add,
                                  color: Colors.white, size: 16),
                            ),
                          ),
                        ),
                      )
                    else if (idx == metrics.length - 1)
                      // Last row: show only plus
                      Container(
                        height: 48,
                        alignment: Alignment.center,
                        child: Material(
                          color: _vehicleThemeColor,
                          borderRadius: BorderRadius.circular(20),
                          child: InkWell(
                            borderRadius: BorderRadius.circular(20),
                            onTap: _addMetricField,
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Icon(Icons.add,
                                  color: Colors.white, size: 16),
                            ),
                          ),
                        ),
                      )
                    else if (metrics.length > 1)
                      // All other rows (if more than one): show minus only
                      Container(
                        height: 48,
                        alignment: Alignment.center,
                        child: Material(
                          color: colorRedError,
                          borderRadius: BorderRadius.circular(20),
                          child: InkWell(
                            borderRadius: BorderRadius.circular(20),
                            onTap: () => _removeMetricField(idx),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Icon(Icons.remove,
                                  color: Colors.white, size: 16),
                            ),
                          ),
                        ),
                      ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildObservationsSection(Dimensions dimensions) {
    final GlobalKey observationsInfoKey = GlobalKey();

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 4 / 414 * dimensions.width,
        vertical: 8 / 896 * dimensions.height,
      ),
      margin: EdgeInsets.only(
        bottom: 16,
        left: 6 / 414 * dimensions.width,
        right: 6 / 414 * dimensions.width,
      ),
      decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1, color: colorGrey200),
          boxShadow: [
            BoxShadow(
                color: colorBlack.withOpacity(0.25),
                offset: const Offset(1, 3),
                blurRadius: 3,
                spreadRadius: 1),
            BoxShadow(
                color: colorWhite.withOpacity(0.25),
                offset: const Offset(-1, -3),
                blurRadius: 3,
                spreadRadius: 1)
          ]),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  rideActivityText['text35']!,
                  style: Theme.of(context).textTheme.headlineLarge,
                ),
                GestureDetector(
                  key: observationsInfoKey,
                  onTap: () {
                    setState(() {
                      _showObservationsTooltip = !_showObservationsTooltip;
                    });
                    if (_showObservationsTooltip) {
                      _showCustomTooltip(context, insightsText['text32']!,
                          observationsInfoKey, false);
                    }
                  },
                  child: Icon(
                    Icons.info_outline,
                    color: Colors.grey,
                    size: 24,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            TextField(
              controller: _observationsController,
              decoration: InputDecoration(
                hintText: insightsText['text40']!,
                hintStyle: TextStyle(
                  fontSize: 10,
                  color: colorGrey600,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: BorderSide(color: colorGrey300),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: BorderSide(color: colorGrey300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: BorderSide(color: Theme.of(context).primaryColor),
                ),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              maxLines: 4,
              minLines: 3,
            ),
          ],
        ),
      ),
    );
  }
}

// Custom painter for the tooltip bubble with triangle pointer
class TooltipCustomPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Fill paint
    final Paint fillPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    // Border paint
    final Paint borderPaint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    const double arrowSize = 6;
    const double cornerRadius = 4;

    final path = Path()
      ..moveTo(cornerRadius, 0)
      ..lineTo(size.width - cornerRadius - arrowSize, 0)
      ..quadraticBezierTo(
          size.width - arrowSize, 0, size.width - arrowSize, cornerRadius)
      ..lineTo(size.width - arrowSize, size.height / 2 - arrowSize)
      ..lineTo(size.width, size.height / 2)
      ..lineTo(size.width - arrowSize, size.height / 2 + arrowSize)
      ..lineTo(size.width - arrowSize, size.height - cornerRadius)
      ..quadraticBezierTo(size.width - arrowSize, size.height,
          size.width - arrowSize - cornerRadius, size.height)
      ..lineTo(cornerRadius, size.height)
      ..quadraticBezierTo(0, size.height, 0, size.height - cornerRadius)
      ..lineTo(0, cornerRadius)
      ..quadraticBezierTo(0, 0, cornerRadius, 0);

    canvas.drawShadow(path, Colors.black.withOpacity(0.1), 1, true);
    canvas.drawPath(path, fillPaint);
    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

import 'package:flutter/material.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/company/factoryFiles/trip_history_list_factory.dart';

import '../../common/colors.dart';
import '../../common/strings.dart';
import '../common/time_filter_container.dart';

class TripHistory extends StatefulWidget {
  const TripHistory({super.key});

  @override
  State<TripHistory> createState() => _TripHistoryState();
}

class _TripHistoryState extends State<TripHistory> {
  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Padding(
      padding: EdgeInsets.only(top: 14 / 896 * dimensions.height),
      child: Stack(
        children: [
          Column(
            children: [
              SizedBox(
                height: 52 / 993 * dimensions.height,
              ),
              ConstrainedBox(
                constraints:
                    BoxConstraints(maxHeight: 600 / 993 * dimensions.height),
                child: Container(
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).shadowColor, //New
                        blurRadius: 10.0 / 414 * dimensions.width,
                      )
                    ],
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.vertical(
                          bottom: Radius.circular(8 / 414 * dimensions.width)),
                      color: Theme.of(context).splashColor,
                    ),
                    child: TripHistoryListFactory.createTripHistoryList(),
                  ),
                ),
              )
            ],
          ),
          Container(
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor, //New
                  blurRadius: 10.0,
                )
              ],
            ),
            child: Container(
              width: 374 / 414 * dimensions.width,
              height: 60 / 993 * dimensions.height,
              decoration: BoxDecoration(
                border: Border.all(color: colorWhite),
                borderRadius: BorderRadius.all(
                    Radius.circular(8 / 414 * dimensions.width)),
                color: Theme.of(context).secondaryHeaderColor,
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 16 / 414 * dimensions.width,
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      insightsText['text1']!,
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    const TimeFilterContainer()
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

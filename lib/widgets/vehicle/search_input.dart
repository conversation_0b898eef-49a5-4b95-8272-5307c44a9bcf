import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/utils/concave_decoration.dart';

class SearchInput extends StatelessWidget {
  const SearchInput({super.key, required this.onSearch});

  final void Function(dynamic query) onSearch;

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          height: 56 / 896 * dimensions.height,
          width: dimensions.width,
          margin: const EdgeInsets.all(5.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(
              color: colorGrey300,
              width: 1.0,
            ),
          ),
        ),
        Container(
          height: 55 / 896 * dimensions.height,
          width: dimensions.width,
          margin: const EdgeInsets.all(6.0),
          decoration: ConcaveDecoration(
            alignment: MyApp.of(context).getCurrentThemeMode() == ThemeMode.dark
                ? Alignment.topRight
                : Alignment.topLeft,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6 / 414 * dimensions.width),
            ),
            depth: 3,
            colors: [
              colorWhite,
              Colors.black.withOpacity(0.25),
            ],
            opacity: 0.5,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Icon(
                Icons.search,
                color: colorGrey400,
              ),
              const SizedBox(width: 10.0),
              Expanded(
                child: TextField(
                  onChanged: (query) {
                    onSearch(query);
                  },
                  decoration: InputDecoration(
                    hintText: vehicleScreen["search"],
                    hintStyle: Theme.of(context).textTheme.titleMedium,
                    border: InputBorder.none,
                  ),
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

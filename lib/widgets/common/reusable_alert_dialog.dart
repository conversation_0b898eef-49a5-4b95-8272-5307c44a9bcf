import 'package:flutter/material.dart';

class ReusableAlertDialog {
  static Future show(
      BuildContext context, {
        required Widget content,
        double borderRadius = 12,
        bool barrierDismissible = true,
        Color backgroundColor = Colors.white,
      }) {
    return showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (BuildContext dialogContext) {
        final screenHeight = MediaQuery.of(context).size.height;
        final screenWidth = MediaQuery.of(context).size.width;
        return AlertDialog(
          backgroundColor: backgroundColor, 
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          content: Container(
            width: screenWidth > 600 ? 400 : double.maxFinite,
            constraints: BoxConstraints(
              maxHeight: screenHeight * 0.7,
              minHeight: 0,
            ),
            child: content,
          ),
          contentPadding: const EdgeInsets.all(20),
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 40.0,
            vertical: 24.0,
          ),
        );
      },
    );
  }

  static void close(BuildContext context) {
    if (Navigator.of(context, rootNavigator: true).canPop()) {
      Navigator.of(context, rootNavigator: true).pop();
    }
  }
}

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:nds_app/company/factoryFiles/emergency_sos_factory.dart';
import 'package:nds_app/widgets/common/base_home_view.dart';
import 'package:nds_app/widgets/dashboard/b2c_home/battery_alert.dart';
import 'package:nds_app/widgets/dashboard/b2c_home/battery_info.dart';
import 'package:nds_app/widgets/dashboard/b2c_home/vehicle_banner.dart';
import 'package:nds_app/widgets/dashboard/home/<USER>';
import 'package:nds_app/widgets/dashboard/home/<USER>';
import 'package:nds_app/widgets/dashboard/home/<USER>';
import 'package:nds_app/widgets/dashboard/home/<USER>';
import 'package:nds_app/widgets/common/common_widget.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/utils/extension.dart';
import '../../common/constant.dart';
import '../../common/dimensions.dart';
import '../../common/strings.dart';
import '../../common/colors.dart';
import '../../constant/vehicle_status.dart';
import '../../models/user_info.dart';

/// Configuration class for different home view layouts
class HomeViewConfig {
  final bool showVehicleBanner;
  final bool showBatteryInfo;
  final bool showUserAndVehicleName;
  final bool showBatteryAndRidingModes;
  final bool showBatteryAlerts;
  final bool alwaysShowScanAndFuelSaving;
  final bool showScanAndFuelSavingWhenDisconnected;
  final bool showBatteryAndRidingModesWhenConnected;
  final bool showAutoSOSWhenConnected;

  const HomeViewConfig({
    this.showVehicleBanner = false,
    this.showBatteryInfo = false,
    this.showUserAndVehicleName = false,
    this.showBatteryAndRidingModes = false,
    this.showBatteryAlerts = false,
    this.alwaysShowScanAndFuelSaving = false,
    this.showScanAndFuelSavingWhenDisconnected = false,
    this.showBatteryAndRidingModesWhenConnected = false,
    this.showAutoSOSWhenConnected = false,
  });

  // Predefined configurations for each company
  static const HomeViewConfig b2c = HomeViewConfig(
    showVehicleBanner: true,
    showBatteryInfo: true,
    showBatteryAlerts: true,
    alwaysShowScanAndFuelSaving: true,
    showAutoSOSWhenConnected: true,
  );

  static const HomeViewConfig prodred = HomeViewConfig(
    showUserAndVehicleName: true,
    showScanAndFuelSavingWhenDisconnected: true,
    showBatteryAndRidingModesWhenConnected: true,
    showAutoSOSWhenConnected: true,
  );

  static const HomeViewConfig nds = HomeViewConfig(
    showUserAndVehicleName: true,
    showScanAndFuelSavingWhenDisconnected: true,
    showBatteryAndRidingModesWhenConnected: true,
    showAutoSOSWhenConnected: true,
  );

  static const HomeViewConfig lapa = HomeViewConfig(
    showUserAndVehicleName: true,
    showScanAndFuelSavingWhenDisconnected: true,
    showBatteryAndRidingModesWhenConnected: true,
    showAutoSOSWhenConnected: true,
  );

  static const HomeViewConfig nichesolv = HomeViewConfig(
    showUserAndVehicleName: true,
    showScanAndFuelSavingWhenDisconnected: true,
    showBatteryAndRidingModesWhenConnected: true,
    showAutoSOSWhenConnected: true,
  );

  static HomeViewConfig getConfigForCompany(String companyName) {
    switch (companyName.toLowerCase()) {
      case 'b2c':
        return b2c;
      case 'prodred':
        return prodred;
      case 'nds':
        return nds;
      case 'lapa':
        return lapa;
      case 'nichesolv':
        return nichesolv;
      default:
        return nds; // Default fallback
    }
  }
}

/// Configurable home view that can be customized for different company
class ConfigurableHomeView extends BaseHomeView {
  final HomeViewConfig config;

  const ConfigurableHomeView({
    super.key,
    required super.dashboardAction,
    required super.companyName,
    required this.config,
  });

  @override
  State<ConfigurableHomeView> createState() => _ConfigurableHomeViewState();
}

class _ConfigurableHomeViewState
    extends BaseHomeViewState<ConfigurableHomeView> {
  late Color color;
  late ColorType colorType;

  @override
  void initState() {
    super.initState();
    // Initialize color and colorType similar to other dashboard screens
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    colorType = ColorType.values.firstWhere(
      (element) =>
          element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );

    color = hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;

    // This helps with the initial state when the app starts with a connected vehicle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (currentVehicleStatus == VehicleStatus.connected) {
        vehicleStatusDataStream
            .updateVehicleStatusResponse(currentVehicleStatus);
      }
    });
  }

  @override
  Widget buildHomeContent(BuildContext context, Dimensions dimensions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // B2C specific vehicle banner
        if (widget.config.showVehicleBanner)
          VehicleBanner(
            imageUrl: imageUrl,
            homeUserDiplayName:
                homeDisplayNameOptions[0].contains(homeUserDisplayName)
                    ? userName
                    : vehicleInfo.regNo ?? "",
          ),

        Padding(
          padding: EdgeInsets.only(
            left: 14 / 414 * dimensions.width,
            right: 14 / 414 * dimensions.width,
          ),
          child: Column(
            children: [
              // User and vehicle name (for non-B2C companies)
              if (widget.config.showUserAndVehicleName)
                UserAndVehicleName(
                  currentVehicleStatus: currentVehicleStatus,
                  vehicleInfo: vehicleInfo,
                  firstName: userInfo?.firstName ?? "",
                ),

              // B2C specific battery info
              if (widget.config.showBatteryInfo)
                BatteryInfo(vehicleInfo: vehicleInfo),

              if (widget.config.showBatteryInfo)
                SizedBox(height: 16 / 896 * dimensions.height),

              // Scan and fuel saving - always show for B2C
              if (widget.config.alwaysShowScanAndFuelSaving)
                ScanAndFuelSaving(action: () {}, userInfo: userInfo),

              // Scan and fuel saving - show when disconnected for other companies
              if (widget.config.showScanAndFuelSavingWhenDisconnected)
                Visibility(
                  visible: currentVehicleStatus == VehicleStatus.disconnected,
                  child: ScanAndFuelSaving(action: () {}, userInfo: userInfo),
                ),

              // Battery and riding modes - show when connected for other companies
              if (widget.config.showBatteryAndRidingModesWhenConnected)
                Visibility(
                  visible: currentVehicleStatus == VehicleStatus.connected,
                  child: BatteryAndRidingModes(
                    vehicleInfo: vehicleInfo,
                    userInfo: userInfo,
                  ),
                ),

              // Auto SOS - show when connected for all companies
              if (widget.config.showAutoSOSWhenConnected)
                StreamBuilder<VehicleStatus>(
                  stream: vehicleStatusDataStream.vehicleStatus,
                  initialData: currentVehicleStatus, // Provide initial data
                  builder: (context, snapshot) {
                    // Get the current vehicle status from stream data or fallback to global
                    VehicleStatus currentStatus =
                        snapshot.data ?? currentVehicleStatus;
                    // Always update color and colorType to ensure they're current
                    String hexColorInStr =
                        sharedPreferences!.getString(vehicleThemeColorInHex) ??
                            "";

                    colorType = ColorType.values.firstWhere(
                      (element) =>
                          element.toString() ==
                          sharedPreferences!
                              .getString(vehicleThemeColorTypeKey),
                      orElse: () => ColorType.normal,
                    );

                    color = hexColorInStr.isNotEmpty
                        ? hexColorInStr.toColor()
                        : colorGrey800;
                    return Visibility(
                      visible: currentStatus == VehicleStatus.connected && !(isProdRedUser && isProdRedLeadership),
                      child: Column(
                        children: [
                          SizedBox(height: 16 / 896 * dimensions.height),
                          getAutoSOSContainer(context, dimensions, () {
                            _handleSOSTap();
                          }, color, colorType),
                        ],
                      ),
                    );
                  },
                ),

              SizedBox(height: 16 / 896 * dimensions.height),

              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 6 / 414 * dimensions.width,
                ),
                child: const MapAndAvailableVehicle(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Future<void> handleUserInfoRefresh(JsonDecoder decoder) async {
    if (widget.config.showBatteryAlerts) {
      // B2C specific user info refresh
      UserInfo? freshUserInfo = await getUserInfo(decoder);
      if (freshUserInfo != null) {
        userInfo = freshUserInfo;
        // Ensure UI updates by triggering streams
        if (vehicleInfoConstant != null) {
          vehicleInfo = vehicleInfoConstant!;
          vehicleDataStream.updateVehicleInfo(vehicleInfo);
        }
      }

      // B2C specific battery alerts
      if ((vehicleInfo.charge ?? 0) < 5 &&
          (vehicleInfo.batteryConnected ?? false)) {
        showBatteryLowAlert();
      }
      if ((vehicleInfo.batteryTemperature ?? 0) > 40) {
        showBatteryTemperatureAlert();
      }
    } else {
      // Default implementation for other company
      vehicleInfo = vehicleInfoConstant!;
    }
  }

  void showBatteryLowAlert() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return BatteryStatusAlert(
          message: homeScreenText["battery_low"]!,
        );
      },
    );
  }

  void showBatteryTemperatureAlert() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return BatteryStatusAlert(
          message: homeScreenText["battery_temperature"]!,
        );
      },
    );
  }

  void _handleSOSTap() {
    // Use the same Emergency SOS modal as profile screen
    EmergencySOSFactory.showEmergencySOSDialog(context);
  }
}

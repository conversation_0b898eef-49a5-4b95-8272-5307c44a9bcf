import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/text_styles.dart';

class DataNotAvailableWidget extends StatelessWidget {
  final String message;
  final double? height;
  final double? width;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final Color? textColor;
  final double? fontSize;
  final FontWeight? fontWeight;

  const DataNotAvailableWidget({
    Key? key,
    required this.message,
    this.height,
    this.width,
    this.padding,
    this.backgroundColor,
    this.textColor,
    this.fontSize,
    this.fontWeight,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    
    return Container(
      height: height ?? 80 / 896 * dimensions.height,
      width: width ?? double.infinity,
      padding: padding ?? EdgeInsets.all(16 / 414 * dimensions.width),
      decoration: BoxDecoration(
        color: backgroundColor ?? colorGrey100,
        border: Border.all(color: colorGrey200),
        borderRadius: BorderRadius.circular(8 / 414 * dimensions.width),
      ),
      child: Center(
        child: Text(
          message,
          textAlign: TextAlign.center,
          style: poppinsTextStyle(
            fontSize ?? 14 / 414 * dimensions.width,
            textColor ?? colorGrey400,
            fontWeight ?? FontWeight.w400,
          ),
        ),
      ),
    );
  }

  /// Factory method for riding mode data not available
  factory DataNotAvailableWidget.ridingMode() {
    return const DataNotAvailableWidget(
      message: 'Riding mode data not available',
    );
  }

  /// Factory method for vehicle data not available
  factory DataNotAvailableWidget.vehicleData() {
    return const DataNotAvailableWidget(
      message: 'Vehicle data not available',
    );
  }

  /// Factory method for nearby vehicles data not available
  factory DataNotAvailableWidget.nearbyVehicles() {
    return const DataNotAvailableWidget(
      message: 'No nearby vehicles available',
    );
  }

  /// Factory method for battery data not available
  factory DataNotAvailableWidget.batteryData() {
    return const DataNotAvailableWidget(
      message: 'Battery data not available',
    );
  }

  /// Factory method for statistics data not available
  factory DataNotAvailableWidget.statistics() {
    return const DataNotAvailableWidget(
      message: 'Statistics data not available',
    );
  }
}

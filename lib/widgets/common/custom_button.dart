import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/text_styles.dart';

enum CustomButtonType {
  elevated,
  text,
  outlined,
  icon,
  gesture,
  inkWell,
}

enum CustomButtonSize {
  small,
  medium,
  large,
  fullWidth,
  custom,
}

class CustomButton extends StatelessWidget {
  // Core properties
  final String? text;
  final IconData? icon;
  final Widget? child;
  final VoidCallback? onPressed;
  final VoidCallback? onTap;

  // Button type and size
  final CustomButtonType type;
  final CustomButtonSize size;

  // Styling properties
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? borderColor;
  final Color? textColor;
  final double? borderRadius;
  final double? elevation;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  // Size properties
  final double? width;
  final double? height;
  final double? fontSize;
  final FontWeight? fontWeight;

  // Additional properties
  final bool enabled;
  final bool loading;
  final Widget? loadingWidget;
  final BorderSide? borderSide;
  final OutlinedBorder? shape;
  final AlignmentGeometry? alignment;
  final double? iconSize;
  final Color? splashColor;
  final Color? highlightColor;

  const CustomButton({
    super.key,
    this.text,
    this.icon,
    this.child,
    this.onPressed,
    this.onTap,
    this.type = CustomButtonType.elevated,
    this.size = CustomButtonSize.medium,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.textColor,
    this.borderRadius,
    this.elevation,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.fontSize,
    this.fontWeight,
    this.enabled = true,
    this.loading = false,
    this.loadingWidget,
    this.borderSide,
    this.shape,
    this.alignment,
    this.iconSize,
    this.splashColor,
    this.highlightColor,
  });

  // Factory constructors for widgets button types
  factory CustomButton.elevated({
    Key? key,
    String? text,
    Widget? child,
    VoidCallback? onPressed,
    Color? backgroundColor,
    Color? foregroundColor,
    double? borderRadius,
    double? elevation,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    double? fontSize,
    FontWeight? fontWeight,
    bool enabled = true,
    bool loading = false,
    CustomButtonSize size = CustomButtonSize.medium,
    OutlinedBorder? shape,
  }) {
    return CustomButton(
      key: key,
      text: text,
      onPressed: onPressed,
      type: CustomButtonType.elevated,
      size: size,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      borderRadius: borderRadius,
      elevation: elevation,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      fontSize: fontSize,
      fontWeight: fontWeight,
      enabled: enabled,
      loading: loading,
      shape: shape,
      child: child,
    );
  }

  factory CustomButton.text({
    Key? key,
    String? text,
    Widget? child,
    VoidCallback? onPressed,
    Color? foregroundColor,
    Color? backgroundColor,
    double? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    double? fontSize,
    FontWeight? fontWeight,
    bool enabled = true,
    CustomButtonSize size = CustomButtonSize.medium,
    OutlinedBorder? shape,
  }) {
    return CustomButton(
      key: key,
      text: text,
      onPressed: onPressed,
      type: CustomButtonType.text,
      size: size,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      fontSize: fontSize,
      fontWeight: fontWeight,
      enabled: enabled,
      shape: shape,
      child: child,
    );
  }

  factory CustomButton.outlined({
    Key? key,
    String? text,
    Widget? child,
    VoidCallback? onPressed,
    Color? foregroundColor,
    Color? borderColor,
    double? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    double? fontSize,
    FontWeight? fontWeight,
    bool enabled = true,
    CustomButtonSize size = CustomButtonSize.medium,
    BorderSide? borderSide,
    OutlinedBorder? shape,
  }) {
    return CustomButton(
      key: key,
      text: text,
      onPressed: onPressed,
      type: CustomButtonType.outlined,
      size: size,
      foregroundColor: foregroundColor,
      borderColor: borderColor,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      fontSize: fontSize,
      fontWeight: fontWeight,
      enabled: enabled,
      borderSide: borderSide,
      shape: shape,
      child: child,
    );
  }

  factory CustomButton.icon({
    Key? key,
    IconData? icon,
    Widget? child,
    VoidCallback? onPressed,
    Color? backgroundColor,
    Color? foregroundColor,
    double? iconSize,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    bool enabled = true,
    CustomButtonSize size = CustomButtonSize.medium,
    Color? splashColor,
    Color? highlightColor,
  }) {
    return CustomButton(
      key: key,
      icon: icon,
      onPressed: onPressed,
      type: CustomButtonType.icon,
      size: size,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      iconSize: iconSize,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      enabled: enabled,
      splashColor: splashColor,
      highlightColor: highlightColor,
      child: child,
    );
  }

  factory CustomButton.gesture({
    Key? key,
    String? text,
    Widget? child,
    VoidCallback? onTap,
    Color? backgroundColor,
    Color? textColor,
    double? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    double? fontSize,
    FontWeight? fontWeight,
    bool enabled = true,
    CustomButtonSize size = CustomButtonSize.medium,
    AlignmentGeometry? alignment,
  }) {
    return CustomButton(
      key: key,
      text: text,
      onTap: onTap,
      type: CustomButtonType.gesture,
      size: size,
      backgroundColor: backgroundColor,
      textColor: textColor,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      fontSize: fontSize,
      fontWeight: fontWeight,
      enabled: enabled,
      alignment: alignment,
      child: child,
    );
  }

  factory CustomButton.inkWell({
    Key? key,
    String? text,
    Widget? child,
    VoidCallback? onTap,
    Color? backgroundColor,
    Color? textColor,
    double? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    double? fontSize,
    FontWeight? fontWeight,
    bool enabled = true,
    CustomButtonSize size = CustomButtonSize.medium,
    Color? splashColor,
    Color? highlightColor,
    AlignmentGeometry? alignment,
  }) {
    return CustomButton(
      key: key,
      text: text,
      onTap: onTap,
      type: CustomButtonType.inkWell,
      size: size,
      backgroundColor: backgroundColor,
      textColor: textColor,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      fontSize: fontSize,
      fontWeight: fontWeight,
      enabled: enabled,
      splashColor: splashColor,
      highlightColor: highlightColor,
      alignment: alignment,
      child: child,
    );
  }

  // Convenience factory constructors for widgets use cases
  factory CustomButton.primary({
    Key? key,
    String? text,
    Widget? child,
    VoidCallback? onPressed,
    CustomButtonSize size = CustomButtonSize.medium,
    bool loading = false,
  }) {
    return CustomButton.elevated(
      key: key,
      text: text,
      onPressed: onPressed,
      size: size,
      loading: loading,
      child: child,
    );
  }

  factory CustomButton.secondary({
    Key? key,
    String? text,
    Widget? child,
    VoidCallback? onPressed,
    CustomButtonSize size = CustomButtonSize.medium,
  }) {
    return CustomButton.outlined(
      key: key,
      text: text,
      onPressed: onPressed,
      size: size,
      child: child,
    );
  }

  factory CustomButton.fullWidth({
    Key? key,
    String? text,
    Widget? child,
    VoidCallback? onPressed,
    Color? backgroundColor,
    Color? textColor,
    bool loading = false,
  }) {
    return CustomButton.gesture(
      key: key,
      text: text,
      onTap: onPressed,
      size: CustomButtonSize.fullWidth,
      backgroundColor: backgroundColor,
      textColor: textColor,
      fontWeight: FontWeight.w600,
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    final dimensions = Dimensions(context);

    // Get computed values based on size and context
    final computedWidth = _getComputedWidth(dimensions);
    final computedHeight = _getComputedHeight(dimensions);
    final computedPadding = _getComputedPadding(dimensions);
    final computedFontSize = _getComputedFontSize(dimensions);
    final computedBorderRadius = _getComputedBorderRadius(dimensions);

    // Build the appropriate button type
    Widget button = _buildButtonByType(
      context,
      dimensions,
      computedWidth,
      computedHeight,
      computedPadding,
      computedFontSize,
      computedBorderRadius,
    );

    // Apply margin if specified
    if (margin != null) {
      button = Container(
        margin: margin,
        child: button,
      );
    }

    return button;
  }

  Widget _buildButtonByType(
    BuildContext context,
    Dimensions dimensions,
    double? computedWidth,
    double? computedHeight,
    EdgeInsetsGeometry computedPadding,
    double computedFontSize,
    double computedBorderRadius,
  ) {
    switch (type) {
      case CustomButtonType.elevated:
        return _buildElevatedButton(
          context,
          computedWidth,
          computedHeight,
          computedPadding,
          computedFontSize,
          computedBorderRadius,
        );
      case CustomButtonType.text:
        return _buildTextButton(
          context,
          computedWidth,
          computedHeight,
          computedPadding,
          computedFontSize,
          computedBorderRadius,
        );
      case CustomButtonType.outlined:
        return _buildOutlinedButton(
          context,
          computedWidth,
          computedHeight,
          computedPadding,
          computedFontSize,
          computedBorderRadius,
        );
      case CustomButtonType.icon:
        return _buildIconButton(
          context,
          computedWidth,
          computedHeight,
          computedPadding,
        );
      case CustomButtonType.gesture:
        return _buildGestureButton(
          context,
          computedWidth,
          computedHeight,
          computedPadding,
          computedFontSize,
          computedBorderRadius,
        );
      case CustomButtonType.inkWell:
        return _buildInkWellButton(
          context,
          computedWidth,
          computedHeight,
          computedPadding,
          computedFontSize,
          computedBorderRadius,
        );
    }
  }

  Widget _buildElevatedButton(
    BuildContext context,
    double? computedWidth,
    double? computedHeight,
    EdgeInsetsGeometry computedPadding,
    double computedFontSize,
    double computedBorderRadius,
  ) {
    return SizedBox(
      width: computedWidth,
      height: computedHeight,
      child: ElevatedButton(
        onPressed: enabled && !loading ? (onPressed ?? onTap) : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? Theme.of(context).primaryColor,
          foregroundColor: foregroundColor ?? colorWhite,
          elevation: elevation ?? 2.0,
          padding: computedPadding,
          shape: shape ??
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(computedBorderRadius),
              ),
        ),
        child: _buildButtonContent(context, computedFontSize),
      ),
    );
  }

  Widget _buildTextButton(
    BuildContext context,
    double? computedWidth,
    double? computedHeight,
    EdgeInsetsGeometry computedPadding,
    double computedFontSize,
    double computedBorderRadius,
  ) {
    return SizedBox(
      width: computedWidth,
      height: computedHeight,
      child: TextButton(
        onPressed: enabled && !loading ? (onPressed ?? onTap) : null,
        style: TextButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor ?? Theme.of(context).primaryColor,
          padding: computedPadding,
          shape: shape ??
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(computedBorderRadius),
              ),
        ),
        child: _buildButtonContent(context, computedFontSize),
      ),
    );
  }

  Widget _buildOutlinedButton(
    BuildContext context,
    double? computedWidth,
    double? computedHeight,
    EdgeInsetsGeometry computedPadding,
    double computedFontSize,
    double computedBorderRadius,
  ) {
    return SizedBox(
      width: computedWidth,
      height: computedHeight,
      child: OutlinedButton(
        onPressed: enabled && !loading ? (onPressed ?? onTap) : null,
        style: OutlinedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor ?? Theme.of(context).primaryColor,
          padding: computedPadding,
          side: borderSide ??
              BorderSide(
                color: borderColor ?? Theme.of(context).primaryColor,
                width: 1.0,
              ),
          shape: shape ??
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(computedBorderRadius),
              ),
        ),
        child: _buildButtonContent(context, computedFontSize),
      ),
    );
  }

  Widget _buildIconButton(
    BuildContext context,
    double? computedWidth,
    double? computedHeight,
    EdgeInsetsGeometry computedPadding,
  ) {
    return SizedBox(
      width: computedWidth,
      height: computedHeight,
      child: IconButton(
        onPressed: enabled && !loading ? (onPressed ?? onTap) : null,
        icon: child ??
            Icon(
              icon,
              size: iconSize ?? 24.0,
              color: foregroundColor ?? Theme.of(context).primaryColor,
            ),
        padding: computedPadding,
        splashColor: splashColor,
        highlightColor: highlightColor,
        color: backgroundColor,
      ),
    );
  }

  Widget _buildGestureButton(
    BuildContext context,
    double? computedWidth,
    double? computedHeight,
    EdgeInsetsGeometry computedPadding,
    double computedFontSize,
    double computedBorderRadius,
  ) {
    return GestureDetector(
      onTap: enabled && !loading ? (onTap ?? onPressed) : null,
      child: Container(
        width: computedWidth,
        height: computedHeight,
        padding: computedPadding,
        alignment: alignment ?? Alignment.center,
        decoration: BoxDecoration(
          color: backgroundColor ?? Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(computedBorderRadius),
        ),
        child: _buildButtonContent(context, computedFontSize),
      ),
    );
  }

  Widget _buildInkWellButton(
    BuildContext context,
    double? computedWidth,
    double? computedHeight,
    EdgeInsetsGeometry computedPadding,
    double computedFontSize,
    double computedBorderRadius,
  ) {
    return InkWell(
      onTap: enabled && !loading ? (onTap ?? onPressed) : null,
      splashColor: splashColor,
      highlightColor: highlightColor,
      borderRadius: BorderRadius.circular(computedBorderRadius),
      child: Container(
        width: computedWidth,
        height: computedHeight,
        padding: computedPadding,
        alignment: alignment ?? Alignment.center,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(computedBorderRadius),
        ),
        child: _buildButtonContent(context, computedFontSize),
      ),
    );
  }

  Widget _buildButtonContent(BuildContext context, double computedFontSize) {
    if (loading) {
      return loadingWidget ??
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                foregroundColor ?? colorWhite,
              ),
            ),
          );
    }

    if (child != null) {
      return child!;
    }

    if (text != null) {
      return Text(
        text!,
        style: poppinsTextStyle(
          computedFontSize,
          textColor ?? foregroundColor ?? colorWhite,
          fontWeight ?? FontWeight.w500,
        ),
      );
    }

    if (icon != null) {
      return Icon(
        icon,
        size: iconSize ?? 24.0,
        color: foregroundColor ?? colorWhite,
      );
    }

    return const SizedBox.shrink();
  }

  // Helper methods for computing values based on size and context
  double? _getComputedWidth(Dimensions dimensions) {
    if (width != null) return width;

    switch (size) {
      case CustomButtonSize.small:
        return 80 / 414 * dimensions.width;
      case CustomButtonSize.medium:
        return 120 / 414 * dimensions.width;
      case CustomButtonSize.large:
        return 200 / 414 * dimensions.width;
      case CustomButtonSize.fullWidth:
        return 0.88 * dimensions.width;
      case CustomButtonSize.custom:
        return width;
    }
  }

  double? _getComputedHeight(Dimensions dimensions) {
    if (height != null) return height;

    switch (size) {
      case CustomButtonSize.small:
        return 32 / 896 * dimensions.height;
      case CustomButtonSize.medium:
        return 48 / 896 * dimensions.height;
      case CustomButtonSize.large:
        return 56 / 896 * dimensions.height;
      case CustomButtonSize.fullWidth:
        return 0.067 * dimensions.height;
      case CustomButtonSize.custom:
        return height;
    }
  }

  EdgeInsetsGeometry _getComputedPadding(Dimensions dimensions) {
    if (padding != null) return padding!;

    switch (size) {
      case CustomButtonSize.small:
        return EdgeInsets.symmetric(
          horizontal: 8 / 414 * dimensions.width,
          vertical: 4 / 896 * dimensions.height,
        );
      case CustomButtonSize.medium:
        return EdgeInsets.symmetric(
          horizontal: 16 / 414 * dimensions.width,
          vertical: 8 / 896 * dimensions.height,
        );
      case CustomButtonSize.large:
        return EdgeInsets.symmetric(
          horizontal: 24 / 414 * dimensions.width,
          vertical: 12 / 896 * dimensions.height,
        );
      case CustomButtonSize.fullWidth:
        return EdgeInsets.symmetric(
          horizontal: 20 / 414 * dimensions.width,
          vertical: 16 / 896 * dimensions.height,
        );
      case CustomButtonSize.custom:
        return EdgeInsets.zero;
    }
  }

  double _getComputedFontSize(Dimensions dimensions) {
    if (fontSize != null) return fontSize!;

    switch (size) {
      case CustomButtonSize.small:
        return 12 / 414 * dimensions.width;
      case CustomButtonSize.medium:
        return 16 / 414 * dimensions.width;
      case CustomButtonSize.large:
        return 18 / 414 * dimensions.width;
      case CustomButtonSize.fullWidth:
        return 0.02 * dimensions.height;
      case CustomButtonSize.custom:
        return 16 / 414 * dimensions.width;
    }
  }

  double _getComputedBorderRadius(Dimensions dimensions) {
    if (borderRadius != null) return borderRadius!;

    switch (size) {
      case CustomButtonSize.small:
        return 4.0;
      case CustomButtonSize.medium:
        return 8.0;
      case CustomButtonSize.large:
        return 12.0;
      case CustomButtonSize.fullWidth:
        return 8.0;
      case CustomButtonSize.custom:
        return 8.0;
    }
  }
}

import 'package:flutter/material.dart';
import '../../common/constant.dart';
import '../../common/image_urls.dart';

getCircularProgressIndicator(BuildContext context) {
  return showDialog(
    barrierDismissible: false,
    builder: (ctx) {
      return Center(
        child: Image.asset(
          isTwoWheels ?
          loaderGifImages['2Wheels']! : loaderGifImages['3Wheels']!,
        ),
      );
    },
    context: context,
  );
}
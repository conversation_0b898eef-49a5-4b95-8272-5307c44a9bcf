import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/company/factoryFiles/dashboard_factory.dart';
import 'package:nds_app/utils/toast.dart';
import 'package:nds_app/widgets/common/progress_indicator.dart';

Future getBotttomNoInternetConnection({
  required heading,
  required context,
}) {
  Dimensions dimensions = Dimensions(context);
  isBottomSheetOpenNotifier.value = true;
  double width = dimensions.width;
  double height = dimensions.height;

  return showModalBottomSheet(
    enableDrag: false,
    isScrollControlled: false,
    isDismissible: false,
    scrollControlDisabledMaxHeightRatio: 1,
    backgroundColor: Colors.transparent,
    shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
      top: Radius.circular(20),
    )),
    context: context,
    builder: (context) {
      bool isPortrait =
          MediaQuery.of(context).orientation == Orientation.portrait;

      if (!isPortrait) {
        double temp = width;
        width = height;
        height = temp;
      }
      return PopScope(
        canPop: false,
        child: Container(
          height: 240 / 896 * height,
          decoration: BoxDecoration(
              color: Theme.of(context).splashColor,
              border: Border(
                  top: BorderSide(
                width: 4 / 896 * dimensions.height,
                color: offlineColorRed,
              ))),
          child: Padding(
            padding: EdgeInsets.only(
              left: 16 / 414 * width,
              right: 16 / 414 * width,
              top: 24 / 896 * height,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                FittedBox(
                  child: Text(
                    heading,
                    style: poppinsTextStyle(
                      20 / 414 * width,
                      offlineColorRed,
                      FontWeight.w600,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(
                  height: 8 / 896 * height,
                ),
                Text(
                  noInternetConnectionText["text2"]!,
                  style: Theme.of(context)
                      .textTheme
                      .titleSmall
                      ?.copyWith(fontSize: 16 / 414 * width),
                ),
                Align(
                  alignment: Alignment.bottomRight,
                  child: getTryAgainButton(width, height, context, isPortrait),
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
}

getTryAgainButton(
    double width, double height, BuildContext context, bool isPortrait) {
  return InkWell(
    onTap: () async {
      getCircularProgressIndicator(context);
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        CustomToast.message(toastMessageText['text6']!,
            gravity: ToastGravity.TOP);
        // ignore: use_build_context_synchronously
        Navigator.of(context).pop();
      } else {
        isBottomSheetOpenNotifier.value = false;
        int count = 0;

        await Navigator.pushAndRemoveUntil(
            // ignore: use_build_context_synchronously
            context,
            MaterialPageRoute(
                builder: (context) => DashboardFactory.createDashboard()),
            (r) => count++ >= 2);
      }
    },
    child: Container(
        alignment: Alignment.bottomRight,
        height: 44 / 896 * height,
        width: 107 / 414 * width,
        margin: EdgeInsets.only(top: 20 / 896 * height),
        padding: EdgeInsets.symmetric(horizontal: 12 / 414 * width),
        decoration: BoxDecoration(
            gradient: LinearGradient(
                colors:
                    MyApp.of(context).getCurrentThemeMode() == ThemeMode.dark
                        ? [colorGrey800, colorBlack]
                        : [colorGrey200, colorWhite],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight),
            borderRadius: BorderRadius.all(Radius.circular(40 / 414 * width)),
            border: Border.all(color: colorGrey300),
            boxShadow: [
              BoxShadow(
                  color:
                      MyApp.of(context).getCurrentThemeMode() == ThemeMode.dark
                          ? colorGrey500.withOpacity(0.7)
                          : colorGrey300.withOpacity(0.7),
                  offset: const Offset(3, 3),
                  spreadRadius: 2,
                  blurRadius: 5)
            ]),
        child: Center(
          child: Text(
            noInternetConnectionText["text3"]!,
            textScaler: const TextScaler.linear(1.0),
            style: isPortrait
                ? Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w400,
                    )
                : Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w400, fontSize: 16 / 896 * height),
            textAlign: TextAlign.center,
          ),
        )),
  );
}

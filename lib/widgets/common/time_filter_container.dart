import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';

import '../../common/dimensions.dart';

class TimeFilterContainer extends StatelessWidget {
  const TimeFilterContainer({super.key});

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return Container(
      width: 108 / 414 * dimensions.width,
      height: 28 / 896 * dimensions.height,
      decoration: BoxDecoration(
        border: Border.all(color: colorGrey600),
        borderRadius:
            BorderRadius.all(Radius.circular(8 / 414 * dimensions.width)),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 8 / 414 * dimensions.width,
        ),
        child: FittedBox(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                insightsText['text2']!,
                style: poppinsTextStyle(
                    12 / 414 * dimensions.width, colorGrey600, FontWeight.w400),
              ),
              SizedBox(
                  width: 20 / 414 * dimensions.width,
                  height: 20 / 993 * dimensions.height,
                  child: FittedBox(
                    fit: BoxFit.fill,
                    child: Icon(
                      Icons.filter_list,
                      color: Theme.of(context).highlightColor,
                    ),
                  ))
            ],
          ),
        ),
      ),
    );
  }
}

import 'dart:math' as math;

import 'package:flutter/material.dart';

class CustomHalfCircle extends StatelessWidget {
  final double diameter;
  final Color color;

  const CustomHalfCircle({super.key, this.diameter = 200, required this.color});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: <PERSON><PERSON><PERSON><PERSON>(color: color),
      size: Size(diameter, diameter),
    );
  }
}

// This is the Painter class
class MyPainter extends CustomPainter {
  final Color color;
  MyPainter({
    required this.color,
  });
  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()..color = color;
    canvas.drawArc(
      Rect.fromCenter(
        center: Offset(size.height / 2, size.width / 2),
        height: size.height,
        width: size.width,
      ),
      0,
      math.pi,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

import 'package:flutter/material.dart';

class CircularArc extends StatelessWidget {
  final double diameter;
  final Color color;
  final double startAngle;
  final double sweepAngle;

  const CircularArc(
      {super.key,
      this.diameter = 200,
      required this.color,
      required this.startAngle,
      required this.sweepAngle});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: <PERSON><PERSON><PERSON><PERSON>(
          color: color, startAngle: startAngle, sweepAngle: sweepAngle),
      size: Size(diameter, diameter),
    );
  }
}

// This is the Painter class
class MyPainter extends CustomPainter {
  final Color color;
  final double startAngle;
  final double sweepAngle;
  MyPainter({
    required this.startAngle,
    required this.sweepAngle,
    required this.color,
  });
  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    canvas.drawArc(
      Rect.fromCenter(
        center: Offset(size.height / 2, size.width / 2),
        height: size.height,
        width: size.width,
      ),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

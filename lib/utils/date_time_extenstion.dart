extension DateTimeExtension on DateTime {
  String timeAgo({bool numericDates = true}) {
    final date2 = DateTime.now();
    final difference = date2.difference(this);

    if ((difference.inDays / 365).floor() >= 2) {
      return '${(difference.inDays / 365).round()} yrs ago';
    } else if ((difference.inDays / 365).floor() >= 1) {
      return (numericDates) ? '1 yr ago' : 'Last yr';
    } else if ((difference.inDays / 30).floor() >= 2) {
      return '${(difference.inDays / 30).round()} mos ago';
    } else if ((difference.inDays / 30).floor() >= 1) {
      return (numericDates) ? '1 mo ago' : 'Last mo';
    } else if ((difference.inDays / 7).floor() >= 2) {
      return '${(difference.inDays / 7).round()} wks ago';
    } else if ((difference.inDays / 7).floor() >= 1) {
      return (numericDates) ? '1 wk ago' : 'Last wk';
    } else if (difference.inDays >= 2) {
      return '${difference.inDays} d ago';
    } else if (difference.inDays >= 1) {
      return (numericDates) ? '1 d ago' : 'yest';
    } else if (difference.inHours >= 2) {
      return '${difference.inHours} hrs ago';
    } else if (difference.inHours >= 1) {
      return (numericDates) ? '1 hr ago' : 'An hr ago';
    } else if (difference.inMinutes >= 2) {
      return '${difference.inMinutes} mins ago';
    } else if (difference.inMinutes >= 1) {
      return (numericDates) ? '1 min ago' : 'A min ago';
    } else if (difference.inSeconds >= 3) {
      return '${difference.inSeconds} secs ago';
    } else {
      return 'Just now';
    }
  }
}

import 'package:intl/intl.dart';

String formatDate(String inputDate) {
  String outputDate = "";
  if (isDateValid(inputDate)) {
    final inputFormat = DateFormat('yyyy-MM-dd');
    final date = inputFormat.parse(inputDate);

    final outputFormat = DateFormat('dd MMMM, y');
    outputDate = outputFormat.format(date);
  }
  return outputDate;
}

bool isDateValid(String inputDate) {
  final RegExp datePattern = RegExp(r'^\d{4}-\d{2}-\d{2}$');
  return datePattern.hasMatch(inputDate);
}

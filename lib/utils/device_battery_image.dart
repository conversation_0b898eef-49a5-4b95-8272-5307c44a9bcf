import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/utils/convax_decoration_rectangle.dart';

import '../common/dimensions.dart';
import '../common/image_urls.dart';
import '../common/strings.dart';
import '../common/text_styles.dart';

Widget getBatteryPercentageContainer(
    Dimensions dimensions, int charge, BuildContext context) {
  Color color = batteryContainerBackgroundColor(context, charge);
  return Stack(
    children: [
      Container(
        width: 58 / 414 * dimensions.width,
        height: 26 / 896 * dimensions.height,
        decoration: BoxDecoration(
            border: Border.all(width: 1, color: color),
            color: color,
            borderRadius: BorderRadius.all(
              Radius.circular(17 / 414 * dimensions.width),
            )),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            deviceBatteryImage(context, charge),
            SizedBox(
              width: 4 / 414 * dimensions.width,
            ),
            Text(homeScreenText["text11"]!.replaceAll("@value", "$charge"),
                style: poppinsTextStyle(
                    12 / 414 * dimensions.width, colorWhite, FontWeight.w400))
          ],
        ),
      ),
      Container(
        width: 56 / 414 * dimensions.width,
        height: 24 / 896 * dimensions.height,
        margin: EdgeInsets.only(
            left: 1 / 414 * dimensions.width, top: 1 / 896 * dimensions.height),
        decoration: ConvexDecorationRectangle(
          depth: 3,
          colors: [
            colorWhite.withOpacity(0.3),
            colorBlack.withOpacity(0.3),
          ],
          opacity: 0.8,
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    ],
  );
}

Widget deviceBatteryImage(BuildContext context, int batteryStatus) {
  Dimensions dimensions = Dimensions(context);
  return Icon(
    batteryStatus == -1
        ? batteryLevelImages[8][0]
        : (batteryStatus >= 0 && batteryStatus <= 100)
            ? batteryLevelImages[getIndex(batteryStatus)][0]
            : batteryLevelImages[9][0],
    size: 14 / 414 * dimensions.width,
    color: colorWhite,
  );
}

Color batteryContainerBackgroundColor(BuildContext context, int batteryStatus) {
  return batteryStatus == -1
      ? batteryLevelImages[8][1]
      : (batteryStatus >= 0 && batteryStatus <= 100)
          ? batteryLevelImages[getIndex(batteryStatus)][1]
          : batteryLevelImages[9][1];
}

int getIndex(int batteryStatus) {
  return ((batteryStatus / 10) * 0.7).round();
}

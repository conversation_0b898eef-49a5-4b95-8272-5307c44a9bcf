import 'package:flutter/material.dart';

class GradientTriangle extends StatelessWidget {
  final double width;
  final double height;
  final List<Color> colors;
  const GradientTriangle(
      {super.key,
      required this.width,
      required this.height,
      required this.colors});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: <PERSON><PERSON>(width, height),
      painter: Grad<PERSON>TrianglePainter(colors: colors),
    );
  }
}

class GradientTrianglePainter extends CustomPainter {
  GradientTrianglePainter({required this.colors});
  final List<Color> colors;

  @override
  void paint(Canvas canvas, Size size) {
    Rect rect = Offset.zero & size;

    var paint = Paint()
      ..style = PaintingStyle.fill
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: colors,
      ).createShader(rect)
      ..maskFilter = const MaskFilter.blur(BlurStyle.inner, 7.0);

    var path = Path();
    path.moveTo(0, 0);
    path.lineTo(size.width, 0);
    path.lineTo(size.width / 2, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

import 'package:flutter/material.dart';
import 'package:nds_app/common/dimensions.dart';

import '../common/colors.dart';
import '../common/image_urls.dart';
import '../common/text_styles.dart';
import 'calculate_data.dart';

class GradientCircleChargeIndicator extends StatelessWidget {
  final double radius;
  final List<Color> colors;
  final int timeRemainingInMin;
  const GradientCircleChargeIndicator(
      {super.key,
      required this.radius,
      required this.colors,
      required this.timeRemainingInMin});

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return CustomPaint(
      painter: GradientCircleChargePainter(radius: radius, colors: colors),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 25 / 896 * dimensions.height,
          ),
          Text(
            getTimeInCorrectFormatByMinutes(timeRemainingInMin),
            style: poppinsTextStyle(
                14 / 414 * dimensions.width, colorGrey600, FontWeight.w600),
          ),
          SizedBox(
              width: 20 / 414 * dimensions.width,
              height: 20 / 896 * dimensions.height,
              child: Image.asset(
                homeScreenImages["bolt_fill"]!,
                fit: BoxFit.fill,
              ))
        ],
      ),
    );
  }
}

class GradientCircleChargePainter extends CustomPainter {
  final double radius;
  final List<Color> colors;
  GradientCircleChargePainter({required this.colors, required this.radius});
  @override
  void paint(Canvas canvas, Size size) {
    size = Size.fromRadius(radius);

    Offset offset = Offset(radius, radius);

    Paint paint = Paint()
      ..style = PaintingStyle.fill
      ..shader = RadialGradient(colors: colors, stops: const [0.8, 1])
          .createShader(Rect.fromCircle(
        center: offset,
        radius: radius,
      ));

    canvas.drawCircle(offset, radius, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

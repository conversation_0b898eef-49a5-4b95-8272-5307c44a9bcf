import 'package:flutter/material.dart';
import 'package:nds_app/widgets/common/reusable_alert_dialog.dart';
import 'package:nds_app/widgets/vehicle/reusableWidgets/header_trailer_text_row.dart';

class AlertDialogUtils {
  /// Show snackbar for no data or alert dialog for vehicle data
  ///
  /// [context] - BuildContext for showing the snackbar/dialog
  /// [imeiList] - List of vehicle IMEI numbers to display
  /// [emptyMessage] - Custom message to show when no data (optional)
  static void showVehicleDataOrSnackbar(
    BuildContext context,
    List<String> imeiList, {
    String emptyMessage = 'No vehicle data available',
  }) {
    if (imeiList.isEmpty) {
      // Show snackbar for no data
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(emptyMessage),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      // Show alert dialog with vehicle list when data exists
      showVehicleAlertDialog(context, imeiList);
    }
  }

  /// Show a reusable alert dialog for vehicle data (kept for single vehicle percentage dialogs)
  ///
  /// [context] - BuildContext for showing the dialog
  /// [imeiList] - List of vehicle IMEI numbers to display
  /// [emptyMessage] - Custom message to show when no data (optional)
  static void showVehicleAlertDialog(
    BuildContext context,
    List<String> imeiList, {
    String emptyMessage = 'No vehicle data available',
  }) {
    ReusableAlertDialog.show(
      context,
      content: imeiList.isEmpty
          ? Center(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Text(
                  emptyMessage,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            )
          : ListView.separated(
              itemCount: imeiList.length,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (BuildContext context, int index) {
                return HeaderTrailerTextRow(
                  header: "Vehicle ${index + 1}",
                  trailer: imeiList[index],
                  fontSize: 14,
                );
              },
              separatorBuilder: (BuildContext context, int index) {
                return const SizedBox(height: 16);
              },
            ),
    );
  }
}

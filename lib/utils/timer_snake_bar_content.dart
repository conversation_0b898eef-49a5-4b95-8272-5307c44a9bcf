import 'dart:async';

import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/text_styles.dart';

class TimerSnackBarContent extends StatefulWidget {
  final String message;
  const TimerSnackBarContent({Key? key, required this.message})
      : super(key: key);

  @override
  TimerSnackBarContentState createState() => TimerSnackBarContentState();
}

class TimerSnackBarContentState extends State<TimerSnackBarContent> {
  int remainingTime = 3;
  Timer? timer;

  @override
  void initState() {
    super.initState();

    startTimer();
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  void startTimer() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (remainingTime == 0) {
        timer.cancel();
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      } else {
        setState(() {
          remainingTime--;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    double height = dimensions.height;
    double width = dimensions.width;
    if (MediaQuery.orientationOf(context) == Orientation.landscape) {
      double temp = width;
      width = height;
      height = temp;
    }

    return SizedBox(
      height: 20 / 896 * height,
      child: Align(
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              widget.message,
              style: poppinsTextStyle(
                  16 / 414 * width, colorWhite, FontWeight.w400),
            ),
            Text(
              remainingTime > 1 ? '$remainingTime Secs' : '$remainingTime Sec',
              style: poppinsTextStyle(
                  16 / 414 * width, colorWhite, FontWeight.w400),
            )
          ],
        ),
      ),
    );
  }
}

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/constant/alert.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/models/app_alert.dart';
import 'package:nds_app/models/enums/alert_type.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/widgets/alert/alert_message_without_close_button_action.dart';
import 'package:nds_app/widgets/alert/temparature_normal_alert_box.dart';
import 'package:nds_app/widgets/alert/temperature_high_alert_box.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

checkAlert(BuildContext context) async {
  final pref = await SharedPreferences.getInstance();
  JsonDecoder decoder = const JsonDecoder();
  await Future.delayed(const Duration(seconds: 10));

  if (currentVehicleStatus == VehicleStatus.connected) {
    String imei = pref.getString(connectedVehicleImeiNo) ?? "";
    http.Response alertResponse =
        await BackendApi.initiateGetCall(ApiUrls.alert, params: {"imei": imei});
    Map<String, dynamic> alertResponseBody =
        decoder.convert(alertResponse.body);
    AppAlert alert = AppAlert.fromJson(alertResponseBody);
    TempAlertType? tempAlertType = alert.tempAlert!.tempAlertType;

    if ((currentTempAlertType == TempAlertType.high &&
            isTempAlertMessageExist == true &&
            alert.tempAlert!.baseTemp! > alert.tempAlert!.currentTemp) ||
        (currentTempAlertType == TempAlertType.normal &&
            tempAlertType == TempAlertType.none)) {
      Navigator.of(currentContext!).pop();
      currentTempAlertType = TempAlertType.normal;

      // ignore: use_build_context_synchronously
      getNormalTempratureAlertBox(
          currentContext!, Alert.normalTemp, alert.tempAlert!.baseTemp!);
    } else if (tempAlertType != currentTempAlertType ||
        (alert.tempAlert!.currentTemp > currentMaxTemp!)) {
      if (isTempAlertMessageExist == true) {
        // ignore: use_build_context_synchronously
        Navigator.of(currentContext!).pop();
        isTempAlertMessageExist = false;
      }
      if (tempAlertType == TempAlertType.low) {
        currentTempAlertType = TempAlertType.low;
        isTempAlertMessageExist = true;
        // ignore: use_build_context_synchronously
        getAlertMessageWithoutCloseButtonAction(currentContext!, Alert.lowTemp);
      } else if (tempAlertType == TempAlertType.medium) {
        currentTempAlertType = TempAlertType.medium;
        isTempAlertMessageExist = true;
        getAlertMessageWithoutCloseButtonAction(
            currentContext!, Alert.mediumTemp);
      } else if (tempAlertType == TempAlertType.high) {
        currentTempAlertType = TempAlertType.high;
        isTempAlertMessageExist = true;
        getHighTempratureAlertBox(
            currentContext!, Alert.highTemp, alert.tempAlert!);
      }
    }

    currentMaxTemp = alert.tempAlert?.currentTemp;
  }
  await Future.delayed(const Duration(seconds: 110));

  // ignore: use_build_context_synchronously
  checkAlert(context);
}

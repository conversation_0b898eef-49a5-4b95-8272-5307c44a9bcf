import 'dart:ui' as ui;
import 'package:flutter/material.dart';

class ConvexDecorationRectangle extends Decoration {
  final double depth;
  final List<Color> colors;
  final double opacity;
  final BorderRadius borderRadius;

  const ConvexDecorationRectangle({
    required this.depth,
    this.colors = const [Colors.black87, Colors.white],
    this.opacity = 1.0,
    this.borderRadius = BorderRadius.zero,
  }) : assert(colors.length == 2);

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) =>
      _ConvexDecorationPainter(depth, colors, opacity, borderRadius);

  @override
  Decoration? lerpFrom(Decoration? a, double t) {
    if (a is ConvexDecorationRectangle) {
      t = Curves.easeInOut.transform(t);
      return ConvexDecorationRectangle(
        depth: ui.lerpDouble(a.depth, depth, t)!,
        colors: [
          Color.lerp(a.colors[0], colors[0], t)!,
          Color.lerp(a.colors[1], colors[1], t)!,
        ],
        opacity: ui.lerpDouble(a.opacity, opacity, t)!,
        borderRadius: BorderRadius.lerp(a.borderRadius, borderRadius, t)!,
      );
    }
    return super.lerpFrom(a, t);
  }

  @override
  Decoration? lerpTo(Decoration? b, double t) {
    if (b is ConvexDecorationRectangle) {
      t = Curves.easeInOut.transform(t);
      return ConvexDecorationRectangle(
        depth: ui.lerpDouble(depth, b.depth, t)!,
        colors: [
          Color.lerp(colors[0], b.colors[0], t)!,
          Color.lerp(colors[1], b.colors[1], t)!,
        ],
        opacity: ui.lerpDouble(opacity, b.opacity, t)!,
        borderRadius: BorderRadius.lerp(borderRadius, b.borderRadius, t)!,
      );
    }
    return super.lerpTo(b, t);
  }
}

class _ConvexDecorationPainter extends BoxPainter {
  double depth;
  List<Color> colors;
  final double opacity;
  final BorderRadius borderRadius;

  _ConvexDecorationPainter(
      this.depth, this.colors, this.opacity, this.borderRadius) {
    if (depth > 0) {
      colors = [colors[1], colors[0]];
    } else {
      depth = -depth;
    }
    colors = [
      colors[0].withOpacity(opacity),
      colors[1].withOpacity(opacity),
    ];
  }

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final rect = offset & configuration.size!;
    final shapePath = Path()..addRRect(borderRadius.toRRect(rect));

    final delta = 16 / rect.longestSide;
    final stops = [0.5 - delta, 0.5 + delta];

    final path = Path()
      ..fillType = PathFillType.evenOdd
      ..addRect(rect.inflate(depth * 2))
      ..addPath(shapePath, Offset.zero);

    canvas.save();
    canvas.clipPath(shapePath);

    final paint = Paint()
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, depth);

    // Top-left to bottom-right depth shading
    final shaderRect =
        Alignment.topLeft.inscribe(Size.square(rect.longestSide), rect);
    paint.shader = ui.Gradient.linear(
      shaderRect.topLeft,
      shaderRect.bottomRight,
      colors,
      stops,
    );

    canvas.drawPath(path, paint);
    canvas.restore();
  }
}

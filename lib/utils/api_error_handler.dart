import 'package:nds_app/utils/toast.dart';
import 'package:flutter/foundation.dart';

class ApiErrorHandler {
  /// Shows appropriate error message based on API failure
  static void showApiError(String apiName, {String? customMessage}) {
    String lower = apiName.toLowerCase();
    // Silence nearby vehicles related errors: log only, no user-facing toast
    if (lower == 'availablevehicles' || lower == 'users/nearby-poi' || lower == 'nearby-poi') {
      debugPrint(customMessage ?? _getDefaultErrorMessage(apiName));
      return;
    }

    String message = customMessage ?? _getDefaultErrorMessage(apiName);
    CustomToast.error(message);
  }

  /// Shows data not available message
  static void showDataNotAvailable(String dataType) {
    CustomToast.message(
        '$dataType data is not available right now. Please try again later.');
  }

  /// Shows connection error message
  static void showConnectionError() {
    CustomToast.error(
        'Unable to connect to server. Please check your internet connection and try again.');
  }

  /// Shows timeout error message
  static void showTimeoutError() {
    CustomToast.error('Request timed out. Please try again.');
  }

  /// Shows server error message
  static void showServerError() {
    CustomToast.error('Server error occurred. Please try again later.');
  }

  /// Gets default error message for specific API
  static String _getDefaultErrorMessage(String apiName) {
    switch (apiName.toLowerCase()) {
      case 'vehicleinfo':
      case 'vehicles/info':
        return 'Unable to fetch vehicle information. Please check your connection and try again.';

      case 'availablevehicles':
      case 'users/nearby-poi':
      case 'nearby-poi':
        return 'Unable to fetch nearby vehicles. Please check your connection and try again.';

      case 'connectvehicle':
      case 'vehicles/connect':
        return 'Unable to connect to vehicle. Please check the vehicle and try again.';

      case 'disconnectvehicle':
      case 'vehicles/disconnect':
        return 'Unable to disconnect vehicle. Please try again.';

      case 'userinfo':
      case 'users':
        return 'Unable to fetch user information. Please check your connection and try again.';

      case 'statistics':
      case 'insights':
        return 'Unable to fetch statistics data. Please check your connection and try again.';

      case 'profile':
      case 'profile details':
        return 'Unable to fetch profile information. Please check your connection and try again.';

      default:
        return 'Unable to fetch data. Please check your connection and try again.';
    }
  }

  /// Handles HTTP status code and shows appropriate message
  static void handleHttpError(int statusCode, String apiName) {
    switch (statusCode) {
      case 400:
        if (apiName.toLowerCase() == 'vehicleinfo') {
          debugPrint('Vehicle not found error - handled silently');
          return;
        }
        if (apiName.toLowerCase() == 'connectvehicle') {
          /*CustomToast.error(
              'Invalid vehicle code. Please check and try again.');*/
          debugPrint('Invalid vehicle code. Please check and try again.');
          return;
        }
        debugPrint('Invalid request. Please try again. ');
       /* CustomToast.error('Invalid request. Please try again.');*/
        break;
      case 401:
        debugPrint('Authentication failed. Please login again. ');
       /* CustomToast.error(' Authentication failed. Please login again.');*/
        break;
      case 403:
        debugPrint('Access denied. Please check your permissions.');
       /* CustomToast.error('Access denied. Please check your permissions.');*/
        break;
      case 404:
        if (apiName.toLowerCase() == 'vehicleinfo') {
          debugPrint('Vehicle not found error - handled silently');
          return;
        }
        if (apiName.toLowerCase() == 'connectvehicle') {
          CustomToast.error(
              'Vehicle not found. Please check the vehicle and try again.');
          return;
        }
        if (apiName.toLowerCase() == 'availablevehicles') {
          debugPrint('No nearby vehicles found - handled silently');
          return;
        }
        CustomToast.error('Data not found. Please try again.');
        break;
      case 409:
        if (apiName.toLowerCase() == 'connectvehicle') {
          CustomToast.error('Vehicle is already connected to another user.');
          return;
        }
        CustomToast.error('Resource conflict. Please try again.');
        break;
      case 500:
      case 502:
      case 503:
        showServerError();
        break;
      default:
        showApiError(apiName);
        break;
    }
  }
}

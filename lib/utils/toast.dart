import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CustomToast {
  static whine(String message, {ToastGravity gravity = ToastGravity.BOTTOM}) {
    Fluttertoast.showToast(
      backgroundColor: Colors.green,
      msg: message,
      gravity: gravity,
      timeInSecForIosWeb: 3,
      webPosition: "right",
      webBgColor: "#db1f00",
      webShowClose: true,
    );
  }

  static error(String message) {
    Fluttertoast.showToast(
        backgroundColor: Colors.red,
        msg: message,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 7,
        webBgColor: "#db1f00",
        webPosition: "right",
        webShowClose: true);
  }

  static message(String message,
      {ToastGravity? gravity = ToastGravity.BOTTOM}) {
    Fluttertoast.showToast(
        toastLength: Toast.LENGTH_LONG,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        msg: message,
        gravity: gravity,
        timeInSecForIosWeb: 7,
        webBgColor: "#db1f00",
        webPosition: "right",
        webShowClose: true);
  }
}

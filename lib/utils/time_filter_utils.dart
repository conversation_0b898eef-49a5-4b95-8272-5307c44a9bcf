import 'package:flutter/foundation.dart';

class TimeFilterUtils {
  /// Calculate start and end timestamps based on the selected time filter
  /// Returns a Map with 'startTime' and 'endTime' in epoch milliseconds
  static Map<String, int> calculateTimeRange(String timeFilter) {
    final DateTime now = DateTime.now();
    DateTime startTime;
    DateTime endTime;

    debugPrint('TimeFilterUtils.calculateTimeRange called with: "$timeFilter"');
    debugPrint('timeFilter length: ${timeFilter.length}');
    debugPrint('timeFilter runes: ${timeFilter.runes.toList()}');

    switch (timeFilter) {
      case 'Today':
        // Today: 12:00 AM to current time
        startTime = DateTime(now.year, now.month, now.day);
        endTime = now;
        break;

      case 'Yesterday':
        // Yesterday: 12:00 AM to 11:59 PM of previous day
        final yesterday = now.subtract(const Duration(days: 1));
        startTime = DateTime(yesterday.year, yesterday.month, yesterday.day);
        endTime = DateTime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59, 999);
        break;

      case 'This Week':
        // This Week: Sunday 12:00 AM to current time
        final weekdaysFromSunday = now.weekday % 7; // Sunday = 0, Monday = 1, ..., Saturday = 6
        final thisSunday = now.subtract(Duration(days: weekdaysFromSunday));
        startTime = DateTime(thisSunday.year, thisSunday.month, thisSunday.day);
        endTime = now;
        break;

      case 'Last Week':
        // Last Week: Sunday 12:00 AM to Saturday 11:59 PM of previous week
        final weekdaysFromSunday = now.weekday % 7;
        final thisSunday = now.subtract(Duration(days: weekdaysFromSunday));
        final lastSunday = thisSunday.subtract(const Duration(days: 7));
        final lastSaturday = lastSunday.add(const Duration(days: 6));
        startTime = DateTime(lastSunday.year, lastSunday.month, lastSunday.day);
        endTime = DateTime(lastSaturday.year, lastSaturday.month, lastSaturday.day, 23, 59, 59, 999);
        break;

      case 'This Month':
        // This Month: 1st day 12:00 AM to current time
        startTime = DateTime(now.year, now.month, 1);
        endTime = now;
        break;

      case 'Last Month':
        // Last Month: 1st day 12:00 AM to last day 11:59 PM of previous month
        final lastMonth = DateTime(now.year, now.month - 1, 1);
        final lastDayOfLastMonth = DateTime(now.year, now.month, 0);
        startTime = lastMonth;
        endTime = DateTime(lastDayOfLastMonth.year, lastDayOfLastMonth.month, 
                          lastDayOfLastMonth.day, 23, 59, 59, 999);
        break;

      default:
        // Default to Today
        startTime = DateTime(now.year, now.month, now.day);
        endTime = now;
        break;
    }

    final result = {
      'startTime': startTime.millisecondsSinceEpoch,
      'endTime': endTime.millisecondsSinceEpoch,
    };

    debugPrint('TimeFilterUtils returning: startTime=${result['startTime']}, endTime=${result['endTime']}');
    debugPrint('TimeFilterUtils startDateTime: ${DateTime.fromMillisecondsSinceEpoch(result['startTime']!)}');
    debugPrint('TimeFilterUtils endDateTime: ${DateTime.fromMillisecondsSinceEpoch(result['endTime']!)}');
    
    return result;
  }

  /// Format ride time from hours to display format
  static String formatRideTime(double hours) {
    if (hours == 0) return "0.00";
    return hours.toStringAsFixed(2);
  }

  /// Format ride distance for display
  static String formatRideDistance(double distance) {
    if (distance == 0) return "0.00";
    return distance.toStringAsFixed(2);
  }

  /// Format count values for display
  static String formatCount(int? count) {
    if (count == null) return "0";
    return count.toString();
  }

  /// Format running status for executive role
  static String formatRunningStatus(int? totalRunning, int? totalVehicles) {
    final running = totalRunning ?? 0;
    final vehicles = totalVehicles ?? 0;
    return "$running / $vehicles";
  }
}

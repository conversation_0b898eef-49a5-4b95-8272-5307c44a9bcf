import 'package:flutter/material.dart';
import "dart:math" show pi;

class GradientCircularProgressIndicator extends StatelessWidget {
  final double radius;
  final List<Color> gradientColors;
  final double strokeWidth;
  final bool isBatteryRemoved; // New parameter

  const GradientCircularProgressIndicator({
    super.key,
    required this.radius,
    required this.gradientColors,
    required this.strokeWidth,
    required this.isBatteryRemoved, // Required
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size.fromRadius(radius),
      painter: GradientCircularProgressPainter(
        radius: radius,
        gradientColors: gradientColors,
        strokeWidth: strokeWidth,
        isBatteryRemoved: isBatteryRemoved, // Pass status
      ),
    );
  }
}

class GradientCircularProgressPainter extends CustomPainter {
  GradientCircularProgressPainter({
    required this.radius,
    required this.gradientColors,
    required this.strokeWidth,
    required this.isBatteryRemoved, // New parameter
  });

  final double radius;
  final List<Color> gradientColors;
  final double strokeWidth;
  final bool isBatteryRemoved;

  @override
  void paint(Canvas canvas, Size size) {
    size = Size.fromRadius(radius);

    double offset = strokeWidth / 2;
    Rect rect = Offset(offset, offset) &
    Size(size.width - strokeWidth, size.height - strokeWidth);

    var paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    paint.shader =
        SweepGradient(colors: gradientColors, startAngle: 0.0, endAngle: 2 * pi)
            .createShader(rect);

    double startAngle = isBatteryRemoved ? 0.0 : 0.1; // No gap when removed
    double sweepAngle = isBatteryRemoved ? 2 * pi : (2 - 0.04) * pi; // Full circle when removed

    canvas.drawArc(rect, startAngle, sweepAngle, false, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }
}

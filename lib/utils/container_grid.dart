import 'package:flutter/material.dart';

class GridPainter extends CustomPainter {
  final Color gridLineColor;
  final int gridCount;

  GridPainter({required this.gridLineColor, required this.gridCount});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = gridLineColor
      ..strokeWidth = 1;

    final double stepX = size.width / gridCount;
    final double stepY = size.height / gridCount;

    // Draw vertical grid lines, excluding the first and last line
    for (int i = 1; i < gridCount; i++) {
      final double x = stepX * i;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // Draw horizontal grid lines, excluding the first and last line
    for (int i = 1; i < gridCount; i++) {
      final double y = stepY * i;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

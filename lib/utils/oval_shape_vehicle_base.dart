import 'package:flutter/material.dart';

class OvalShapeBase extends StatelessWidget {
  final double width;
  final double height;
  final List<Color> gradientColors;

  const OvalShapeBase({
    super.key,
    required this.width,
    required this.height,
    required this.gradientColors,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(width, height),
      painter: OvalShapeBasePainter(
        gradientColors: gradientColors,
      ),
    );
  }
}

class OvalShapeBasePainter extends CustomPainter {
  OvalShapeBasePainter({
    required this.gradientColors,
  });

  final List<Color> gradientColors;

  @override
  void paint(Canvas canvas, Size size) {
    Rect rect = Offset.zero & size;
    var path = Path();

    var center = Offset(size.width / 2, size.height / 2);
    var rectangle =
        Rect.fromCenter(center: center, width: size.width, height: size.height);
    var paint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.center,
        end: Alignment.center,
        colors: gradientColors,
      ).createShader(rect)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.inner, 2.0);

    path.addOval(rectangle);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}

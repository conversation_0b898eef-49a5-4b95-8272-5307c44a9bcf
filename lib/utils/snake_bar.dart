import 'package:flutter/material.dart';
import 'package:nds_app/utils/timer_snake_bar_content.dart';

class SnackBarMessage {
  static void message(
      String message, Color backgroundColor, BuildContext context) {
    // Show the SnackBar by displaying the StatefulWidget with the timer
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: backgroundColor,
        duration: const Duration(seconds: 10),
        content: TimerSnackBarContent(message: message),
      ),
    );
  }
}

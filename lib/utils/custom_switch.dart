import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_rider/user_rider_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_rider/user_rider_event.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/models/add_rider_body.dart';
import 'package:nds_app/models/enums/permission_status.dart';
import 'package:nds_app/models/rider.dart';

class CustomSwitch extends StatefulWidget {
  final Rider rider;
  const CustomSwitch({super.key, required this.rider});

  @override
  CustomSwitchState createState() => CustomSwitchState();
}

class CustomSwitchState extends State<CustomSwitch> {
  late Rider rider;
  late bool isOn;

  @override
  void initState() {
    rider = widget.rider;
    if (rider.permissionStatus == RiderPermissionStatus.granted) {
      isOn = true;
    } else {
      isOn = false;
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return GestureDetector(
      onTap: () {
        setState(() {
          VehicleRiderBody vehicleRiderBody = VehicleRiderBody(
              regNo: rider.regNo,
              riderName: rider.ownerName,
              riderPhoneNumber: "+91${rider.riderPhoneNumber}",
              permissionStatus: isOn
                  ? RiderPermissionStatus.revoked
                  : RiderPermissionStatus.granted,
              relationType: rider.relationType,
              isEdit: true);
          if (isOn) {
            isOn = false;
          } else {
            isOn = true;
          }
          context
              .read<UserRiderBloc>()
              .add(AddOrUpdateUserRiderEvent(vehicleRiderBody));
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 88 / 414 * dimensions.width,
        height: 32 / 896 * dimensions.height,
        padding: EdgeInsets.symmetric(
            horizontal: 4 / 414 * dimensions.width,
            vertical: 4 / 896 * dimensions.height),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.0),
          color: isOn ? Colors.green : Colors.red,
        ),
        child: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            AnimatedPositioned(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeIn,
              left: (isOn ? 0.0 : 24) / 414 * dimensions.width,
              right: (isOn ? 24.0 : 0.0) / 414 * dimensions.width,
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: isOn
                    ? Text(commonText["labelYes"]!,
                        key: UniqueKey(),
                        style: poppinsTextStyle(14 / 414 * dimensions.width,
                            colorGrey25, FontWeight.w400))
                    : Text(commonText["labelNo"]!,
                        key: UniqueKey(),
                        style: poppinsTextStyle(14 / 414 * dimensions.width,
                            colorGrey25, FontWeight.w400)),
              ),
            ),
            AnimatedPositioned(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeIn,
              left: (isOn ? 54.0 : 0.0) / 414 * dimensions.width,
              right: (isOn ? 0.0 : 54.0) / 414 * dimensions.width,
              child: Container(
                width: 24.0 / 414 * dimensions.width,
                height: 24.0 / 896 * dimensions.height,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

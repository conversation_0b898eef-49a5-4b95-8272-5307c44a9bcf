import 'package:flutter/material.dart';

class RideModeContainerPainter extends CustomPainter {
  final bool isItCurrentRidingMode;
  final Size size;
  RideModeContainerPainter(this.isItCurrentRidingMode, this.size);
  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;
    Path path = Path();
    path.moveTo(0.0, 0);
    path.lineTo(size.width, 0);
    path.close();
    Offset startingPoint = const Offset(8, 0);
    Offset endingPoint = Offset(size.width, 0);

    canvas.drawLine(startingPoint, endingPoint, paint);
    // canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

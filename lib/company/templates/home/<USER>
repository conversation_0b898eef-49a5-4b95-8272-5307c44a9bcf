import 'package:flutter/material.dart';
import 'package:nds_app/company/factoryFiles/home_view_factory.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';

import '../../baseScreens/home/<USER>';

class HomeStandardScreen extends BaseHome {
  const HomeStandardScreen({
    super.key,
    required super.dashboardAction,
  });

  @override
  State<HomeStandardScreen> createState() => _HomeStandardScreenState();
}

class _HomeStandardScreenState extends State<HomeStandardScreen> {
  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_screen': 'Home Screen',
      'screen_class': widget.runtimeType.toString(),
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // Use the factory pattern to create the appropriate company-specific home view
    return HomeViewFactory.createHomeView(
      dashboardAction: widget.dashboardAction,
    );
  }
}

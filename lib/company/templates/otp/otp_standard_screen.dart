import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/otp/otp_bloc.dart';
import 'package:nds_app/blocs/otp/otp_events.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/enums/otp_status.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/services/login_service.dart';
import 'package:nds_app/widgets/common/progress_indicator.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:nds_app/company/widgets/otp/common_otp_widgets.dart';
import '../../baseScreens/otp/base_otp.dart';

/// Standard OTP verification screen
/// Contains OTP functionality with company-specific navigation logic handled in common widgets
/// Used by all companies with B2C/Lapa vs standard navigation differences handled cleanly
class OtpStandardScreen extends BaseOtp {
  const OtpStandardScreen({
    Key? key,
    required String phoneNumber,
    required String phoneNumText,
    required List<UserActivitySetting> settings,
  }) : super(
          key: key,
          phoneNumber: phoneNumber,
          phoneNumText: phoneNumText,
          settings: settings,
        );

  @override
  State<OtpStandardScreen> createState() => _OtpStandardScreenState();
}

class _OtpStandardScreenState extends State<OtpStandardScreen> {
  final _bloc = OtpScreenBloc();
  late List<TextEditingController> _otpContainerControllers;
  late List<FocusNode> _otpContainerFocusNodes;
  late int maxTextFieldLength;
  bool isResendEnabled = false;
  int countdown = 10;
  Timer? timer;

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': "OTP Verification Standard Screen",
      'screen_class': widget.runtimeType.toString(),
    });

    maxTextFieldLength = 1; // For individual fields
    // But we'll allow pasting a full 6-digit OTP

    super.initState();
    widget.settings.add(UserActivitySetting(
        activityType: ActivityType.login,
        isNewSettingExist: false,
        message: "",
        value: ""));

    _otpContainerControllers = [];
    _otpContainerFocusNodes = [];
    for (int i = 0; i < _bloc.streams.length; i++) {
      _otpContainerControllers.add(TextEditingController());
      _otpContainerFocusNodes.add(FocusNode());
    }
    _otpContainerFocusNodes[0].requestFocus();
    startResendTimer();
  }

  bool isFoundError = false;

  void startResendTimer() {
    setState(() {
      isResendEnabled = false;
      countdown = 10;
    });

    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (countdown > 0) {
          countdown--;
        } else {
          isResendEnabled = true;
          timer.cancel();
        }
      });
    });
  }

  late Dimensions dimensions;

  @override
  Widget build(BuildContext context) {
    dimensions = Dimensions(context);
    bool isB2COrLapa = CommonOtpWidgets.isB2COrLapaCompany();

    return BlocListener<UserVehicleBloc, UserVehicleFetchState>(
      listener: (context, state) {
        // Handle B2C/Lapa user navigation after vehicle data is fetched
        if (isB2COrLapa && state.apiStatus == ApiStatus.success) {
          List<Rider> riders = context.read<UserVehicleBloc>().state.riders;
          CommonOtpWidgets.handleB2CUserNavigation(
            context: context,
            riders: riders,
          );
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: Padding(
          padding: EdgeInsets.fromLTRB(0.057 * dimensions.width,
              0.077 * dimensions.width, 0.057 * dimensions.width, 0.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ...[
                SizedBox(
                  height: 25 / 878 * dimensions.height,
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.arrow_back_ios,
                    color: loginThemeColor,
                  ),
                ),
                SizedBox(
                  height: 0.038 * dimensions.height,
                ),
                Text(
                  otpVerificationScreen["text7"]!,
                  style: urbanistTextStyle(20 / 414 * dimensions.width,
                      loginThemeColor, FontWeight.w700),
                ),
                SizedBox(
                  height: 0.014 * dimensions.height,
                ),
                Text(
                  "${otpVerificationScreen["text8"]!} ${widget.phoneNumText}",
                  style: urbanistTextStyle(14 / 414 * dimensions.width,
                      primaryBlueLight, FontWeight.w500),
                ),
                SizedBox(
                  height: 0.04 * dimensions.height,
                ),
                buildOtpContainers(),
                SizedBox(
                  height: 0.02 * dimensions.height,
                ),
              ],
              ...[
                Flexible(
                  child: Row(
                    children: [
                      Expanded(
                        child: isResendEnabled
                            ? Row(
                                children: [
                                  Flexible(
                                    child: Text(
                                      "${otpVerificationScreen["text3"]!} ",
                                      style: urbanistTextStyle(
                                        14 /
                                            414 *
                                            MediaQuery.of(context).size.width,
                                        primaryBlueLight,
                                        FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: () async {
                                      getCircularProgressIndicator(context);
                                      cleanTextControllers();
                                      await LoginService.sendLoginOtp(
                                          widget.phoneNumber);
                                      if (context.mounted) {
                                        Navigator.pop(context);
                                        startResendTimer();
                                      }
                                    },
                                    child: Text(
                                      otpVerificationScreen["text4"]!,
                                      style: urbanistTextStyle(
                                        14 /
                                            414 *
                                            MediaQuery.of(context).size.width,
                                        secondaryBlue,
                                        FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              )
                            : Text(
                                otpVerificationScreen["text9"]!
                                    .replaceFirst("@value", '$countdown')
                                    .toString(),
                                style: urbanistTextStyle(
                                  12 / 414 * MediaQuery.of(context).size.width,
                                  otpVerificationScreenGrey,
                                  FontWeight.w500,
                                ),
                              ),
                      ),
                    ],
                  ),
                ),
                const Expanded(child: SizedBox()),
                StreamBuilder<String>(
                    stream: _bloc.otp,
                    initialData: "",
                    builder: (streamContext, snapshot) {
                      return snapshot.data!.length == _bloc.streams.length
                          ? GestureDetector(
                              onTap: () async {
                                getCircularProgressIndicator(context);

                                String? errorStatusCode =
                                    await LoginService.verifyLoginOtp(
                                        snapshot.data ?? "",
                                        widget.phoneNumber);

                                if (errorStatusCode == null) {
                                  for (UserActivitySetting e
                                      in widget.settings) {
                                    final request = {
                                      "activityType":
                                          e.activityType.requestName,
                                      "value": e.value,
                                      "organisationId": organisationId
                                    };

                                    await BackendApi.initiatePostCall(
                                      ApiUrls.saveUserKeyActivity,
                                      body: request,
                                    );
                                  }

                                  // Use common widget navigation logic
                                  CommonOtpWidgets.handlePostVerificationNavigation(
                                    context: context,
                                    isB2COrLapa: isB2COrLapa,
                                  );
                                } else {
                                  isFoundError = true;
                                  for (int i = 0;
                                      i < _otpContainerControllers.length;
                                      i++) {
                                    fillContainer(
                                        OtpStatus.invalid.toString(), i);
                                  }
                                }
                              },
                              child: CustomButton.gesture(
                                text: otpVerificationScreen["text5"]!,
                                backgroundColor: loginThemeColor,
                                size: CustomButtonSize.fullWidth,
                                textColor: colorWhite,
                                fontWeight: FontWeight.w600,
                              ))
                          : CustomButton.gesture(
                              text: otpVerificationScreen["text5"]!,
                              backgroundColor: loginThemeColor,
                              size: CustomButtonSize.fullWidth,
                              textColor: colorWhite,
                              fontWeight: FontWeight.w600,
                            );
                    }),
                SizedBox(
                  height: 0.01 * dimensions.height,
                )
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget showErrorWidgetIf({required bool isVisible, required Widget widget}) {
    return StreamBuilder<String>(
      stream: _bloc.streams[_otpContainerFocusNodes.length - 1],
      initialData: "",
      builder: (context, snapshot) {
        return Visibility(
            visible: (isVisible &&
                    snapshot.data != OtpStatus.invalid.toString()) ||
                (snapshot.data == OtpStatus.invalid.toString() && !isVisible),
            child: widget);
      },
    );
  }

  void fillContainer(String number, int index) {
    _bloc.eventSink.add(FillDigitEvent(digit: number, index: index));
    if (number.isEmpty && index > 0) {
      // Only move focus back if we're not at the first field
      _otpContainerFocusNodes[index - 1].requestFocus();
    } else if (index + 1 < _otpContainerFocusNodes.length) {
      // Only move focus forward if we're not at the last field
      _otpContainerFocusNodes[index + 1].requestFocus();
    }
    if (isFoundError && number != OtpStatus.invalid.toString()) {
      for (int i = 0; i < _otpContainerControllers.length; i++) {
        String num = _otpContainerControllers[i].text;
        if (i == index) {
          num = number;
        }
        _bloc.eventSink.add(FillDigitEvent(digit: num, index: i));
      }
      isFoundError = false;
    }
  }

  Widget buildOtpContainers() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(_otpContainerControllers.length, (i) {
        return Expanded(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 4.0),
            child: StreamBuilder<String>(
                stream: _bloc.streams[i],
                initialData: "",
                builder: (context, snapshot) {
                  _otpContainerControllers[i].text = snapshot.data!;

                  return Container(
                    decoration: BoxDecoration(
                        color: otpDigitContainerColor,
                        border: Border.all(
                            color: snapshot.data!.isNotEmpty
                                ? snapshot.data == OtpStatus.invalid.toString()
                                    ? colorRed
                                    : secondaryBlue
                                : unSelectedTextBoxColor,
                            width: 1.4),
                        borderRadius: BorderRadius.circular(8.0)),
                    height: 0.072 * dimensions.height,
                    child: Center(
                      child: TextField(
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp('[0-9]')),
                        ],
                        onTap: () {
                          _otpContainerControllers[i].selection = TextSelection(
                              baseOffset:
                                  _otpContainerControllers[i].text.length,
                              extentOffset:
                                  _otpContainerControllers[i].text.length);
                        },
                        autofillHints:
                            i == 0 ? [AutofillHints.oneTimeCode] : [],
                        onChanged: (value) {
                          // Handle full OTP paste (6 digits)
                          if (value.length == 6 &&
                              RegExp(r'^\d{6}$').hasMatch(value)) {
                            // Fill all containers with the pasted OTP
                            for (int index = 0;
                                index < value.length &&
                                    index < _otpContainerControllers.length;
                                index++) {
                              _otpContainerControllers[index].text =
                                  value[index];
                              fillContainer(value[index], index);
                            }
                            // Focus on the last field
                            if (_otpContainerFocusNodes.length > 5) {
                              _otpContainerFocusNodes[5].requestFocus();
                            }
                          } else {
                            // Handle single digit input
                            fillContainer(value, i);
                          }
                        },
                        maxLength:
                            i == 0 ? 6 : 1, // First field can accept full OTP
                        textAlign: TextAlign.center,
                        style: urbanistTextStyle(0.029 * dimensions.height,
                            colorBlack, FontWeight.w700),
                        keyboardType: const TextInputType.numberWithOptions(
                            signed: true, decimal: true),
                        controller: _otpContainerControllers[i],
                        focusNode: _otpContainerFocusNodes[i],
                        decoration: InputDecoration(
                          counterText: "",
                          labelStyle: urbanistTextStyle(
                              0.029 * dimensions.height,
                              colorBlack,
                              FontWeight.w700),
                          border: InputBorder.none,
                        ),
                      ),
                    ),
                  );
                }),
          ),
        );
      }),
    );
  }

  void cleanTextControllers() {
    for (var element in _otpContainerControllers) {
      element.clear();
    }
    _otpContainerFocusNodes.first.requestFocus();
  }

  String? extractOtp(String? messageBody) {
    messageBody = messageBody ?? "";
    // Regular expression to find a 6-digit number
    final RegExp regExp = RegExp(r'\b\d{6}\b');
    final match = regExp.firstMatch(messageBody);
    return match?.group(0); // Return the matched OTP if found
  }

  @override
  void dispose() {
    cleanTextControllers();
    for (var e in _otpContainerControllers) {
      e.dispose();
    }
    for (var node in _otpContainerFocusNodes) {
      node.dispose();
    }
    timer?.cancel();
    super.dispose();
  }
}

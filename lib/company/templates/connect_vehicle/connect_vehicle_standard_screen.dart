import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:nds_app/blocs/connect/connect_events.dart';
import 'package:nds_app/blocs/connect/connect_stream.dart';
import 'package:nds_app/blocs/connect/toggle/connect_vehicle_toogle_bloc.dart';
import 'package:nds_app/blocs/connect/toggle/connect_vehicle_toogle_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/constant/action.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/company/factoryFiles/dashboard_factory.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:vibration/vibration.dart';
import 'package:synchronized/synchronized.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../../widgets/connect_vehicle/common_connect_vehicle_widgets.dart';
import '../../baseScreens/connect_vehicle/base_connect_vehicle.dart';

/// Standard connect vehicle screen template
/// Contains all the connect vehicle functionality and UI
/// Used by all companies that share the same connect vehicle template
class ConnectVehicleStandardScreen extends BaseConnectVehicle {
  const ConnectVehicleStandardScreen({super.key});

  @override
  State<ConnectVehicleStandardScreen> createState() =>
      _ConnectVehicleStandardScreenState();
}

class _ConnectVehicleStandardScreenState
    extends State<ConnectVehicleStandardScreen> {
  final _streams = ConnectScreenStream();
  late List<TextEditingController> _codeContainerControllers;
  late List<FocusNode> _codeContainerFocusNodes;

  bool isConnectChassisCodeViewEnabled = false;
  String? message;
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  MobileScannerController? controller;
  late Color color;
  late ColorType colorType;
  bool isFlashOn = false;

  @override
  void initState() {
    WakelockPlus.enable();
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Connect Vehicle Standard Screen',
      'screen_class': widget.runtimeType.toString()
    });
    controller = MobileScannerController(
      detectionSpeed: DetectionSpeed.normal,
      formats: [BarcodeFormat.qrCode],
      facing: CameraFacing.back,
      torchEnabled: false,
    );
    super.initState();

    // Add a short delay to ensure UI is fully initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        // Ensure the UI is fully visible
      });
    });

    _codeContainerControllers = [];
    _codeContainerFocusNodes = [];

    for (int i = 0; i < _streams.streams.length; i++) {
      _codeContainerControllers.add(TextEditingController());
      _codeContainerFocusNodes.add(FocusNode());
    }
    // _codeContainerFocusNodes[0].requestFocus();
    _streams.code.listen((event) async {
      if (event.length == _streams.streams.length) {
        FocusManager.instance.primaryFocus?.unfocus();
        // ignore: use_build_context_synchronously
        if (mounted) {
          message = await DialogAction.connect.action(
            context: context,
            code: event,
          );
        }

        _streams.eventSink.add(MessageEvent(message: message ?? ""));
      } else {
        _streams.eventSink.add(MessageEvent(message: ""));
      }
    });
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    colorType = ColorType.values.firstWhere(
      (element) =>
          element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );
    color = hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    final height = dimensions.height;
    final width = dimensions.width;
    ThemeMode themeMode = MyApp.of(context).getCurrentThemeMode();

    return Scaffold(
        resizeToAvoidBottomInset: false,
        body: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: 20 / 414 * width, vertical: 54 / 896 * height),
          child: Column(
            children: [
              Stack(
                children: [
                  BlocBuilder<ConnectVehicleToogleBloc,
                      ConnectVehicleToogleState>(
                    builder: (context, state) {
                      return state.isSwitchRight
                          ? _buildChassisCodeView(height, width, themeMode)
                          : CommonConnectVehicleWidgets.buildQrScannerView(
                              context: context,
                              dimensions: dimensions,
                              qrView: _buildQrView(context),
                              isFlashOn: isFlashOn,
                              onFlashToggle: () async {
                                await controller?.toggleTorch();
                                setState(() {
                                  isFlashOn = !isFlashOn;
                                });
                              },
                              themeMode: themeMode,
                            );
                    },
                  ),
                  Column(
                    children: [
                      CommonConnectVehicleWidgets.buildCloseButton(
                        context: context,
                        width: width,
                        height: height,
                        onClose: () async {
                          FocusManager.instance.primaryFocus?.unfocus();
                          closePageOperation(200);
                        },
                      ),
                      SizedBox(height: 40 / 896 * height),
                      CommonConnectVehicleWidgets.buildToggleSection(
                        color: color,
                        colorType: colorType,
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ));
  }

  Widget _buildChassisCodeView(
      double height, double width, ThemeMode themeMode) {
    return CommonConnectVehicleWidgets.buildChassisCodeView(
      context: context,
      height: height,
      width: width,
      themeMode: themeMode,
      textFieldContainers: getTextFieldContainers(width, height, themeMode),
      messageStream: _streams.message,
      message: message,
    );
  }

  final Lock _lock = Lock();
  bool _isExecuting = false;

  Widget _buildQrView(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    var scanArea = dimensions.width;

    // For this example we check how width or tall the device is and change the scanArea and overlay accordingly.
    // To ensure the Scanner view is properly sizes after rotation
    // we need to listen for Flutter SizeChanged notification and update controller
    return Stack(
      children: [
        MobileScanner(
          key: qrKey,
          controller: controller,
          onDetect: (BarcodeCapture capture) {
            final List<Barcode> barcodes = capture.barcodes;
            if (barcodes.isNotEmpty) {
              Vibration.vibrate(duration: 500, amplitude: 200);
              tryLogin(barcodes.first);
            }
          },
          onDetectError: _onPermissionSet,
        ),
        Center(
          child: Container(
            width: 284 / 414 * scanArea,
            height: 284 / 414 * scanArea,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.secondary,
                width: 2,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _onPermissionSet(Object error, StackTrace stackTrace) {
    log('${DateTime.now().toIso8601String()}_onPermissionSet $error');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          connectVehicleText['error1']!,
        ),
      ),
    );
  }

  getTextFieldContainers(double width, double height, ThemeMode themeMode) {
    return CommonConnectVehicleWidgets.buildTextFieldContainers(
      context: context,
      width: width,
      height: height,
      themeMode: themeMode,
      controllers: _codeContainerControllers,
      focusNodes: _codeContainerFocusNodes,
      streams: _streams.streams,
      onChanged: fillContainer,
    );
  }

  void fillContainer(String number, int index) {
    _streams.eventSink.add(FillDigitEvent(digit: number, index: index));

    if (number.isEmpty && index > 0) {
      _codeContainerFocusNodes[index - 1].requestFocus();
    } else if (index + 1 != _codeContainerFocusNodes.length && number != "") {
      _codeContainerFocusNodes[index + 1].requestFocus();
    }
  }

  Future<String?> tryLogin(Barcode barcode) async {
    return _lock.synchronized(() async {
      if (_isExecuting) {
        return null;
      }

      _isExecuting = true;

      try {
        if (Platform.isAndroid || Platform.isIOS) {
          await controller?.stop();
        }

        // Show loading indicator while connecting
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          },
        );

        // ignore: use_build_context_synchronously
        String? msg = await DialogAction.connect
            // ignore: use_build_context_synchronously
            .action(imei: barcode.rawValue, context: context);

        // Dialog is closed by the connect action

        if (msg == null) {
          closePageOperation(200);
        } else {
          _isExecuting = false;
          _streams.updateMessage(msg);
          // Reset camera to continue scanning
          if (Platform.isAndroid || Platform.isIOS) {
            await controller?.start();
          }
        }
      } catch (e) {
        _isExecuting = false;
        _streams.updateMessage(e.toString());

        // Make sure dialog is closed in case of errors
        // ignore: use_build_context_synchronously
        if (context.mounted && Navigator.canPop(context)) {
          Navigator.pop(context);
        }

        // Reset camera to continue scanning
        if (Platform.isAndroid || Platform.isIOS) {
          await controller?.start();
        }
      }
      return null;
    });
  }

  void closePageOperation(int statusCode) {
    if (statusCode == 200) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => DashboardFactory.createDashboard()),
      );
    } else {
      Navigator.pop(context);
    }
  }
}

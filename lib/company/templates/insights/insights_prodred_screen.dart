import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/widgets/insights/statistics_screen.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';

import '../../baseScreens/insights/base_insights.dart';

/// ProdRed insights screen (Statistics only)
/// Contains insights functionality with ProdRed-specific UI behavior
/// Used by ProdRed company for simplified insights view
class InsightsProdRedScreen extends BaseInsights {
  const InsightsProdRedScreen({
    super.key,
    required super.color,
    required super.colorType,
  });

  @override
  State<InsightsProdRedScreen> createState() =>
      _InsightsProdRedScreenState();
}

class _InsightsProdRedScreenState extends State<InsightsProdRedScreen> {
  late Color color;
  late ColorType colorType;
  @override
  void initState() {
    color = widget.color;
    colorType = widget.colorType;
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Insights ProdRed Screen',
      'screen_class': widget.runtimeType.toString(),
    });
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    colorType = ColorType.values.firstWhere(
          (element) =>
      element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );

    color = hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return StatisticsScreen(color: color, colorType: colorType);
  }
}
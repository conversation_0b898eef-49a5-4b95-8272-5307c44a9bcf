import 'package:flutter/material.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/company/factoryFiles/splash_screen_factory.dart';
import 'package:nds_app/services/firebase_remote_config_serivce.dart';
import 'package:nds_app/services/package_info_setup.dart';
import 'package:open_store/open_store.dart';

import '../../baseScreens/check_app_update/base_check_app_update.dart';

class CheckAppUpdateStandardScreen extends BaseCheckAppUpdate {
  const CheckAppUpdateStandardScreen({super.key});

  @override
  State<CheckAppUpdateStandardScreen> createState() =>
      _CheckAppUpdateStandardScreenState();
}

class _CheckAppUpdateStandardScreenState
    extends State<CheckAppUpdateStandardScreen> {
  final packageInfo = PackageInfoSetup();
  final firebaseRemoteConfigService = FirebaseRemoteConfigService();
  bool _showUpdateDialog = false;

  @override
  void initState() {
    setup();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_showUpdateDialog) {
        _showUpdateVersionDialog(context);
      } else {
        _navigateToNextPage();
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
        listener: (context, state) {
          // Handle connectivity changes if needed
          // Currently no specific action required for template
        },
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor,
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  splashScreenLoadingPageCompanyLogo1,
                  height: 200,
                  width: 200,
                ),
                const SizedBox(height: 20),
                Text(
                  commonText['appTitle']!,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future setup() async {
    final appVersion = _getExtendedVersionNumber(packageInfo.version);
    final requiredMinVersion = _getExtendedVersionNumber(
        firebaseRemoteConfigService.getMinRequiredVersion());
    debugPrint("------requiredMinVersion : $requiredMinVersion");
    if (appVersion < requiredMinVersion) {
      _showUpdateDialog = true;
    }
  }

  int _getExtendedVersionNumber(String version) {
    List versionCells = version.split('.');
    versionCells = versionCells.map((i) => int.parse(i)).toList();
    return versionCells[0] * 100000 + versionCells[1] * 1000 + versionCells[2];
  }

  void _navigateToNextPage() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => SplashScreenFactory.createSplashScreen(),
      ),
    );
  }

  void _showUpdateVersionDialog(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            commonText['updateRequired']!,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            commonText['updateMessage']!,
            style: const TextStyle(
              fontSize: 14,
            ),
          ),
          actions: [
            CustomButton(
              onPressed: () {
                OpenStore.instance.open(
                  appStoreId: iosAppId,
                  androidAppBundleId: androidPackageName,
                );
              },
              text: commonText['update']!,
              width: 100 / 414 * dimensions.width,
              height: 40 / 896 * dimensions.height,
              fontSize: 14,
            ),
          ],
        );
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/company/baseScreens/emergency_sos/base_emergency_sos_screen.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/services/api_service.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:nds_app/utils/toast.dart';
import 'package:nds_app/services/location_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:location/location.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/company/widgets/emergency_sos/common_emergency_sos_widgets.dart';

class EmergencySOSStandardScreen extends BaseEmergencySOSScreen {
  const EmergencySOSStandardScreen({super.key});

  @override
  State<EmergencySOSStandardScreen> createState() =>
      _EmergencySOSStandardScreenState();
}

class _EmergencySOSStandardScreenState
    extends BaseEmergencySOSScreenState<EmergencySOSStandardScreen> {
  String additionalInfo = "";
  String? selectedReason;
  String? errorMessage;
  bool isLoading = true;

  List<String> emergencyReasons = [];

  @override
  void initState() {
    super.initState();
    fetchEmergencyReasons();
  }

  Future<void> fetchEmergencyReasons() async {
    try {
      setState(() => isLoading = true);
      final params = {"vIdVal": vehicleInfoConstant?.regNo ?? ""};
      debugPrint("Fetching emergency reasons with params: $params");

      final response = await BackendApi.initiateGetCall(
        ApiUrls.getEmergencyReasons,
        params: params,
      );

      if (response is http.Response && response.statusCode == 200) {
        final data = json.decode(response.body);
        debugPrint("Parsed response data: $data");

        setState(() {
          if (data is List) {
            emergencyReasons = List<String>.from(data);
          }
          isLoading = false;
          errorMessage = null;
        });
      } else {
        setState(() {
          errorMessage = emergencySosText['emergency_error'];
          isLoading = false;
        });
      }
    } catch (e, stackTrace) {
      debugPrint("Error fetching emergency reasons: $e");
      debugPrint("Stack trace: $stackTrace");
      setState(() {
        errorMessage = emergencySosText['emergency_error'];
        isLoading = false;
      });
    }
  }

  /// Check if internet connection is available
  Future<bool> checkInternetConnection() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  /// Check if location services are enabled
  Future<bool> checkLocationServices() async {
    Location location = Location();
    bool serviceEnabled = await location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await location.requestService();
    }
    return serviceEnabled;
  }

  /// Check if location permissions are granted
  Future<bool> checkLocationPermissions() async {
    Location location = Location();
    PermissionStatus permissionGranted = await location.hasPermission();
    if (permissionGranted == PermissionStatus.denied) {
      permissionGranted = await location.requestPermission();
    }
    return permissionGranted == PermissionStatus.granted;
  }

  /// Show location services disabled alert
  void showLocationServicesAlert() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(emergencySosText['location_services_disabled']!),
          content:
              Text(emergencySosText['location_services_disabled_message']!),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(emergencySosText['ok_button']!),
            ),
          ],
        );
      },
    );
  }

  /// Show location permission denied alert
  void showLocationPermissionAlert() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(emergencySosText['location_permission_denied']!),
          content:
              Text(emergencySosText['location_permission_denied_message']!),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(emergencySosText['ok_button']!),
            ),
          ],
        );
      },
    );
  }

  /// Show no internet connection alert
  void showNoInternetAlert() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(emergencySosText['no_internet_connection']!),
          content: Text(emergencySosText['no_internet_connection_message']!),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(emergencySosText['ok_button']!),
            ),
          ],
        );
      },
    );
  }

  /// Show vehicle not connected alert
  void showVehicleNotConnectedAlert() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(emergencySosText['vehicle_not_connected']!),
          content: Text(emergencySosText['vehicle_not_connected_message']!),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(emergencySosText['ok_button']!),
            ),
          ],
        );
      },
    );
  }

  /// Check if vehicle is currently connected
  bool isVehicleConnected() {
    return currentVehicleStatus == VehicleStatus.connected;
  }

  /// Show loading overlay with gif loader
  void showLoadingOverlay() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async => false, // Prevent back button
          child: Center(
            child: Image.asset(
              isTwoWheels
                  ? loaderGifImages['2Wheels']!
                  : loaderGifImages['3Wheels']!,
            ),
          ),
        );
      },
    );
  }

  /// Hide loading overlay
  void hideLoadingOverlay() {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    // Use common widget for color logic
    Color iconColor = CommonEmergencySosWidgets.getEmergencySosColor();
    ColorType colorType = vehicleInfoConstant?.colorType ?? ColorType.normal;
    Color vehicleColor = currentVehicleStatus == VehicleStatus.connected
        ? colorType == ColorType.light
            ? Theme.of(context).highlightColor
            : iconColor
        : loginThemeColor;

    return WillPopScope(
      onWillPop: () async => !isLoading, // Prevent closing during loading
      child: Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: EdgeInsets.symmetric(
          horizontal: 20 / 414 * dimensions.width,
          vertical: 24 / 896 * dimensions.height,
        ),
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.circular(20),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      Center(
                        child: Text(
                          emergencySosText['emergency_sos_title']!,
                          style: Theme.of(context)
                              .textTheme
                              .headlineLarge
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: 20,
                              ),
                        ),
                      ),
                      Positioned(
                        right: 0,
                        child: GestureDetector(
                          onTap: isLoading ? null : onClose,
                          child: Icon(
                            Icons.close,
                            size: 24 / 414 * dimensions.width,
                            color: isLoading
                                ? Theme.of(context)
                                    .iconTheme
                                    .color
                                    ?.withOpacity(0.5)
                                : Theme.of(context).iconTheme.color,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Content
                Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: 20 / 414 * dimensions.width),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Description
                      Text(
                        emergencySosText['emergency_sos_description']!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontSize: 14,
                            ),
                        textAlign: TextAlign.center,
                      ),

                      SizedBox(height: 24 / 896 * dimensions.height),

                      // Emergency Type Label with red asterisk
                      Row(
                        children: [
                          Text(
                            emergencySosText['emergency_type']!,
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: colorGrey600,
                                    ),
                          ),
                          Text(
                            ' *',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.red,
                                    ),
                          ),
                        ],
                      ),

                      SizedBox(height: 24 / 896 * dimensions.height),
                      // Emergency Reason Dropdown
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16 / 414 * dimensions.width,
                          vertical: 12 / 896 * dimensions.height,
                        ),
                        decoration: BoxDecoration(
                          color: colorGrey100,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: colorGrey300),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            isExpanded: true,
                            hint: Text(
                              emergencySosText['emergency_type_placeholder']!,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    color: colorGrey600,
                                  ),
                            ),
                            value: selectedReason,
                            icon: Icon(
                              Icons.keyboard_arrow_down,
                              color: colorGrey600,
                            ),
                            onChanged: onEmergencyReasonChanged,
                            items: emergencyReasons.map((String reason) {
                              return DropdownMenuItem<String>(
                                value: reason,
                                child: Text(
                                  reason,
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      ),

                      SizedBox(height: 24 / 896 * dimensions.height),

                      // Location Section
                      Text(
                        emergencySosText['additional_info']!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: colorGrey600,
                            ),
                      ),

                      SizedBox(height: 8 / 896 * dimensions.height),

                      // Text Input for Additional Info
                      Container(
                        padding: EdgeInsets.all(16 / 414 * dimensions.width),
                        decoration: BoxDecoration(
                          color: colorGrey100,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: colorGrey300),
                        ),
                        child: TextField(
                          onChanged: (value) {
                            setState(() {
                              additionalInfo = value;
                              // Clear error message when user types
                              if (errorMessage != null) {
                                errorMessage = null;
                              }
                            });
                          },
                          maxLines: 3,
                          textInputAction: TextInputAction.done,
                          onSubmitted: (_) {
                            FocusScope.of(context).unfocus();
                          },
                          decoration: InputDecoration(
                            hintText: emergencySosText[
                                'additional_info_placeholder']!,
                            border: InputBorder.none,
                            hintStyle: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontSize: 16,
                                  color: colorGrey600,
                                ),
                          ),
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: colorGrey700,
                                  ),
                        ),
                      ),

                      SizedBox(height: 24 / 896 * dimensions.height),

                      // Add error message widget using common widget
                      CommonEmergencySosWidgets.buildErrorWidget(
                        isVisible: errorMessage != null,
                        widget: Text(
                          errorMessage ?? '',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.red,
                                  ),
                        ),
                      ),

                      SizedBox(height: 16 / 896 * dimensions.height),

                      // Confirm Button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: (selectedReason != null && !isLoading)
                              ? onConfirmTap
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                (selectedReason != null && !isLoading)
                                    ? vehicleColor
                                    : otpVerificationScreenGrey,
                            padding: EdgeInsets.symmetric(
                              vertical: 16 / 896 * dimensions.height,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: isLoading
                              ? Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                colorWhite),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      emergencySosText['submitting']!,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(
                                            color: colorWhite,
                                            fontWeight: FontWeight.w600,
                                          ),
                                    ),
                                  ],
                                )
                              : Text(
                                  emergencySosText['emergency_submit']!,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: colorWhite,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                        ),
                      ),

                      SizedBox(height: 20 / 896 * dimensions.height),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void onSOSButtonTap() {
    // Implementation for SOS button tap
    debugPrint("SOS button tapped");
  }

  @override
  void onEditLocation() {
    // Implementation for location editing
    debugPrint("Edit location tapped");
    // TODO: Implement location picker/editor
  }

  @override
  void onEmergencyReasonChanged(String? reason) {
    setState(() {
      selectedReason = reason;
      // Clear error message when a reason is selected
      if (reason != null) {
        errorMessage = null;
      }
    });
  }

  @override
  void onConfirmTap() async {
    if (selectedReason == null) {
      return;
    }

    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      // Show loading overlay
      showLoadingOverlay();
      final hasInternet = await checkInternetConnection();
      if (!hasInternet) {
        setState(() {
          isLoading = false;
        });
        hideLoadingOverlay();
        showNoInternetAlert();
        return;
      }
      final prefs = await SharedPreferences.getInstance();
      final imei = prefs.getString(connectedVehicleImeiNo) ?? "";
      final isConnected = isVehicleConnected();
      if (imei.isEmpty || !isConnected) {
        setState(() {
          isLoading = false;
        });
        hideLoadingOverlay();
        showVehicleNotConnectedAlert();
        return;
      }
      final locationServicesEnabled = await checkLocationServices();
      if (!locationServicesEnabled) {
        setState(() {
          isLoading = false;
        });
        hideLoadingOverlay();
        showLocationServicesAlert();
        return;
      }

      final locationPermissionsGranted = await checkLocationPermissions();
      if (!locationPermissionsGranted) {
        setState(() {
          isLoading = false;
        });
        hideLoadingOverlay();
        showLocationPermissionAlert();
        return;
      }
      final locationData = await determineLocation();
      // Validate location data
      if (locationData.latitude == null ||
          locationData.longitude == null ||
          locationData.latitude == 0.0 ||
          locationData.longitude == 0.0) {
        setState(() {
          isLoading = false;
          errorMessage = emergencySosText['unable_to_get_location'];
        });
        hideLoadingOverlay();
        CustomToast.error(emergencySosText['emergency_submit_error']!);
        return;
      }

      // Prepare request body
      final requestBody = {
        "userLatitude": locationData.latitude ?? 0.0,
        "userLongitude": locationData.longitude ?? 0.0,
        "userAltitude": locationData.altitude ?? 0.0,
        "additionalInfo": additionalInfo.isEmpty ? " " : additionalInfo,
        "emergencyType": selectedReason,
        "imei": imei,
      };

      final response = await BackendApi.initiatePostCall(
        ApiUrls.createEmergencyEvent,
        body: requestBody,
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final emergencyType = responseData['emergencyType'] ?? selectedReason;

        setState(() {
          isLoading = false;
        });
        hideLoadingOverlay();

        // Close the popup
        Navigator.of(context).pop();

        // Show success toast using common widget
        final successMessage = CommonEmergencySosWidgets.replaceValuePlaceholder(
            emergencySosText['emergency_submit_success']!, emergencyType);
        CustomToast.whine(successMessage);
      } else if (response.statusCode == 400 || response.statusCode == 500) {
        debugPrint(
            "API Error - Status: ${response.statusCode}, Body: ${response.body}");
        setState(() {
          isLoading = false;
          errorMessage = emergencySosText['submit_failed'];
        });
        hideLoadingOverlay();
        CustomToast.error(emergencySosText['emergency_submit_error']!);
      } else {
        debugPrint(
            "Unexpected API Status - Status: ${response.statusCode}, Body: ${response.body}");
        setState(() {
          isLoading = false;
          errorMessage = emergencySosText['submit_failed'];
        });
        hideLoadingOverlay();
        CustomToast.error(emergencySosText['emergency_submit_error']!);
      }
    } catch (e) {
      setState(() {
        isLoading = false;
        errorMessage = emergencySosText['submit_failed'];
      });
      hideLoadingOverlay();
      CustomToast.error(emergencySosText['emergency_submit_error']!);
    }
  }

  @override
  void onClose() {
    Navigator.of(context).pop();
  }
}
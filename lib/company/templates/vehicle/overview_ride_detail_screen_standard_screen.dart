import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/models/ride_summary.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:nds_app/constant/api_urls.dart';

import '../../../../common/constant.dart';
import '../../../../common/image_urls.dart';
import '../../../../common/strings.dart';
import '../../../../main.dart';

import '../../baseScreens/vehicle/base_overview_ride_detail_screen.dart';

/// Standard overview ride detail screen
/// Contains all the overview ride detail functionality and UI
/// Used by all companies that share the same overview ride detail template
class OverviewRideDetailScreenStandardScreen
    extends BaseOverviewRideDetailScreen {
  const OverviewRideDetailScreenStandardScreen({
    super.key,
    required super.testId,
  });

  @override
  State<OverviewRideDetailScreenStandardScreen> createState() =>
      _OverviewRideDetailScreenStandardScreenState();
}

class _OverviewRideDetailScreenStandardScreenState
    extends State<OverviewRideDetailScreenStandardScreen> {
  late Future<RideSummary?> _rideSummaryFuture;
  late ThemeMode themeMode;

  Future<RideSummary?> _validateRideActivity(String? tripId) async {
    Map<String, String> params = {};
    if (tripId != null) {
      params['testId'] = tripId;
    }
    try {
      http.Response response = await BackendApi.initiateGetCall(
        ApiUrls.validateRideActivity,
        params: params,
      );
      if (response.statusCode == 200) {
        return RideSummary.fromApiJson(json.decode(response.body));
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  String formatTimeDuration(String duration) {
    // Expecting duration in HH:mm:ss format
    final parts = duration.split(":");
    int hours = 0, minutes = 0, seconds = 0;
    if (parts.length == 3) {
      hours = int.tryParse(parts[0]) ?? 0;
      minutes = int.tryParse(parts[1]) ?? 0;
      seconds = int.tryParse(parts[2]) ?? 0;
    }
    if (hours > 0) {
      return "$hours ${insightsText["text11"] ?? "hr"} $minutes ${insightsText["text12"] ?? "min"} $seconds ${insightsText["text46"] ?? "sec"}";
    } else if (minutes > 0) {
      return "$minutes ${insightsText["text12"] ?? "min"} $seconds ${insightsText["text46"] ?? "sec"}";
    } else {
      return "$seconds ${insightsText["text46"] ?? "sec"}";
    }
  }

  @override
  void initState() {
    super.initState();
    _rideSummaryFuture = _validateRideActivity(widget.testId);
    themeMode = MyApp.of(context).getCurrentThemeMode();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return FutureBuilder<RideSummary?>(
      future: _rideSummaryFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: Image.asset(
              isTwoWheels
                  ? loaderGifImages['2Wheels']!
                  : loaderGifImages['3Wheels']!,
            ),
          );
        }

        final rideSummary = snapshot.hasData && snapshot.data != null
            ? snapshot.data!
            : RideSummary(
                totalDistance: 0.0,
                tripDuration: '00:00:00',
                averageSpeed: 0.0,
                topSpeed: 0.0,
                startingVoltage: 0.0,
                endingVoltage: 0.0,
                totalDischarge: 0.0,
                startSoc: 0.0,
                endSoc: 0.0,
                speedAnalysis: [
                  SpeedAnalysis(
                      mode: rideDetailsScreenText["text15"]!,
                      speed: 0.0,
                      distance: 0.0),
                  SpeedAnalysis(
                      mode: rideDetailsScreenText["text16"]!,
                      speed: 0.0,
                      distance: 0.0),
                  SpeedAnalysis(
                      mode: rideDetailsScreenText["text17"]!,
                      speed: 0.0,
                      distance: 0.0),
                ],
              );

        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              children: [
                // Test Trip Section
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 8 / 414 * dimensions.width,
                    vertical: 16 / 896 * dimensions.height,
                  ),
                  margin: EdgeInsets.only(
                    bottom: 12,
                    left: 6 / 414 * dimensions.width,
                    right: 6 / 414 * dimensions.width,
                  ),
                  decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      borderRadius: BorderRadius.circular(8.0),
                      border: Border.all(width: 1, color: colorGrey200),
                      boxShadow: [
                        BoxShadow(
                            color: colorBlack.withOpacity(0.25),
                            offset: const Offset(1, 3),
                            blurRadius: 3,
                            spreadRadius: 1),
                        BoxShadow(
                            color: colorWhite.withOpacity(0.25),
                            offset: const Offset(-1, -3),
                            blurRadius: 3,
                            spreadRadius: 1)
                      ]),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Image.asset(
                            overViewScreenImages['testTrip']!,
                            color: themeMode == ThemeMode.dark
                                ? colorWhite
                                : colorBlack,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            rideDetailsScreenText["text4"]!,
                            style: Theme.of(context)
                                .textTheme
                                .headlineMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8 / 414 * dimensions.width,
                          vertical: 8 / 896 * dimensions.height,
                        ),
                        decoration: BoxDecoration(
                            color: Theme.of(context).scaffoldBackgroundColor,
                            borderRadius: BorderRadius.circular(8.0),
                            border:
                                Border.all(width: 1.5, color: colorGrey300)),
                        child: IntrinsicHeight(
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  children: [
                                    Text(
                                      rideDetailsScreenText["text8"]!,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(color: colorGrey500),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      " ${rideSummary.totalDistance.toStringAsFixed(2)} ${rideDetailsScreenText["text20"]!}",
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge
                                          ?.copyWith(fontSize: 16),
                                    ),
                                  ],
                                ),
                              ),
                              VerticalDivider(
                                  color: colorGrey300, thickness: 1, width: 1),
                              Expanded(
                                child: Column(
                                  children: [
                                    Text(
                                      rideDetailsScreenText["text9"]!,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(color: colorGrey500),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      formatTimeDuration(
                                          rideSummary.tripDuration),
                                      textAlign: TextAlign.center,
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge
                                          ?.copyWith(fontSize: 16),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Speed Section
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 8 / 414 * dimensions.width,
                    vertical: 16 / 896 * dimensions.height,
                  ),
                  margin: EdgeInsets.only(
                    bottom: 12,
                    left: 6 / 414 * dimensions.width,
                    right: 6 / 414 * dimensions.width,
                  ),
                  decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      borderRadius: BorderRadius.circular(8.0),
                      border: Border.all(width: 1, color: colorGrey200),
                      boxShadow: [
                        BoxShadow(
                            color: colorBlack.withOpacity(0.25),
                            offset: const Offset(1, 3),
                            blurRadius: 3,
                            spreadRadius: 1),
                        BoxShadow(
                            color: colorWhite.withOpacity(0.25),
                            offset: const Offset(-1, -3),
                            blurRadius: 3,
                            spreadRadius: 1)
                      ]),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Image.asset(
                            overViewScreenImages['speed']!,
                            color: themeMode == ThemeMode.dark
                                ? colorWhite
                                : colorBlack,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            rideDetailsScreenText["text5"]!,
                            style: Theme.of(context)
                                .textTheme
                                .headlineMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 12 / 414 * dimensions.width,
                          vertical: 8 / 896 * dimensions.height,
                        ),
                        decoration: BoxDecoration(
                            color: Theme.of(context).scaffoldBackgroundColor,
                            borderRadius: BorderRadius.circular(8.0),
                            border:
                                Border.all(width: 1.5, color: colorGrey300)),
                        child: IntrinsicHeight(
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  children: [
                                    Text(
                                      rideDetailsScreenText["text10"]!,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(color: colorGrey500),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      "${rideSummary.averageSpeed.toStringAsFixed(2)} ${rideDetailsScreenText["text21"]!}",
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge
                                          ?.copyWith(fontSize: 16),
                                    ),
                                  ],
                                ),
                              ),
                              VerticalDivider(
                                  color: colorGrey300, thickness: 1, width: 1),
                              Expanded(
                                child: Column(
                                  children: [
                                    Text(
                                      rideDetailsScreenText["text11"]!,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(color: colorGrey500),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      "${rideSummary.topSpeed.toStringAsFixed(2)} ${rideDetailsScreenText["text21"]!}",
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge
                                          ?.copyWith(fontSize: 16),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Charge Section
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 8 / 414 * dimensions.width,
                    vertical: 16 / 896 * dimensions.height,
                  ),
                  margin: EdgeInsets.only(
                    bottom: 12,
                    left: 6 / 414 * dimensions.width,
                    right: 6 / 414 * dimensions.width,
                  ),
                  decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      borderRadius: BorderRadius.circular(8.0),
                      border: Border.all(width: 1, color: colorGrey200),
                      boxShadow: [
                        BoxShadow(
                            color: colorBlack.withOpacity(0.25),
                            offset: const Offset(1, 3),
                            blurRadius: 3,
                            spreadRadius: 1),
                        BoxShadow(
                            color: colorWhite.withOpacity(0.25),
                            offset: const Offset(-1, -3),
                            blurRadius: 3,
                            spreadRadius: 1)
                      ]),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Image.asset(
                            overViewScreenImages['charge']!,
                            color: themeMode == ThemeMode.dark
                                ? colorWhite
                                : colorBlack,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            rideDetailsScreenText["text6"]!,
                            style: Theme.of(context)
                                .textTheme
                                .headlineMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 12 / 414 * dimensions.width,
                          vertical: 8 / 896 * dimensions.height,
                        ),
                        decoration: BoxDecoration(
                            color: Theme.of(context).scaffoldBackgroundColor,
                            borderRadius: BorderRadius.circular(8.0),
                            border:
                                Border.all(width: 1.5, color: colorGrey300)),
                        child: IntrinsicHeight(
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  children: [
                                    Text(
                                      rideDetailsScreenText["text12"]!,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(color: colorGrey500),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      "${rideSummary.startSoc.toStringAsFixed(1)} %",
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge
                                          ?.copyWith(fontSize: 16),
                                    ),
                                  ],
                                ),
                              ),
                              VerticalDivider(
                                  color: colorGrey300, thickness: 1, width: 1),
                              Expanded(
                                child: Column(
                                  children: [
                                    Text(
                                      rideDetailsScreenText["text13"]!,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(color: colorGrey500),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      "${rideSummary.endSoc.toStringAsFixed(1)} %",
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge
                                          ?.copyWith(fontSize: 16),
                                    ),
                                  ],
                                ),
                              ),
                              VerticalDivider(
                                  color: colorGrey300, thickness: 1, width: 1),
                              Expanded(
                                child: Column(
                                  children: [
                                    Text(
                                      rideDetailsScreenText["text14"]!,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(color: colorGrey500),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      "${rideSummary.totalDischarge.toStringAsFixed(1)} %",
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge
                                          ?.copyWith(fontSize: 16),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Speed Analysis Section
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 8 / 414 * dimensions.width,
                    vertical: 16 / 896 * dimensions.height,
                  ),
                  margin: EdgeInsets.only(
                    bottom: 12,
                    left: 6 / 414 * dimensions.width,
                    right: 6 / 414 * dimensions.width,
                  ),
                  decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      borderRadius: BorderRadius.circular(8.0),
                      border: Border.all(width: 1, color: colorGrey200),
                      boxShadow: [
                        BoxShadow(
                            color: colorBlack.withOpacity(0.25),
                            offset: const Offset(1, 3),
                            blurRadius: 3,
                            spreadRadius: 1),
                        BoxShadow(
                            color: colorWhite.withOpacity(0.25),
                            offset: const Offset(-1, -3),
                            blurRadius: 3,
                            spreadRadius: 1)
                      ]),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Image.asset(
                            overViewScreenImages['speedAnalysis']!,
                            color: themeMode == ThemeMode.dark
                                ? colorWhite
                                : colorBlack,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            rideDetailsScreenText["text7"]!,
                            style: Theme.of(context)
                                .textTheme
                                .headlineMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          children: rideSummary.speedAnalysis
                              .map(
                                (analysis) => Padding(
                                  padding: const EdgeInsets.only(bottom: 12.0),
                                  child: _buildSpeedAnalysisRow(
                                    context,
                                    dimensions,
                                    analysis.mode.toUpperCase() == 'ECO'
                                        ? colorEcomode
                                        : analysis.mode.toUpperCase() == 'CITY'
                                            ? colorCitymode
                                            : colorPowermode,
                                    analysis.mode,
                                    analysis.speed.toStringAsFixed(2),
                                    analysis.distance.toStringAsFixed(2),
                                  ),
                                ),
                              )
                              .toList(),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSpeedAnalysisRow(BuildContext context, Dimensions dimensions,
      Color dotColor, String category, String freqSpeed, String distance) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 16 / 414 * dimensions.width,
        vertical: 16 / 896 * dimensions.height,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(width: 1, color: colorGrey300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: dotColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                category,
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontSize: 14),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      rideDetailsScreenText["text18"]!,
                      style: Theme.of(context)
                          .textTheme
                          .bodySmall
                          ?.copyWith(color: colorGrey500),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      "$freqSpeed ${rideDetailsScreenText["text21"]!}",
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontSize: 16),
                    ),
                  ],
                ),
              ),
              Container(
                height: 32,
                width: 1,
                color: colorGrey300,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      rideDetailsScreenText["text8"]!,
                      style: Theme.of(context)
                          .textTheme
                          .bodySmall
                          ?.copyWith(color: colorGrey500),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      "$distance ${rideDetailsScreenText["text20"]!}",
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

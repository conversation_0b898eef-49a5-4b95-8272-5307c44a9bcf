import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/vehicle/performance/leadership_performance_bloc.dart';
import 'package:nds_app/blocs/vehicle/performance/leadership_performance_event.dart';
import 'package:nds_app/blocs/vehicle/toggle/leadership_vehicle_detail_toggle_bloc.dart';
import 'package:nds_app/blocs/vehicle/toggle/leadership_vehicle_detail_toggle_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/models/enums/vehicle_performace_type.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/utils/time_filter_utils.dart';
import 'package:nds_app/widgets/vehicle/toggle_button_leadership_vehicle_detail.dart';
import 'package:nds_app/widgets/vehicle/leadership_range_view.dart';
import 'package:nds_app/widgets/vehicle/leadership_motor_view.dart';
import 'package:nds_app/widgets/vehicle/leadership_battery_view.dart';

import '../../baseScreens/vehicle/base_vehicle.dart';

/// Leadership-specific vehicle detail screen template for ProdRed
/// Contains segmented control for Range, Motor, and Battery views
class ProdredLeadershipVehicleDetailScreen extends BaseVehicle {
  final String selectedItemName;
  final String selectedItemType; // "Model", "Fleet", or "Vehicle"
  final int? entityId; // Optional entity ID passed from parent

  const ProdredLeadershipVehicleDetailScreen({
    super.key,
    required super.color,
    required super.colorType,
    required this.selectedItemName,
    required this.selectedItemType,
    this.entityId,
  });

  @override
  State<ProdredLeadershipVehicleDetailScreen> createState() =>
      _ProdredLeadershipVehicleDetailScreenState();
}

class _ProdredLeadershipVehicleDetailScreenState
    extends State<ProdredLeadershipVehicleDetailScreen> {
  late Color color;
  late ColorType colorType;
  late int entityId;
  late VehicleType vehicleType;

  @override
  void initState() {
    super.initState();
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name':
          "ProdRed Leadership Vehicle Detail - ${widget.selectedItemName}",
      'screen_class': widget.runtimeType.toString(),
    });

    color = widget.color;
    colorType = widget.colorType;
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    colorType = ColorType.values.firstWhere(
      (element) =>
          element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );

    color = hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;

    // Initialize entity ID and vehicle type based on selected item type
    entityId = widget.entityId ?? 1; // Use passed entityId or default to 1
    vehicleType = _getVehicleTypeFromString(widget.selectedItemType);
  }

  VehicleType _getVehicleTypeFromString(String type) {
    switch (type.toLowerCase()) {
      case 'model':
        return VehicleType.model;
      case 'fleet':
        return VehicleType.fleet;
      case 'vehicle':
        return VehicleType.vehicle;
      default:
        return VehicleType.fleet; // Default to fleet
    }
  }

  void _loadMotorData(BuildContext context) {
    final performanceState = context.read<LeadershipPerformanceBloc>().state;
    final timeRange = TimeFilterUtils.calculateTimeRange(performanceState.selectedTimeFilter);
    context.read<LeadershipPerformanceBloc>().add(
          LoadPerformanceDataEvent(
            entityId: entityId,
            startTime: timeRange['startTime']!,
            endTime: timeRange['endTime']!,
            vehicleType: vehicleType,
            summaryType: SummaryType.motor,
          ),
        );
  }

  void _loadBatteryData(BuildContext context) {
    final performanceState = context.read<LeadershipPerformanceBloc>().state;
    final timeRange = TimeFilterUtils.calculateTimeRange(performanceState.selectedTimeFilter);
    context.read<LeadershipPerformanceBloc>().add(
          LoadPerformanceDataEvent(
            entityId: entityId,
            startTime: timeRange['startTime']!,
            endTime: timeRange['endTime']!,
            vehicleType: vehicleType,
            summaryType: SummaryType.battery,
          ),
        );
  }

  void _loadInitialData(LeadershipPerformanceBloc bloc) {
    // Load range data by default when screen opens
    final timeRange = TimeFilterUtils.calculateTimeRange('Today');
    bloc.add(
      LoadPerformanceDataEvent(
        entityId: entityId,
        startTime: timeRange['startTime']!,
        endTime: timeRange['endTime']!,
        vehicleType: vehicleType,
        summaryType: SummaryType.range,
      ),
    );
  }

  void _loadRangeData(BuildContext context) {
    final performanceState = context.read<LeadershipPerformanceBloc>().state;
    final timeRange = TimeFilterUtils.calculateTimeRange(performanceState.selectedTimeFilter);
    context.read<LeadershipPerformanceBloc>().add(
          LoadPerformanceDataEvent(
            entityId: entityId,
            startTime: timeRange['startTime']!,
            endTime: timeRange['endTime']!,
            vehicleType: vehicleType,
            summaryType: SummaryType.range,
          ),
        );
  }

  Widget _buildSelectedView(int selectedIndex) {
    switch (selectedIndex) {
      case 0:
        return LeadershipRangeView(
          entityId: entityId,
          vehicleType: vehicleType,
        );
      case 1:
        return LeadershipMotorView(
          entityId: entityId,
          vehicleType: vehicleType,
        );
      case 2:
        return LeadershipBatteryView(
          entityId: entityId,
          vehicleType: vehicleType,
        );
      default:
        return LeadershipRangeView(
          entityId: entityId,
          vehicleType: vehicleType,
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Theme.of(context).iconTheme.color,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          widget.selectedItemName,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
        ),
        centerTitle: true,
      ),
      body: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => LeadershipVehicleDetailToggleBloc(),
          ),
          BlocProvider(
            create: (context) {
              final bloc = LeadershipPerformanceBloc();
              // Load initial range data
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _loadInitialData(bloc);
              });
              return bloc;
            },
          ),
        ],
        child: Padding(
          padding: EdgeInsets.only(top: 24 / 993 * dimensions.height),
          child: Column(
            children: [
              // Toggle Button
              ToggleButtonLeadershipVehicleDetail(
                color: color,
                colorType: colorType,
              ),
              SizedBox(height: 8 / 896 * dimensions.height),
              // Content based on selected segment
              Expanded(
                child: BlocListener<LeadershipVehicleDetailToggleBloc,
                    LeadershipVehicleDetailToggleState>(
                  listener: (context, state) {
                    // Always load fresh data when switching between views to ensure correct time filter
                    if (state.selectedIndex == 0) {
                      // Range view - always load fresh data
                      _loadRangeData(context);
                    } else if (state.selectedIndex == 1) {
                      // Motor view - always load fresh data
                      _loadMotorData(context);
                    } else if (state.selectedIndex == 2) {
                      // Battery view - always load fresh data
                      _loadBatteryData(context);
                    }
                  },
                  child: BlocBuilder<LeadershipVehicleDetailToggleBloc,
                      LeadershipVehicleDetailToggleState>(
                    builder: (context, state) {
                      return SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        child: Column(
                          children: [
                            _buildSelectedView(state.selectedIndex),
                            const SizedBox(
                                height:
                                    100), // Add bottom padding for scrolling
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

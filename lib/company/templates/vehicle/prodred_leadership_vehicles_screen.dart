import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/vehicle/toggle/leadership_vehicle_toggle_bloc.dart';
import 'package:nds_app/blocs/vehicle/toggle/leadership_vehicle_toggle_state.dart';
import 'package:nds_app/blocs/vehicle/vehicles/vehicles_bloc.dart';
import 'package:nds_app/blocs/vehicle/vehicles/vehicles_event.dart';
import 'package:nds_app/blocs/vehicle/vehicle_models/vehicle_models_bloc.dart';
import 'package:nds_app/blocs/vehicle/vehicle_models/vehicle_models_event.dart';
import 'package:nds_app/blocs/vehicle/fleet/fleet_bloc.dart';
import 'package:nds_app/blocs/vehicle/fleet/fleet_event.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/widgets/vehicle/toggle_button_leadership_vehicle.dart';
import 'package:nds_app/widgets/vehicle/leadership_model_view.dart';
import 'package:nds_app/widgets/vehicle/leadership_fleet_view.dart';
import 'package:nds_app/widgets/vehicle/leadership_vehicle_view.dart';

import '../../baseScreens/vehicle/base_vehicle.dart';

/// Leadership-specific vehicle screen template for ProdRed
/// Contains segmented control for Model, Fleet, and Vehicle views
class ProdredLeadershipVehiclesScreen extends BaseVehicle {
  const ProdredLeadershipVehiclesScreen({
    super.key,
    required super.color,
    required super.colorType,
  });

  @override
  State<ProdredLeadershipVehiclesScreen> createState() =>
      _ProdredLeadershipVehiclesScreenState();
}

class _ProdredLeadershipVehiclesScreenState extends State<ProdredLeadershipVehiclesScreen> {
  late Color color;
  late ColorType colorType;
  ScrollController? _scrollController;
  late VehiclesBloc _vehiclesBloc;
  late VehicleModelsBloc _vehicleModelsBloc;
  late FleetBloc _fleetBloc;
  int _currentSelectedIndex = 0; // Track current tab selection

  @override
  void initState() {
    super.initState();
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': "ProdRed Leadership Vehicles",
      'screen_class': widget.runtimeType.toString(),
    });

    _scrollController = ScrollController();
    _scrollController!.addListener(_onScroll);
    
    _vehiclesBloc = VehiclesBloc();
    _vehiclesBloc.add(const LoadVehiclesPageEvent(page: 0, size: 10));
    
    _vehicleModelsBloc = VehicleModelsBloc();
    _vehicleModelsBloc.add(const LoadVehicleModelsEvent(page: 0, size: 10));
    
    _fleetBloc = FleetBloc();
    _fleetBloc.add(const LoadFleetEvent(page: 0, size: 10));

    color = widget.color;
    colorType = widget.colorType;
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    colorType = ColorType.values.firstWhere(
      (element) =>
          element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );

    color = hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
  }

  @override
  void dispose() {
    if (_scrollController != null) {
      _scrollController!.removeListener(_onScroll);
      _scrollController!.dispose();
    }
    _vehiclesBloc.close();
    _vehicleModelsBloc.close();
    _fleetBloc.close();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController != null &&
        _scrollController!.position.pixels >=
            _scrollController!.position.maxScrollExtent - 100) {
      // Use the stored selected index to trigger pagination accordingly
      switch (_currentSelectedIndex) {
        case 0:
          _handleModelPagination();
          break;
        case 1:
          _handleFleetPagination();
          break;
        case 2:
          _handleVehiclePagination();
          break;
      }
    }
  }

  void _handleModelPagination() {
    final state = _vehicleModelsBloc.state;
    final hasMorePages = state.vehicleModelsResponse != null &&
        (state.currentPage + 1) < state.vehicleModelsResponse!.totalPages;
    if (state.apiStatus == ApiStatus.success &&
        !state.isLoadingMore &&
        hasMorePages) {
      _vehicleModelsBloc.add(LoadVehicleModelsEvent(page: state.currentPage + 1, size: 10));
    }
  }

  void _handleFleetPagination() {
    final state = _fleetBloc.state;
    final hasMorePages = state.fleetsResponse != null &&
        (state.currentPage + 1) < state.fleetsResponse!.totalPages;
    if (state.apiStatus == ApiStatus.success &&
        !state.isLoadingMore &&
        hasMorePages) {
      _fleetBloc.add(LoadFleetEvent(page: state.currentPage + 1, size: 10));
    }
  }

  void _handleVehiclePagination() {
    final state = _vehiclesBloc.state;
    if (state.apiStatus == ApiStatus.success &&
        !state.isLoadingMore &&
        state.totalPages > 0 &&
        (state.currentPage + 1) < state.totalPages) {
      _vehiclesBloc.add(LoadVehiclesPageEvent(page: state.currentPage + 1, size: 10));
    }
  }

  Widget _buildSelectedView(int selectedIndex) {
    switch (selectedIndex) {
      case 0:
        return BlocProvider.value(
          value: _vehicleModelsBloc,
          child: const LeadershipModelView(),
        );
      case 1:
        return BlocProvider.value(
          value: _fleetBloc,
          child: const LeadershipFleetView(),
        );
      case 2:
        return BlocProvider.value(
          value: _vehiclesBloc,
          child: const LeadershipVehicleView(),
        );
      default:
        return BlocProvider.value(
          value: _vehicleModelsBloc,
          child: const LeadershipModelView(),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return BlocProvider(
      create: (context) => LeadershipVehicleToggleBloc(),
      child: Padding(
        padding: EdgeInsets.only(top: 24 / 993 * dimensions.height),
        child: Column(
          children: [
            // Toggle Button
            ToggleButtonLeadershipVehicle(
              color: color,
              colorType: colorType,
            ),
            SizedBox(height: 8 / 896 * dimensions.height),
            // Content based on selected segment
            Expanded(
              child: BlocBuilder<LeadershipVehicleToggleBloc, LeadershipVehicleToggleState>(
                builder: (context, state) {
                  // Update the current selected index when state changes
                  _currentSelectedIndex = state.selectedIndex;
                  
                  return SingleChildScrollView(
                    controller: _scrollController,
                    physics: const BouncingScrollPhysics(),
                    child: Column(
                      children: [
                        _buildSelectedView(state.selectedIndex),
                        SizedBox(height: 100), // Add bottom padding for scrolling
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

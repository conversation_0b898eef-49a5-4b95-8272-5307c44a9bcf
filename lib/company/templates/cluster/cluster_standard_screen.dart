import 'package:flutter/material.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import '../../widgets/cluster/common_cluster_widgets.dart';
import '../../baseScreens/cluster/base_cluster.dart';

/// Standard cluster screen template
/// Contains all the cluster functionality and UI
/// Used by all companies that share the same cluster template
class ClusterStandardScreen extends BaseCluster {
  final VehicleInfo vehicleInfo;
  final String userName;

  const ClusterStandardScreen({
    super.key,
    required this.vehicleInfo,
    required this.userName,
  });

  @override
  State<ClusterStandardScreen> createState() =>
      _ClusterStandardScreenState();
}

class _ClusterStandardScreenState extends State<ClusterStandardScreen> {
  @override
  void initState() {
    super.initState();
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': "Cluster Standard Screen",
      'screen_class': widget.runtimeType.toString(),
    });
  }

  @override
  Widget build(BuildContext context) {
    return CommonClusterWidgets.buildClusterLayout(
      context: context,
      vehicleInfo: widget.vehicleInfo,
      userName: widget.userName,
    );
  }
}

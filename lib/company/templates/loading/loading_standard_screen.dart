import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
//import 'package:location/location.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_event.dart';

import 'package:nds_app/common/constant.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/user_activity_setting.dart';

import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/services/location_service.dart';
import 'package:nds_app/services/login_service.dart';
import 'package:nds_app/utils/check_alert.dart';
import 'package:nds_app/utils/toast.dart';
import 'package:nds_app/widgets/login/bottom_sheet_privacy_policy_and_toc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../widgets/onboarding/preference_helper.dart';
import '../../widgets/loading/common_loading_widgets.dart';
import '../../baseScreens/loading/base_loading.dart';

/// Standard loading screen
/// Contains loading functionality and UI that works for all companies
/// Uses common widget navigation logic to handle B2C/Lapa vs standard company differences
class LoadingStandardScreen extends BaseLoading {
  const LoadingStandardScreen({Key? key}) : super(key: key);

  @override
  LoadingStandardScreenState createState() =>
      LoadingStandardScreenState();
}

class LoadingStandardScreenState extends State<LoadingStandardScreen>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInCirc,
    );
    _controller.forward(
      from: 0,
    );
    _navigatetoLoadingScreen();
  }

  _navigatetoLoadingScreen() {
    Future.delayed(const Duration(milliseconds: 500), () async {
      // ignore: use_build_context_synchronously
      sharedPreferences = await SharedPreferences.getInstance();
      String? token = await LoginService.checkLoginStatus();
      final bool isOnboardingShown = await PreferenceHelper.isOnboardingDone();

      // ignore: use_build_context_synchronously
      final settingResults = await getAwaitedResults(context);

      final http.Response privacyPolicyResponse =
          settingResults[0] as http.Response;
      final termsConditionsResponse = settingResults[1] as http.Response;

      if (privacyPolicyResponse.statusCode == 200 &&
          termsConditionsResponse.statusCode == 200) {
        
        // Parse settings using common widget
        final settings = CommonLoadingWidgets.parseSettings(
            privacyPolicyResponse, termsConditionsResponse);

        if (isInternetAvailable ?? true) {
          if (token != null) {
            http.Response privacyPolicyUpdateResponse =
                await BackendApi.initiateGetCall(
              ApiUrls.getUserKeyActivity,
              params: {
                "activityType":
                    ActivityType.privacyPolicyAcceptance.requestName,
                "organisationId": organisationId
              },
            );
            Map<String, dynamic> privacyPolicyUpdateDetails =
                const JsonDecoder().convert(privacyPolicyUpdateResponse.body);
            UserActivitySetting privacyPolicyUpdate =
                UserActivitySetting.fromJson(privacyPolicyUpdateDetails);

            if (privacyPolicyUpdate.isNewSettingExist == true) {
              // ignore: use_build_context_synchronously
              await getBotttomSheetPrivacyPolicyAndToc(
                  heading: loginScreen['text12']!,
                  // ignore: use_build_context_synchronously
                  context: context,
                  userActivitySetting: privacyPolicyUpdate);
            }

            http.Response termsAndConditionsUpdateResponse =
                await BackendApi.initiateGetCall(
              ApiUrls.getUserKeyActivity,
              params: {
                "activityType":
                    ActivityType.termsConditionsAcceptance.requestName,
                "organisationId": organisationId
              },
            );
            Map<String, dynamic> termsAndConditionsUpdateDetails =
                const JsonDecoder().convert(termsAndConditionsUpdateResponse.body);
            UserActivitySetting termAndConditionUpdate =
                UserActivitySetting.fromJson(termsAndConditionsUpdateDetails);

            if (termAndConditionUpdate.isNewSettingExist == true) {
              // ignore: use_build_context_synchronously
              await getBotttomSheetPrivacyPolicyAndToc(
                  heading: loginScreen['text13']!,
                  // ignore: use_build_context_synchronously
                  context: context,
                  userActivitySetting: termAndConditionUpdate);
            }
          }

          if (isBottomSheetOpenNotifier.value == false) {
            // Check if any rider is connected using common widget
            // ignore: use_build_context_synchronously
            bool isConnected = CommonLoadingWidgets.isAnyRiderConnected(context);

            // Set global settings
            privacyPolicySetting = settings['privacyPolicy']!;
            termsAndConditionsSetting = settings['termsAndConditions']!;

            // ignore: use_build_context_synchronously
            Navigator.pushAndRemoveUntil(context,
                MaterialPageRoute(builder: (context) {
              // Use common widget navigation logic
              return CommonLoadingWidgets.getNavigationWidget(
                token: token,
                isOnboardingShown: isOnboardingShown,
                isConnected: isConnected,
                privacyPolicySetting: privacyPolicySetting!,
                termsAndConditionsSetting: termsAndConditionsSetting!,
                isB2COrLapa: CommonLoadingWidgets.isB2COrLapaCompany(),
              );
            }), (route) => false);
          }
        } else if (privacyPolicyResponse.statusCode != 200) {
          CustomToast.message(toastMessageText['text4']!);
        } else {
          CustomToast.message(toastMessageText['text4']!);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    checkAlert(context);
    currentContext = context;

    return CommonLoadingWidgets.buildConnectivityListener(
      context: context,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CommonLoadingWidgets.buildLoadingLogoSection(context, _animation),
        ],
      ),
    );
  }

  Future<http.Response?> getPrivacyPolicyResponse() async {
    return CommonLoadingWidgets.getPrivacyPolicyResponse();
  }

  Future<http.Response?> getTermsAndConditionsResponse() async {
    return CommonLoadingWidgets.getTermsAndConditionsResponse();
  }

  Future<bool> getLocation() async {
    try {
      userLocationData = userLocationData ?? await determineLocation();
    } catch (e) {
      debugPrint(e.toString());
    }
    return true;
  }

  Future<String> getUserVehicles(BuildContext context) async {
    // Only B2C/Lapa apps should fetch riders/vehicles via B2C endpoint
    if (CommonLoadingWidgets.isB2COrLapaCompany()) {
      context.read<UserVehicleBloc>().add(LoadUserVehicleEvent());
    }
    return await Future.delayed(
        const Duration(milliseconds: 500), () => 'Completed');
  }

  Future<List<Object?>> getAwaitedResults(BuildContext context) async {
    return await Future.wait([
      getPrivacyPolicyResponse(),
      getTermsAndConditionsResponse(),
      getLocation(),
      awaitFunction(),
      getUserVehicles(context),
    ]);
  }

  Future<Object> awaitFunction() async {
    return await Future.delayed(const Duration(milliseconds: 500),
        () => 'Completed'); // Reduced from 4.5s to 0.5s
  }
}
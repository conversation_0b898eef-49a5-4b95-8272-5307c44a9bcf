import 'package:flutter/material.dart';

/// Base class for Emergency SOS screens
/// This class serves as a foundation for company-specific Emergency SOS implementations
abstract class BaseEmergencySOSScreen extends StatefulWidget {
  const BaseEmergencySOSScreen({super.key});
}

/// Abstract state class for Emergency SOS screens
/// Contains common functionality that can be shared across different company implementations
abstract class BaseEmergencySOSScreenState<T extends BaseEmergencySOSScreen> extends State<T> {
  
  /// Method to handle SOS button tap
  /// Should be implemented by concrete template classes
  void onSOSButtonTap();
  
  /// Method to handle location editing
  /// Should be implemented by concrete template classes
  void onEditLocation();
  
  /// Method to handle emergency reason selection
  /// Should be implemented by concrete template classes
  void onEmergencyReasonChanged(String? reason);
  
  /// Method to handle confirm button tap
  /// Should be implemented by concrete template classes
  void onConfirmTap();
  
  /// Method to handle close/cancel
  /// Should be implemented by concrete template classes
  void onClose();
}

import 'package:flutter/material.dart';
import 'package:nds_app/models/vehicle.dart';

/// Base class for vehicle health screens
/// This serves as a contract for all vehicle health template implementations
abstract class BaseVehicleHealth extends StatefulWidget {
  final String imei;
  final void Function() onBackPressed;
  final void Function(String partType, String partLabel) onHealthDetailPressed;
  final Vehicle vehicle;

  const BaseVehicleHealth({
    super.key,
    required this.imei,
    required this.onBackPressed,
    required this.onHealthDetailPressed,
    required this.vehicle,
  });
}

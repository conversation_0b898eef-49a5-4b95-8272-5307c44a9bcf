import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/company/baseScreens/select_fleet/base_select_fleet.dart';
import 'package:nds_app/company/templates/select_fleet/select_fleet_standard_screen.dart';

/// Factory class to create company-specific select fleet screens based on the current company configuration
class SelectFleetFactory {
  /// Creates and returns the appropriate select fleet widget based on the company name
  static Widget createSelectFleet() {
    // All company currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getSelectFleetTemplate();
  }

  /// Returns the appropriate select fleet template based on company validation requirements
  static BaseSelectFleet _getSelectFleetTemplate() {
    // Currently all companies use standard screen
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return const SelectFleetStandardScreen();
    }
  }

  /// Returns the company-specific select fleet class name for debugging purposes
  static String getSelectFleetClassName() {
    return 'SelectFleetStandardScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}

import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/company/baseScreens/vehicle/base_vehicles_list.dart';
import 'package:nds_app/company/templates/vehicle/vehicles_list_standard_screen.dart';

/// Factory class to create company-specific vehicles list screens based on the current company configuration
class VehiclesListFactory {
  /// Creates and returns the appropriate vehicles list widget based on the company name
  static Widget createVehiclesList({
    required void Function(Vehicle vehicle) switchScreen,
  }) {
    // All companies use the same standard screen
    // No company-specific logic needed for vehicles list
    return _getVehiclesListTemplate(switchScreen: switchScreen);
  }

  /// Returns the appropriate vehicles list template based on company validation requirements
  static BaseVehiclesList _getVehiclesListTemplate({
    required void Function(Vehicle vehicle) switchScreen,
  }) {
    // All companies use the same standard screen
    // No company-specific logic needed for vehicles list
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return VehiclesListStandardScreen(switchScreen);
    }
  }

  /// Returns the company-specific vehicles list class name for debugging purposes
  static String getVehiclesListClassName() {
    return 'VehiclesListStandardScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}

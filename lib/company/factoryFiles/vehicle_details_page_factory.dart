import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/company/baseScreens/vehicle/base_vehicle_details_page.dart';
import 'package:nds_app/company/templates/vehicle/vehicle_details_page_standard_screen.dart';

/// Factory class to create company-specific vehicle details page screens based on the current company configuration
class VehicleDetailsPageFactory {
  /// Creates and returns the appropriate vehicle details page widget based on the company name
  static Widget createVehicleDetailsPage({
    required Vehicle vehicle,
    required void Function() onBackPressed,
    required void Function() healthPressed,
  }) {
    // All companies use the same standard screen
    // Company-specific logic is handled by common widgets
    return _getVehicleDetailsPageTemplate(
      vehicle: vehicle,
      onBackPressed: onBackPressed,
      healthPressed: healthPressed,
    );
  }

  /// Returns the appropriate vehicle details page template based on company validation requirements
  static BaseVehicleDetailsPage _getVehicleDetailsPageTemplate({
    required Vehicle vehicle,
    required void Function() onBackPressed,
    required void Function() healthPressed,
  }) {
    // All companies use the same standard screen with common widgets for company logic
    // Company-specific logic is handled by CommonVehicleWidgets
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return VehicleDetailsPageStandardScreen(
            vehicle, onBackPressed, healthPressed);
    }
  }

  /// Returns the company-specific vehicle details page class name for debugging purposes
  static String getVehicleDetailsPageClassName() {
    return 'VehicleDetailsPageStandardScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}

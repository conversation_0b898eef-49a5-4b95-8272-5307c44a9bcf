import 'package:flutter/material.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/company/templates/cluster/cluster_standard_screen.dart';

/// Factory class to create cluster screens using template-based architecture
class ClusterFactory {
  /// Creates and returns the cluster standard screen
  /// All companies use the same cluster standard screen since functionality is identical
  static Widget createCluster({
    required VehicleInfo vehicleInfo,
    required String userName,
  }) {
    // All companies use the same cluster standard screen
    return ClusterStandardScreen(
        vehicleInfo: vehicleInfo, userName: userName);
  }
}

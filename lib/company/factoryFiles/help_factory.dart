import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/company/baseScreens/profile_screens/base_profile_screens.dart';
import 'package:nds_app/company/templates/profile_screens/help_standard_screen.dart';

/// Factory class to create company-specific help screens based on the current company configuration
class HelpFactory {
  /// Creates and returns the appropriate help widget based on the company name
  static Widget createHelp({
    required void Function() onBackPressed,
  }) {
    // All company currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getHelpTemplate(onBackPressed: onBackPressed);
  }

  /// Returns the appropriate help template based on company validation requirements
  static BaseHelpScreen _getHelpTemplate({
    required void Function() onBackPressed,
  }) {
    // Currently all company use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return HelpStandardScreen(onBackPressed: onBackPressed);
    }
  }

  /// Returns the company-specific help class name for debugging purposes
  static String getHelpClassName() {
    return 'HelpStandardScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}

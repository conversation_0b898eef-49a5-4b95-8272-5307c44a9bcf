import 'package:flutter/material.dart';
import 'package:nds_app/company/templates/dashboard/dashboard_standard_screen.dart';

/// Factory class to create dashboard screens using template-based architecture
class DashboardFactory {
  /// Creates and returns the dashboard standard screen
  /// All companies use the same dashboard standard screen since functionality is identical
  /// The branding system automatically handles company-specific logos and assets
  static Widget createDashboard() {
    // All companies use the same dashboard standard screen
    return const DashboardStandardScreen();
  }
}

import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/company/baseScreens/check_app_update/base_check_app_update.dart';
import 'package:nds_app/company/templates/check_app_update/check_app_update_standard_screen.dart';

/// Factory class for creating check app update screens based on company requirements
/// This factory determines which check app update template to use for each company
class CheckAppUpdateScreenFactory {
  /// Creates the appropriate check app update screen based on company validation requirements
  static BaseCheckAppUpdate createCheckAppUpdateScreen() {
    return _getCheckAppUpdateTemplate();
  }

  /// Returns the appropriate check app update template based on company validation requirements
  static BaseCheckAppUpdate _getCheckAppUpdateTemplate() {
    // Currently all companies use standard screen
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return const CheckAppUpdateStandardScreen();
    }
  }

  /// Returns the company-specific check app update class name for debugging purposes
  static String getCheckAppUpdateClassName() {
    return 'CheckAppUpdateStandardScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}

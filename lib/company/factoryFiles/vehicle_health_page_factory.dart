import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/company/baseScreens/vehicle_health/base_vehicle_health.dart';
import 'package:nds_app/company/templates/vehicle_health/vehicle_health_standard_screen.dart';

/// Factory class to create company-specific vehicle health screens based on the current company configuration
class VehicleHealthPageFactory {
  /// Creates and returns the appropriate vehicle health widget based on the company name
  static Widget createVehicleHealthPage({
    required String imei,
    required void Function() onBackPressed,
    required void Function(String partType, String partLabel)
        onHealthDetailPressed,
    required Vehicle vehicle,
  }) {
    // All companies use the same standard screen
    // Company-specific logic is handled by common widgets
    return _getVehicleHealthTemplate(
      imei: imei,
      onBackPressed: onBackPressed,
      onHealthDetailPressed: onHealthDetailPressed,
      vehicle: vehicle,
    );
  }

  /// Returns the appropriate vehicle health template based on company validation requirements
  static BaseVehicleHealth _getVehicleHealthTemplate({
    required String imei,
    required void Function() onBackPressed,
    required void Function(String partType, String partLabel)
        onHealthDetailPressed,
    required Vehicle vehicle,
  }) {
    // All companies use the same standard screen with common widgets for company logic
    // Company-specific logic is handled by CommonVehicleWidgets
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return VehicleHealthStandardScreen(
          imei: imei,
          onBackPressed: onBackPressed,
          onHealthDetailPressed: onHealthDetailPressed,
          vehicle: vehicle,
        );
    }
  }

  /// Returns the company-specific vehicle health class name for debugging purposes
  static String getVehicleHealthClassName() {
    return 'VehicleHealthStandardScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}

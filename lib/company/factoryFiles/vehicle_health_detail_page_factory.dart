import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/company/baseScreens/vehicle_health/base_vehicle_health_detail.dart';
import 'package:nds_app/company/templates/vehicle_health/vehicle_health_detail_standard_screen.dart';

/// Factory class to create company-specific vehicle health detail screens based on the current company configuration
class VehicleHealthDetailPageFactory {
  /// Creates and returns the appropriate vehicle health detail widget based on the company name
  static Widget createVehicleHealthDetailPage({
    required void Function() onBackPressed,
    required String imei,
    required String partType,
    required String partLabel,
    required Vehicle vehicle,
  }) {
    // All companies use the same standard screen
    // Company-specific logic is handled by common widgets
    return _getVehicleHealthDetailTemplate(
      onBackPressed: onBackPressed,
      imei: imei,
      partType: partType,
      partLabel: partLabel,
      vehicle: vehicle,
    );
  }

  /// Returns the appropriate vehicle health detail template based on company validation requirements
  static BaseVehicleHealthDetail _getVehicleHealthDetailTemplate({
    required void Function() onBackPressed,
    required String imei,
    required String partType,
    required String partLabel,
    required Vehicle vehicle,
  }) {
    // All companies use the same standard screen with common widgets for company logic
    // Company-specific logic is handled by CommonVehicleWidgets
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return VehicleHealthDetailStandardScreen(
          onBackPressed: onBackPressed,
          imei: imei,
          partType: partType,
          partLabel: partLabel,
          vehicle: vehicle,
        );
    }
  }

  /// Returns the company-specific vehicle health detail class name for debugging purposes
  static String getVehicleHealthDetailClassName() {
    return 'VehicleHealthDetailStandardScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}

import 'package:flutter/material.dart';
import 'package:nds_app/company/templates/connect_vehicle/connect_vehicle_standard_screen.dart';

/// Factory class to create connect vehicle screens using template-based architecture
class ConnectVehicleFactory {
  /// Creates and returns the connect vehicle standard screen
  /// All companies use the same connect vehicle standard screen since functionality is identical
  static Widget createConnectVehicle() {
    // All companies use the same connect vehicle standard screen
    return const ConnectVehicleStandardScreen();
  }
}

import 'package:flutter/material.dart';

import 'package:nds_app/company/templates/trip_history/trip_history_list_standard_screen.dart';

/// Factory class to create trip history list screens using template-based architecture
class TripHistoryListFactory {
  /// Creates and returns the trip history list template screen
  /// All companies use the same trip history list standard screen with company-specific logic in common widgets
  static Widget createTripHistoryList() {
    // All companies use the same trip history list standard screen
    return const TripHistoryListStandardScreen();
  }

  /// Returns the company-specific trip history list class name for debugging purposes
  static String getTripHistoryListClassName() {
    return 'TripHistoryListStandardScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().isNotEmpty; // All companies supported
  }
}

import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/company/baseScreens/home/<USER>';
import 'package:nds_app/company/templates/home/<USER>';

/// Factory class for creating home screens based on company requirements
/// This factory determines which home template to use for each company
class HomeScreenFactory {
  /// Creates the appropriate home screen based on company validation requirements
  static BaseHome createHomeScreen({
    required Function dashboardAction,
  }) {
    return _getHomeTemplate(dashboardAction: dashboardAction);
  }

  /// Returns the appropriate home template based on company validation requirements
  static BaseHome _getHomeTemplate({
    required Function dashboardAction,
  }) {
    // Currently all companies use standard screen
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return HomeStandardScreen(dashboardAction: dashboardAction);
    }
  }

  /// Returns the company-specific home class name for debugging purposes
  static String getHomeClassName() {
    return 'HomeStandardScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}

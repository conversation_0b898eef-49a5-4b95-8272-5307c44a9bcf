import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/company/baseScreens/emergency_sos/base_emergency_sos_screen.dart';
import 'package:nds_app/company/templates/emergency_sos/emergency_sos_standard_screen.dart';

/// Factory class to create company-specific Emergency SOS screens based on the current company configuration
class EmergencySOSFactory {
  /// Creates and returns the appropriate Emergency SOS widget based on the company name
  static BaseEmergencySOSScreen createEmergencySOSScreen() {
    return _getEmergencySOSTemplate();
  }

  /// Shows Emergency SOS as a modal dialog
  static Future<void> showEmergencySOSDialog(BuildContext context) {
    return showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return createEmergencySOSScreen();
      },
    );
  }

  /// Returns the appropriate Emergency SOS template based on company validation requirements
  static BaseEmergencySOSScreen _getEmergencySOSTemplate() {
    // All companies use the same standard screen with B2C logic handled in common widgets
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return const EmergencySOSStandardScreen();
    }
  }
}

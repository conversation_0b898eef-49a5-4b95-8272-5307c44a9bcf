import 'package:flutter/material.dart';

// Template imports
import 'package:nds_app/company/baseScreens/login/base_login.dart';
import 'package:nds_app/company/templates/login/login_standard_screen.dart';
import 'package:nds_app/company/templates/loading/loading_standard_screen.dart';
import 'package:nds_app/company/templates/splash/splash_standard_screen.dart';
import 'package:nds_app/company/templates/onboarding/onboarding_standard_screen.dart';
import 'package:nds_app/company/templates/otp/otp_standard_screen.dart';
import 'package:nds_app/company/templates/promotional/promotional_standard_screen.dart';

// Branding
import '../../branding/branding.dart';
import 'package:nds_app/models/user_activity_setting.dart';

class SplashScreenFactory {
  static Widget createSplashScreen() {
    return const SplashStandardScreen();
  }

  static Widget createLoadingScreen() {
    // All companies use the same standard screen with B2C/Lapa logic handled in common widgets
    return const LoadingStandardScreen();
  }


  static Widget createOnboardingScreen() {
    return const OnboardingStandardScreen();
  }

  static Widget createPromotionalScreen() {
    return const PromotionalStandardScreen();
  }

  static BaseLogin createLoginScreen(
      {required List<UserActivitySetting> settings}) {
    return LoginStandardScreen(settings: settings);
  }

  static Widget createOtpScreen({
    required String phoneNumber,
    required String phoneNumText,
    required List<UserActivitySetting> settings,
  }) {
    // All companies use the same OTP standard screen
    return OtpStandardScreen(
      phoneNumber: phoneNumber,
      phoneNumText: phoneNumText,
      settings: settings,
    );
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}

import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/company/baseScreens/profile_screens/base_profile_screens.dart';
import 'package:nds_app/company/templates/profile_screens/profile_details_standard_screen.dart';

/// Factory class to create company-specific profile details screens based on the current company configuration
class ProfileDetailsFactory {
  /// Creates and returns the appropriate profile details widget based on the company name
  static Widget createProfileDetails({
    required void Function() onBackPressed,
    required void Function() editProfile,
  }) {
    // All company currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getProfileDetailsTemplate(
      editProfile: editProfile,
      onBackPressed: onBackPressed,
    );
  }

  /// Returns the appropriate profile details template based on company validation requirements
  static BaseProfileDetailsScreen _getProfileDetailsTemplate({
    required void Function() editProfile,
    required void Function() onBackPressed,
  }) {
    // Currently all company use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return ProfileDetailsStandardScreen(
          editProfile: editProfile,
          onBackPressed: onBackPressed,
        );
    }
  }

  /// Returns the company-specific profile details class name for debugging purposes
  static String getProfileDetailsClassName() {
    return 'ProfileDetailsStandardScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}

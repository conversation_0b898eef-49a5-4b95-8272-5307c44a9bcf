import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:math';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/models/trip.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:nds_app/constant/api_urls.dart';

import '../../../common/constant.dart';
import '../../../common/shared_preferences_keys.dart' as shared_preferences_keys;
import '../../../common/strings.dart';
import '../../../models/enums/organisation_type.dart';

class RouteScreen extends StatefulWidget {
  final Trip trip;

  const RouteScreen({super.key, required this.trip});

  @override
  State<RouteScreen> createState() => _RouteScreenState();
}

class _RouteScreenState extends State<RouteScreen>
    with TickerProviderStateMixin {
  GoogleMapController? _mapController;
  AnimationController? _animationController;
  bool _isPlaying = false;
  final Set<Marker> _markers = {};
  final Set<Polyline> _polylines = {};
  LatLng? _startLocation;
  LatLng? _endLocation;
  List<LatLng> _routePoints = [];
  double _playbackSpeed = 1.0;
  final List<double> _speedOptions = [2.0, 1.8, 1.5, 1.2, 1.0, 0.5];
  bool _showSpeedMenu = false;
  double _totalRouteDistance = 0;
  bool _isPerformingInitialZoomOut = false;
  Color _vehicleThemeColor = Colors.blue;

  @override
  void initState() {
    super.initState();
    _loadVehicleThemeColor();
    _fetchRoutePoints();
  }

  Future<void> _loadVehicleThemeColor() async {
    final prefs = await SharedPreferences.getInstance();
    final hexColorInStr =
        prefs.getString(shared_preferences_keys.vehicleThemeColorInHex) ?? '';
    if (hexColorInStr.isNotEmpty) {
      setState(() {
        _vehicleThemeColor = hexColorInStr.toColor();
      });
    } else {}
  }

  Future<BitmapDescriptor> _createVehicleMarkerIcon(
      double size, double bearing) async {
    // Create a larger triangle marker as a custom icon
    const double markerSize = 120.0; // Increased size
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);
    final Paint paint = Paint()
      ..shader = ui.Gradient.linear(
        const Offset(0, 0),
        const Offset(0, markerSize),
        [
          Colors.red, // Default Google blue
          Colors.red.withOpacity(0.7),
        ],
      );

    // Center of the canvas
    double centerX = markerSize / 2;
    double centerY = markerSize / 2;

    // Always point straight up (north), so bearing is not applied
    canvas.save();
    canvas.translate(centerX, centerY);
    // No rotation for straight up: canvas.rotate(0);
    canvas.translate(-centerX, -centerY);

    // Draw a larger triangle pointing up
    final Path path = Path();
    path.moveTo(centerX, centerY - 50); // Top
    path.lineTo(centerX - 35, centerY + 40); // Bottom left
    path.lineTo(centerX + 35, centerY + 40); // Bottom right
    path.close();
    canvas.drawPath(path, paint);
    canvas.restore();

    final ui.Image image = await recorder
        .endRecording()
        .toImage(markerSize.toInt(), markerSize.toInt());
    final ByteData? byteData =
        await image.toByteData(format: ui.ImageByteFormat.png);
    final Uint8List bytes = byteData!.buffer.asUint8List();
    return BitmapDescriptor.fromBytes(bytes);
  }

  Future<void> _fetchRoutePoints() async {
    final prefs = await SharedPreferences.getInstance();
    final imei =
        prefs.getString(shared_preferences_keys.connectedVehicleImeiNo) ?? '';
    final startTime = widget.trip.startTime;
    final endTime = widget.trip.endTime;
    if (imei.isEmpty || startTime == null || endTime == null) {
      return;
    }

    final params = {
      'vIdVal': imei,
      'startTime': startTime.toString(),
      'endTime': endTime.toString(),
      'dataTable': 'LOCATION',
      'samplingFrequency': '1m',
      'showErrors': 'false',
    };

    final body = {
      'field': ['latitude', 'longitude']
    };

    try {
      final response = await initiatePostCallTelemetry(
        ApiUrls.telemetryFieldValues,
        params: params,
        body: body,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final points = <LatLng>[];
        LatLng? lastPoint;

        for (var item in data['data']) {
          if (item['latitude'] != null && item['longitude'] != null) {
            final currentPoint = LatLng(item['latitude'], item['longitude']);

            if (lastPoint == null ||
                (currentPoint.latitude != lastPoint.latitude ||
                    currentPoint.longitude != lastPoint.longitude)) {
              points.add(currentPoint);
              lastPoint = currentPoint;
            }
          }
        }

        if (points.isNotEmpty) {
          setState(() {
            _routePoints = points;
            _startLocation = points.first;
            _endLocation = points.last;
            _totalRouteDistance = _calculateTotalDistance(); // Calculate once
          });
          _setupMapElements();
        } else {
          developer.log('Fetched Route Points: No points found.');
        }
      } else {
        debugPrint('Response body: ${response.body}');
      }
    } catch (e) {
      debugPrint('API call error: $e');
    }
  }

  void _setupMapElements() {
    _markers.clear();
    _polylines.clear();
    if (_startLocation != null && _endLocation != null) {
      _markers.addAll([
        Marker(
          markerId: const MarkerId('start'),
          position: _startLocation!,
          icon:
              BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
        ),
        Marker(
          markerId: const MarkerId('end'),
          position: _endLocation!,
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        ),
      ]);
      _polylines.add(
        Polyline(
          polylineId: const PolylineId('route'),
          points: _routePoints,
          color: Colors.blue, // Default Google Maps blue
          width: 6, // Thinner path for better aesthetics
        ),
      );
      // Initial vehicle position is added in _updateVehicleMarker now.
      setState(() {});
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    // Add a small delay to ensure _startLocation is set before animating.
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_startLocation != null) {
        _mapController?.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: _startLocation!,
              zoom: 19, // Consistent high zoom for initial focus
              tilt: 60,
            ),
          ),
        );
      }
    });
  }

  void _fitBounds() {
    if (_routePoints.isNotEmpty && _mapController != null) {
      final bounds = _createBounds(_routePoints);
      _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(bounds, 100),
      );
    }
  }

  LatLngBounds _createBounds(List<LatLng> points) {
    double minLat = points.first.latitude;
    double maxLat = points.first.latitude;
    double minLng = points.first.longitude;
    double maxLng = points.first.longitude;
    for (final p in points) {
      if (p.latitude < minLat) minLat = p.latitude;
      if (p.latitude > maxLat) maxLat = p.latitude;
      if (p.longitude < minLng) minLng = p.longitude;
      if (p.longitude > maxLng) maxLng = p.longitude;
    }
    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  void _toggleAnimation() {
    if (_routePoints.length < 2) {
      developer
          .log('Animation: Not enough route points (${_routePoints.length}).');
      return;
    }

    if (_isPlaying) {
      _animationController?.stop();
      setState(() {
        _isPlaying = false;
        _isPerformingInitialZoomOut = false;
      });
      return;
    }

    if (_animationController == null) {
      _animationController = AnimationController(
        vsync: this,
      );
      _animationController!.addListener(_animationListener);
      _animationController!.addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          setState(() {
            _isPlaying = false;
            _updateVehicleMarker(_routePoints.last,
                bearing: _calculateBearing(
                    _routePoints[_routePoints.length - 2], _routePoints.last));
          });
        }
      });
    }

    final baseDurationInSeconds = (_totalRouteDistance * 8).toInt();
    final baseDuration = Duration(seconds: max(1, baseDurationInSeconds));
    _animationController!.duration = Duration(
      milliseconds: (baseDuration.inMilliseconds / _playbackSpeed).toInt(),
    );
    developer.log(
        'Animation: New duration set to ${_animationController!.duration!.inSeconds}s at ${_playbackSpeed}x speed. Total route distance: $_totalRouteDistance km');
    _animationController!.value = 0.0;

    setState(() {
      _isPlaying = true;
      _isPerformingInitialZoomOut = true;
    });
    _animationController?.stop();

    _fitBounds();

    Future.delayed(const Duration(milliseconds: 1200), () {
      setState(() {
        _isPerformingInitialZoomOut = false;
      });
      _animationController!.forward(from: _animationController!.value);
    });
  }

  void _animationListener() {
    if (!_isPlaying) return;
    if (_isPerformingInitialZoomOut) {
      return;
    }

    double progress = _animationController!.value;
    int totalPoints = _routePoints.length - 1;
    double pointProgress = progress * totalPoints;

    int currentIndex = pointProgress.floor();
    double fraction = pointProgress - currentIndex;

    if (currentIndex < _routePoints.length - 1) {
      LatLng currentPoint = _routePoints[currentIndex];
      LatLng nextPoint = _routePoints[currentIndex + 1];

      double lat = currentPoint.latitude +
          (nextPoint.latitude - currentPoint.latitude) * fraction;
      double lng = currentPoint.longitude +
          (nextPoint.longitude - currentPoint.longitude) * fraction;

      LatLng newVehiclePosition = LatLng(lat, lng);
      double newBearing = _calculateBearing(currentPoint, nextPoint);

      _updateVehicleMarker(newVehiclePosition, bearing: newBearing);

      // Update camera to follow vehicle with improved zoom and tilt
      _mapController?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: newVehiclePosition,
            zoom: 19,
            tilt: 60,
            bearing: newBearing,
          ),
        ),
      );
    } else if (_routePoints.isNotEmpty) {
      if (_routePoints.length > 1) {
        _updateVehicleMarker(_routePoints.last,
            bearing: _calculateBearing(
                _routePoints[_routePoints.length - 2], _routePoints.last));
      } else {
        _updateVehicleMarker(_routePoints.last, bearing: 0);
      }
    }
  }

  void _changePlaybackSpeed(double speed) {
    if (_animationController == null) {
      return;
    }
    double currentProgress = _animationController!.value;

    final baseDurationInSeconds = (_totalRouteDistance * 2).toInt();
    final baseDuration = Duration(seconds: max(1, baseDurationInSeconds));
    final newDuration = Duration(
      milliseconds: (baseDuration.inMilliseconds / speed).toInt(),
    );

    _animationController!.duration = newDuration;
    if (_isPlaying) {
      _animationController!.forward(from: currentProgress);
    }
    setState(() {
      _playbackSpeed = speed;
      _showSpeedMenu = false;
    });
  }

  void _toggleSpeedMenu() {
    setState(() {
      _showSpeedMenu = !_showSpeedMenu;
    });
  }

  Future<void> _updateVehicleMarker(LatLng position,
      {double bearing = 0}) async {
    final BitmapDescriptor vehicleIcon =
        await _createVehicleMarkerIcon(0, bearing);

    setState(() {
      _markers.removeWhere((m) => m.markerId == const MarkerId('vehicle'));
      _markers.add(
        Marker(
          markerId: const MarkerId('vehicle'),
          position: position,
          icon: vehicleIcon,
          anchor: const Offset(0.5, 0.5),
          // Center the icon
          rotation: bearing,
        ),
      );
    });
  }

  double _calculateBearing(LatLng from, LatLng to) {
    if (from.latitude == to.latitude && from.longitude == to.longitude) {
      return 0.0;
    }

    double lat1 = from.latitude * (pi / 180);
    double lat2 = to.latitude * (pi / 180);
    double dLng = (to.longitude - from.longitude) * (pi / 180);
    double y = sin(dLng) * cos(lat2);
    double x = cos(lat1) * sin(lat2) - sin(lat1) * cos(lat2) * cos(dLng);
    double bearing = atan2(y, x) * (180 / pi);
    return (bearing + 360) % 360;
  }

  double _calculateDistance(LatLng start, LatLng end) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    double lat1 = start.latitude * (pi / 180);
    double lat2 = end.latitude * (pi / 180);
    double dLat = (end.latitude - start.latitude) * (pi / 180);
    double dLng = (end.longitude - start.longitude) * (pi / 180);

    double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1) * cos(lat2) * sin(dLng / 2) * sin(dLng / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return earthRadius * c;
  }

  double _calculateTotalDistance() {
    double totalDistance = 0;
    for (int i = 0; i < _routePoints.length - 1; i++) {
      totalDistance += _calculateDistance(_routePoints[i], _routePoints[i + 1]);
    }
    return totalDistance;
  }

  @override
  void dispose() {
    _animationController?.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GoogleMap(
          onMapCreated: _onMapCreated,
          initialCameraPosition: CameraPosition(
            target: _startLocation ?? const LatLng(12.9716, 77.5946),
            zoom: 19,
            tilt: 60,
          ),
          markers: _markers,
          polylines: _polylines,
          myLocationEnabled: false,
          zoomControlsEnabled: false,
          mapToolbarEnabled: false,
          compassEnabled: false,
        ),
        if (_routePoints.isNotEmpty)
          Positioned(
            bottom: 32,
            left: 0,
            right: 0,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_showSpeedMenu)
                  Material(
                    elevation: 4,
                    color: Colors.transparent,
                    child: Container(
                      width: 120,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            child: Text(
                              rideDetailsScreenText["text19"]!,
                              style: TextStyle(
                                  color: Colors.black87,
                                  fontWeight: FontWeight.bold),
                            ),
                          ),
                          ..._speedOptions.map((speed) => InkWell(
                                onTap: () => _changePlaybackSpeed(speed),
                                child: Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 10),
                                  color: _playbackSpeed == speed
                                      ? Colors.blue.withOpacity(0.1)
                                      : Colors.transparent,
                                  child: Row(
                                    children: [
                                      if (_playbackSpeed == speed)
                                        const Icon(Icons.check,
                                            color: Colors.blue, size: 18),
                                      if (_playbackSpeed == speed)
                                        const SizedBox(width: 8),
                                      Text(
                                        '${speed}x',
                                        style: TextStyle(
                                            color: _playbackSpeed == speed
                                                ? Colors.blue
                                                : Colors.black87,
                                            fontSize: 16),
                                      ),
                                    ],
                                  ),
                                ),
                              )),
                        ],
                      ),
                    ),
                  ),
                if (_showSpeedMenu) const SizedBox(height: 10),
                // Speed menu button
                Center(
                  child: GestureDetector(
                    onTap: _toggleSpeedMenu,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: _vehicleThemeColor,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.speed, color: Colors.white),
                          const SizedBox(width: 8),
                          Text(
                            '${_playbackSpeed}x',
                            style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.white),
                          ),
                          const Icon(Icons.arrow_drop_down,
                              color: Colors.white),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Play/Pause button
                GestureDetector(
                  onTap: _toggleAnimation,
                  child: Container(
                    width: 72,
                    height: 72,
                    decoration: BoxDecoration(
                      color: _vehicleThemeColor,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(
                      _isPlaying ? Icons.pause : Icons.play_arrow,
                      size: 40,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  static Future<http.Response> initiatePostCallTelemetry(
    ApiUrls apiUrl, {
    Map? params,
    Map<String, dynamic>? body,
  }) async {
    body ??= {};

    if (!body.containsKey('field')) {
      body.putIfAbsent(
          "organisationType",
          () => ((isB2CUser || isLapaUser)
              ? OrganisationType.b2c.fullName
              : OrganisationType.b2b.fullName));
      body.putIfAbsent("orgId", () => organisationId.toString());
    }

    Map<String, String> putRequestHeaders = {
      "Content-Type": "application/json",
    };
    final pref = await SharedPreferences.getInstance();
    String token = pref.getString(shared_preferences_keys.authTokenKey) ?? "";
    if (token.isNotEmpty) {
      putRequestHeaders.addAll({"Authorization": "Bearer $token"});
    }

    String paramString = "";
    if (params != null && params.keys.isNotEmpty) {
      for (int i = 0; i < params.keys.length; i++) {
        paramString += params.keys.elementAt(i) +
            "=" +
            params[params.keys.elementAt(i)].toString();
        if (i + 1 < params.keys.length) {
          paramString += "&";
        }
      }
    }
    String url =
        apiUrl.getUrl() + (paramString.isNotEmpty ? "?" : "") + paramString;
    developer.log("===== url : $url \n===== request body : $body");

    http.Response? response;
    JsonEncoder encoder = const JsonEncoder();
    response = await http
        .post(Uri.parse(url),
            headers: putRequestHeaders, body: encoder.convert(body))
        .timeout(const Duration(seconds: 10));

    developer.log(
        "===== url : $url \n===== status : ${response.statusCode} \n===== response : ${response.body}");
    developer.log("Token: $token");
    return response;
  }
}

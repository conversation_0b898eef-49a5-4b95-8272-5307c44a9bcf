import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/strings.dart';

class CommonAboutVehicleWidgets {
  /// Gets the vehicle health button based on company configuration
  /// For ProdRed users, the button is hidden, for others it's visible
  static Widget getVehicleHealthButton(
    BuildContext context,
    Color color,
    void Function() goVehicleHealth,
  ) {
    return Visibility(
      visible: !isProdRedUser,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(32, 8, 32, 8),
        child: SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: () async {
              goVehicleHealth();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(45.0),
              ),
              elevation: 4,
            ),
            child: Text(
              vehicleScreen["vehicleHealth"]!,
              style: const TextStyle(
                color: colorWhite,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Checks if the current user is a ProdRed user
  static bool isProdRedCompany() {
    return isProdRedUser;
  }
}

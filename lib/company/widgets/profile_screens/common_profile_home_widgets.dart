import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/company/factoryFiles/emergency_sos_factory.dart';
import 'package:nds_app/widgets/profile/scooter_access/scooter_access_screen.dart';
import 'package:flutter_svg/svg.dart';

class CommonProfileHomeWidgets {
  /// Gets the scooter access menu item based on company configuration
  /// Only visible for B2C and Lapa users
  static Widget getScooterAccessMenuItem(BuildContext context) {
    return Visibility(
      visible: isB2COrLapaCompany(),
      child: Column(
        children: [
          RowItemB2C(
            image: profileScreenImages["menu7"]!,
            text: profileScreen["menu7"]!,
            onTapHandler: () async {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ScooterAccessScreen(),
                ),
              );
            },
          ),
          const Divider(
            height: 1,
            color: colorGrey800,
          ),
        ],
      ),
    );
  }

  /// Gets the about vehicle menu item based on company configuration
  /// Only visible for B2C and Lapa users
  static Widget getAboutVehicleMenuItem(
    BuildContext context,
    void Function() openAboutVehicle,
  ) {
    return Visibility(
      visible: isB2COrLapaCompany(),
      child: Column(
        children: [
          RowItemB2C(
            image: profileScreenImages["menu6"]!,
            text: profileScreen["menu6"]!,
            onTapHandler: openAboutVehicle,
          ),
          const Divider(
            height: 1,
            color: colorGrey800,
          ),
        ],
      ),
    );
  }

  /// Gets the emergency SOS menu item based on vehicle connection status
  /// Only visible when vehicle is connected
  static Widget getEmergencySOSMenuItem(BuildContext context) {
    return Visibility(
      visible: currentVehicleStatus == VehicleStatus.connected,
      child: Column(
        children: [
          RowItemB2C(
            image: profileScreenImages["menu9"]!,
            text: profileScreen["menu9"]!,
            onTapHandler: () {
              EmergencySOSFactory.showEmergencySOSDialog(context);
            },
          ),
          const Divider(
            height: 1,
            color: colorGrey800,
          ),
        ],
      ),
    );
  }

  /// Checks if the current company is B2C or Lapa
  static bool isB2COrLapaCompany() {
    return isB2CUser || isLapaUser;
  }
}

class RowItemB2C extends StatelessWidget {
  final String image;
  final String text;
  final void Function() onTapHandler;

  const RowItemB2C(
      {super.key,
      required this.image,
      required this.text,
      required this.onTapHandler});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTapHandler,
      child: Container(
        padding: const EdgeInsets.only(
            top: 35, right: 19.01, bottom: 15, left: 15.33),
        decoration: const BoxDecoration(color: Colors.transparent),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                SizedBox(
                  height: 13.33,
                  width: 13.33,
                  child: SvgPicture.asset(
                    image,
                    fit: BoxFit.fill,
                  ),
                ),
                const SizedBox(
                  width: 8,
                ),
                Text(
                  text,
                  style: Theme.of(context).textTheme.labelSmall,
                ),
              ],
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 15,
            )
          ],
        ),
      ),
    );
  }
}

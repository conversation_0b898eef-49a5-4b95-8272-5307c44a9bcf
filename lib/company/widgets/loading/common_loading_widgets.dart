import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_bloc.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/models/setting.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/company/factoryFiles/dashboard_factory.dart';
import 'package:nds_app/company/factoryFiles/splash_screen_factory.dart';

/// Common loading templates that can be reused across all company loading screens
class CommonLoadingWidgets {
  /// Builds the main loading screen logo section with company branding
  static Widget buildLoadingLogoSection(
      BuildContext context, Animation<double> animation) {
    final widthforImg = MediaQuery.of(context).size.width * 0.4;

    return Row(mainAxisAlignment: MainAxisAlignment.center, children: [
      SizedBox(
        width: MediaQuery.of(context).size.width * 0.30,
        child: Image.asset(
          splashScreenLoadingScreenCompanyLogo2,
          fit: BoxFit.contain,
        ),
      ),
      FadeTransition(
          opacity: animation,
          child: SizedBox(
            width: widthforImg,
            child: Image.asset(
              splashScreenLoadingScreenCompanyLogo3,
              fit: BoxFit.contain,
            ),
          ))
    ]);
  }

  /// Builds the connectivity listener for handling internet connection changes
  static Widget buildConnectivityListener({
    required BuildContext context,
    required Widget child,
  }) {
    return BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
      listener: (context, state) {
        if (state is InternetConnectivityFailure) {
          // Show the no internet connection modal here
          WidgetsBinding.instance.addPostFrameCallback((_) {
            getBotttomNoInternetConnection(
              heading: noInternetConnectionText["text4"]!,
              context: context,
            ).then((_) {
              // Once the bottom sheet is dismissed, reset the notifier
              isBottomSheetOpenNotifier.value = false;
            });
          });
        } else if (isBottomSheetOpenNotifier.value == true &&
            state is InternetConnectivitySuccess) {
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            isBottomSheetOpenNotifier.value = false;
            Navigator.of(context).pop();
          });
        }
      },
      child: BlocBuilder<InternetConnectivityBloc, InternetConnectivityState>(
        builder: (context, state) {
          return ColoredBox(
            color: Colors.white,
            child: Scaffold(
              body: child,
            ),
          );
        },
      ),
    );
  }

  /// Helper method to get privacy policy response with error handling
  static Future<http.Response?> getPrivacyPolicyResponse() async {
    try {
      return await BackendApi.initiateGetCall(
        ApiUrls.setting,
        params: {
          "settingName": "PRIVACY_POLICY",
          "organisationId": organisationId
        },
      ).timeout(const Duration(seconds: 10)); // Add 10 second timeout
    } catch (e) {
      debugPrint('Privacy Policy fetch failed: $e');
      return null; // Return null on error to prevent blocking
    }
  }

  /// Helper method to get terms and conditions response with error handling
  static Future<http.Response?> getTermsAndConditionsResponse() async {
    try {
      return await BackendApi.initiateGetCall(
        ApiUrls.setting,
        params: {
          "settingName": "TERMS_AND_CONDITION",
          "organisationId": organisationId
        },
      ).timeout(const Duration(seconds: 10)); // Add 10 second timeout
    } catch (e) {
      debugPrint('Terms and Conditions fetch failed: $e');
      return null; // Return null on error to prevent blocking
    }
  }

  /// Helper method to check if any rider is connected
  static bool isAnyRiderConnected(BuildContext context) {
    List<Rider> riders = context.read<UserVehicleBloc>().state.riders;
    return riders.any((element) => element.isConnected);
  }

  /// Helper method to check if the current company is B2C or Lapa
  static bool isB2COrLapaCompany() {
    return isB2CUser || isLapaUser;
  }

  /// Helper method to parse settings from responses
  static Map<String, UserActivitySetting> parseSettings(
      http.Response privacyPolicyResponse,
      http.Response termsConditionsResponse) {
    JsonDecoder decoder = const JsonDecoder();

    // Parse privacy policy
    Map<String, dynamic> privacyPolicyDetails =
        decoder.convert(privacyPolicyResponse.body);
    Setting privacyPolicy = Setting.fromJson(privacyPolicyDetails);

    // Parse terms and conditions
    Map<String, dynamic> termsConditionsResponseDetails =
        decoder.convert(termsConditionsResponse.body);
    Setting termsAndConditions =
        Setting.fromJson(termsConditionsResponseDetails);

    // Create settings
    UserActivitySetting privacyPolicySetting = UserActivitySetting(
        activityType: ActivityType.privacyPolicyAcceptance,
        isNewSettingExist: false,
        message: privacyPolicy.message,
        value: privacyPolicy.value);

    UserActivitySetting termsAndConditionsSetting = UserActivitySetting(
        activityType: ActivityType.termsConditionsAcceptance,
        isNewSettingExist: false,
        message: termsAndConditions.message,
        value: termsAndConditions.value);

    return {
      'privacyPolicy': privacyPolicySetting,
      'termsAndConditions': termsAndConditionsSetting
    };
  }

  /// Helper method to determine the appropriate navigation based on company type and connection status
  /// B2C/Lapa companies show promotional screens when not connected, others go to dashboard
  static Widget getNavigationWidget({
    required String? token,
    required bool isOnboardingShown,
    required bool isConnected,
    required UserActivitySetting privacyPolicySetting,
    required UserActivitySetting termsAndConditionsSetting,
    bool isB2COrLapa = false,
  }) {
    if (token == null) {
      return isOnboardingShown
          ? SplashScreenFactory.createLoginScreen(
              settings: [privacyPolicySetting, termsAndConditionsSetting],
            )
          : SplashScreenFactory.createOnboardingScreen();
    }

    if (isConnected) {
      return DashboardFactory.createDashboard();
    }

    // B2C/Lapa specific: show promotional screen when not connected
    // Standard companies: go directly to dashboard
    return isB2COrLapa
        ? SplashScreenFactory.createPromotionalScreen()
        : DashboardFactory.createDashboard();
  }
}

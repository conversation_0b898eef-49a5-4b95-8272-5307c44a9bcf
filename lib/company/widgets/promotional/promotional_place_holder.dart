import 'dart:convert';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:nds_app/blocs/promo/promo_stream.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:http/http.dart' as http;
import '../../../common/image_urls.dart';
import 'package:cached_network_image/cached_network_image.dart';

class PromotionalPlaceHolder extends StatefulWidget {
  const PromotionalPlaceHolder({super.key});

  @override
  State<PromotionalPlaceHolder> createState() => _PromotionalPlaceHolderState();
}

class _PromotionalPlaceHolderState extends State<PromotionalPlaceHolder>
    with SingleTickerProviderStateMixin {
  int activeIndex = 0;
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  List<String> images = [];
  final bloc = PromoStream();

  @override
  void initState() {
    images = [];
    bloc.fetchProfileDetails();
    super.initState();

    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
    _controller.forward();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return StreamBuilder(
        stream: bloc.profileStream,
        builder: (context, snapshot) {
          Widget widget = SizedBox(
            height: 530 / 896 * dimensions.height,
            width: dimensions.width,
            child: Center(
              child: Image.asset(
                isTwoWheels ?
                loaderGifImages['2Wheels']! : loaderGifImages['3Wheels']!,
              ),
            ),
          );
          if (snapshot.data != null) {
            images = snapshot.data!;
            widget = images.isNotEmpty
                ? Material(
                    color: Colors.transparent,
                    child: SizedBox(
                      width: dimensions.width,
                      child: Stack(
                        alignment: Alignment.topCenter,
                        children: [
                          SizedBox(
                            width: dimensions.width,
                            height: 530 / 896 * dimensions.height,
                            child: FadeTransition(
                              opacity: _fadeAnimation,
                              child: CarouselSlider(
                                items: images.map((image) {
                                  return CachedNetworkImage(
                                    imageUrl: image,
                                    fit: BoxFit.cover,
                                    fadeInDuration: const Duration(milliseconds: 200),
                                    memCacheHeight: 800,
                                    maxWidthDiskCache: 1080,
                                    maxHeightDiskCache: 1920,
                                    useOldImageOnUrlChange: true,
                                    placeholder: (context, url) => Center(
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2.0,
                                        valueColor: AlwaysStoppedAnimation<Color>(colorWhiteLite),
                                      ),
                                    ),
                                    errorWidget: (context, error, stackTrace) {
                                      debugPrint("Error loading image $image: $error");
                                      return Image.asset(
                                        isTwoWheels ?
                                        loaderGifImages['2Wheels']! : loaderGifImages['3Wheels']!,
                                      );
                                    },
                                  );
                                }).toList(),
                                options: CarouselOptions(
                                  autoPlay: true,
                                  height: dimensions.height,
                                  viewportFraction: 1,
                                  onPageChanged: (index, reason) =>
                                      setState(() => activeIndex = index),
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            top: 500 / 896 * dimensions.height,
                            left: 170 / 414 * dimensions.width,
                            child: FadeTransition(
                              opacity: _fadeAnimation,
                              child: AnimatedSmoothIndicator(
                                activeIndex: activeIndex,
                                count: images.length,
                                effect: ExpandingDotsEffect(
                                    dotHeight: 4 / 360 * dimensions.height,
                                    dotWidth: 8 / 360 * dimensions.width,
                                    expansionFactor: 2,
                                    spacing: 4 / 360 * dimensions.width,
                                    dotColor: colorWhiteLite,
                                    activeDotColor: colorWhiteLite),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  )
                : SizedBox(
                    width: dimensions.width,
                    height: 530 / 896 * dimensions.height,
                    child: Center(
                      child: Icon(Icons.image_search_outlined,
                          size: 200 / 414 * dimensions.width),
                    ),
                  );
          }
          return widget;
        });
  }

  @override
  void dispose() {
    images.clear();  // Clear image references
    _controller.dispose(); // Dispose the controller
    super.dispose();
  }

  loadData() async {
    try {
      http.Response response = await BackendApi.initiateGetCall(
          ApiUrls.getPromotionalImage,
          params: {
            "appType": (isB2CUser || isLapaUser) ? "B2C_APP" : "B2B_APP",
            "promoType": "DEFAULT",
            "org_id": organisationId,
            "organisationType": (isB2CUser || isLapaUser) ? "B2CCUSTOMER" : "B2BCUSTOMER",
            "orgId": organisationId, // Adding both formats for compatibility
          });

      if (response.statusCode == 200) {
        const jsonDecoder = JsonDecoder();
        final List<dynamic> responseBody = jsonDecoder.convert(response.body);

        List<String> imageList = [];

        for (var entry in responseBody) {
          if (entry is Map<String, dynamic> && entry.containsKey('url')) {
            String? url = entry['url'];
            if (url != null && url.isNotEmpty) {
              images.add(url);
            } else {
              debugPrint("Invalid or empty URL in promotional image: $entry");
            }
          } else {
            debugPrint(
                "Unexpected entry in promotional image response: $entry");
          }
        }

        return imageList;
      } else {
        debugPrint(
            "Error fetching promotional images: ${response.statusCode} - ${response.body}");
        return [];
      }
    } catch (e) {
      debugPrint("Exception occurred while fetching promotional images: $e");
      return [];
    }
  }
}

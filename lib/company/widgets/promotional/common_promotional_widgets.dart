import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/action.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/company/widgets/promotional/promotional_place_holder.dart';
import 'package:nds_app/utils/on_will_pop_popup.dart';
import 'package:nds_app/utils/snake_bar.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import 'package:nds_app/widgets/dashboard/action_button_row.dart';
import 'package:url_launcher/url_launcher.dart';

/// Common promotional templates that can be reused across all company promotional screens
class CommonPromotionalWidgets {
  /// Builds the app bar with action button row
  static PreferredSizeWidget buildAppBar({
    required Dimensions dimensions,
    required BuildContext context,
    required VoidCallback onConnectAction,
  }) {
    return PreferredSize(
      preferredSize:
          Size.fromHeight(100 * dimensions.height), // Reduced from 120 to 100
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          border: const Border(
            bottom: BorderSide(color: colorGrey400, width: 1.0),
          ),
        ),
        width: dimensions.width,
        height: 100 / 896 * dimensions.height, // Reduced from 120 to 100
        child: ActionButtonRow(
          isConnected: currentVehicleStatus == VehicleStatus.connected,
          connectAction: onConnectAction,
          context: context,
          userName: userName,
          isDashboardActionRow: true,
        ),
      ),
    );
  }

  /// Builds the main content section with promotional placeholder and buttons
  static Widget buildMainContent({
    required Dimensions dimensions,
    required Animation<double> animation,
    required VoidCallback onGoToWebsite,
    required VoidCallback onLogout,
  }) {
    return Column(
      children: [
        const Expanded(child: PromotionalPlaceHolder()),
        SizedBox(height: 32 / 896 * dimensions.height),
        Column(
          children: [
            // Go to Website Button
            SizedBox(
              height: 48 / 896 * dimensions.height,
              child: CustomButton.elevated(
                width: 300 / 414 * dimensions.width,
                backgroundColor: colorError600,
                onPressed: onGoToWebsite,
                text: promotionalScreenText['go_to_website']!,
                foregroundColor: colorWhite,
                fontSize: 20 / 414 * dimensions.width,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 20 / 896 * dimensions.height),

            // Logout Button
            SizedBox(
              height: 48 / 896 * dimensions.height,
              child: CustomButton.elevated(
                width: 300 / 414 * dimensions.width,
                backgroundColor: colorGrey400,
                onPressed: onLogout,
                text: profileScreen['menu5']!,
                foregroundColor: colorWhite,
                fontSize: 20 / 414 * dimensions.width,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 32 / 896 * dimensions.height),

            // Logo Section
            buildLogoSection(dimensions: dimensions, animation: animation),
          ],
        ),
        SizedBox(height: 32 / 896 * dimensions.height),
      ],
    );
  }

  /// Builds the animated logo section at the bottom
  static Widget buildLogoSection({
    required Dimensions dimensions,
    required Animation<double> animation,
  }) {
    return GestureDetector(
      onTap: () async {
        Uri url = Uri.parse(website);
        launchUrl(url);
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedContainer(
            alignment: Alignment.bottomCenter,
            padding: EdgeInsets.only(bottom: 20 / 896 * dimensions.height),
            duration: const Duration(seconds: 1),
            curve: Curves.easeInOut,
            width: (0.20) * dimensions.width,
            child: Image.asset(
              splashScreenLoadingScreenCompanyLogo2,
              fit: BoxFit.contain,
            ),
          ),
          FadeTransition(
            opacity: animation,
            child: AnimatedContainer(
              alignment: Alignment.bottomCenter,
              duration: const Duration(seconds: 1),
              curve: Curves.easeInOut,
              padding: EdgeInsets.only(bottom: 28 / 896 * dimensions.height),
              width: (0.25) * dimensions.width,
              child: Image.asset(
                splashScreenLoadingScreenCompanyLogo3,
                fit: BoxFit.contain,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the connectivity listener for handling internet connection changes
  static Widget buildConnectivityListener({
    required BuildContext context,
    required Widget child,
  }) {
    return BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
      listener: (context, state) {
        if (state is InternetConnectivityFailure) {
          // Show the no internet connection modal here
          WidgetsBinding.instance.addPostFrameCallback((_) {
            getBotttomNoInternetConnection(
              heading: noInternetConnectionText["text4"]!,
              context: context,
            ).then((_) {
              // Once the bottom sheet is dismissed, reset the notifier
              isBottomSheetOpenNotifier.value = false;
            });
          });
        } else if (isBottomSheetOpenNotifier.value == true &&
            state is InternetConnectivitySuccess) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.of(context).pop();
            isBottomSheetOpenNotifier.value = false;
            SnackBarMessage.message(noInternetConnectionText["text5"]!,
                backOnlineColorGreen, context);
          });
        }
      },
      child: child,
    );
  }

  /// Helper method to handle website navigation
  static void navigateToWebsite() {
    Uri url = Uri.parse(website);
    launchUrl(url);
  }

  /// Helper method to handle logout action
  static void handleLogout(BuildContext context) {
    onWillPopUpMsg(context, DialogAction.logout);
  }
}

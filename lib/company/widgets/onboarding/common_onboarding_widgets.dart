import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/models/setting.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:http/http.dart' as http;

/// Common onboarding templates that can be reused across all company onboarding screens
class CommonOnboardingWidgets {
  /// Returns the standard onboarding data for all company
  static List<Map<String, String>> getOnboardingData() {
    return [
      {
        'title': onBoardingScreenText['title1']!,
        'description': onBoardingScreenText['description1']!,
        'image': onboardingScreenImages['battery']!,
      },
      {
        'title': onBoardingScreenText['title2']!,
        'description': onBoardingScreenText['description2']!,
        'image': onboardingScreenImages['performance']!,
      }
    ];
  }

  /// Builds the title and description section for the current onboarding page
  static Widget buildTitleAndDescription({
    required Dimensions dimensions,
    required Map<String, String> pageData,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(14.0),
          child: Text(
            pageData['title']!,
            style: poppinsTextStyle(
                24 / 414 * dimensions.width, loginThemeColor, FontWeight.w900),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(20.0),
          child: Text(
            pageData['description']!,
            style: poppinsTextStyle(
                20 / 414 * dimensions.width, colorBlack, FontWeight.w400),
          ),
        ),
      ],
    );
  }

  /// Builds the image section for the current onboarding page
  static Widget buildImageSection({
    required Dimensions dimensions,
    required String imagePath,
  }) {
    return SizedBox(
      height: 420 / 896 * dimensions.height,
      width: 380 / 414 * dimensions.width,
      child: Image.asset(
        imagePath,
        width: double.infinity,
      ),
    );
  }

  /// Builds the navigation buttons (Skip/Next/Finish)
  static Widget buildNavigationButtons({
    required VoidCallback onSkip,
    required VoidCallback onNext,
    required bool isLastPage,
  }) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Skip Button without Background and Grey Text
          CustomButton.text(
            onPressed: onSkip,
            text: "Skip",
            foregroundColor: Colors.grey,
          ),

          // Next Button with White Background and Black Text
          CustomButton.elevated(
            onPressed: onNext,
            text: isLastPage ? "Finish" : "Next",
            backgroundColor: Colors.white,
            foregroundColor: const Color(0xFF3E4A5B),
            elevation: 8,
            borderRadius: 30,
            fontSize: 15,
            fontWeight: FontWeight.bold,
          )
        ],
      ),
    );
  }

  /// Helper method to get privacy policy response
  static Future<http.Response> getPrivacyPolicyResponse() async {
    return await BackendApi.initiateGetCall(
      ApiUrls.setting,
      params: {
        "settingType": "privacy_policy",
        "organisationId": organisationId
      },
    );
  }

  /// Helper method to get terms and conditions response
  static Future<http.Response> getTermsAndConditionsResponse() async {
    return await BackendApi.initiateGetCall(
      ApiUrls.setting,
      params: {
        "settingType": "terms_and_conditions",
        "organisationId": organisationId
      },
    );
  }

  /// Helper method to parse settings from responses
  static Map<String, UserActivitySetting> parseSettings(
      http.Response privacyPolicyResponse,
      http.Response termsConditionsResponse) {
    JsonDecoder decoder = const JsonDecoder();

    // Parse privacy policy
    Map<String, dynamic> privacyPolicyDetails =
        decoder.convert(privacyPolicyResponse.body);
    Setting privacyPolicy = Setting.fromJson(privacyPolicyDetails);

    // Parse terms and conditions
    Map<String, dynamic> termsConditionsResponseDetails =
        decoder.convert(termsConditionsResponse.body);
    Setting termsAndConditions =
        Setting.fromJson(termsConditionsResponseDetails);

    // Create settings
    UserActivitySetting privacyPolicySetting = UserActivitySetting(
        activityType: ActivityType.privacyPolicyAcceptance,
        isNewSettingExist: false,
        message: privacyPolicy.message,
        value: privacyPolicy.value);

    UserActivitySetting termsAndConditionsSetting = UserActivitySetting(
        activityType: ActivityType.termsConditionsAcceptance,
        isNewSettingExist: false,
        message: termsAndConditions.message,
        value: termsAndConditions.value);

    return {
      'privacyPolicy': privacyPolicySetting,
      'termsAndConditions': termsAndConditionsSetting
    };
  }
}

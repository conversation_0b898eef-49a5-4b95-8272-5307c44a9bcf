import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nds_app/blocs/otp/otp_bloc.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/models/enums/otp_status.dart';
import 'package:nds_app/services/login_service.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_event.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/company/factoryFiles/dashboard_factory.dart';
import 'package:nds_app/company/factoryFiles/splash_screen_factory.dart';

/// Common OTP templates that can be reused across all company OTP verification screens
class CommonOtpWidgets {
  /// Handles post-verification navigation based on company type
  /// B2C/Lapa users: Fetch vehicles and navigate to dashboard/promotional based on connection
  /// Standard users: Navigate directly to dashboard
  static void handlePostVerificationNavigation({
    required BuildContext context,
    bool isB2COrLapa = false,
  }) {
    if (isB2COrLapa) {
      // Trigger UserVehicleBloc to fetch user vehicle data for B2C/Lapa users
      context.read<UserVehicleBloc>().add(LoadUserVehicleEvent());
      // Navigation will be handled by the BlocListener in the calling widget
    } else {
      // Standard companies: go directly to dashboard
      Navigator.pop(context);
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => DashboardFactory.createDashboard()),
        (r) => false,
      );
    }
  }

  /// Handles B2C/Lapa user navigation after vehicle data is fetched
  static void handleB2CUserNavigation({
    required BuildContext context,
    required List<Rider> riders,
  }) {
    bool isConnected = riders.any((element) => element.isConnected);
    
    Navigator.pop(context);
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(
        builder: (context) => isConnected
            ? DashboardFactory.createDashboard()
            : SplashScreenFactory.createPromotionalScreen(),
      ),
      (r) => false,
    );
  }

  /// Helper method to check if the current company is B2C or Lapa
  static bool isB2COrLapaCompany() {
    return isB2CUser || isLapaUser;
  }
  /// Builds the header section with back button and title
  static Widget buildHeaderSection({
    required Dimensions dimensions,
    required String phoneNumText,
    required VoidCallback onBackPressed,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 25 / 878 * dimensions.height),
        GestureDetector(
          onTap: onBackPressed,
          child: Icon(
            Icons.arrow_back_ios,
            color: loginThemeColor,
          ),
        ),
        SizedBox(height: 0.038 * dimensions.height),
        Text(
          otpVerificationScreen["text7"]!,
          style: urbanistTextStyle(
              20 / 414 * dimensions.width, loginThemeColor, FontWeight.w700),
        ),
        SizedBox(height: 0.014 * dimensions.height),
        Text(
          "${otpVerificationScreen["text8"]!} $phoneNumText",
          style: urbanistTextStyle(
              14 / 414 * dimensions.width, primaryBlueLight, FontWeight.w500),
        ),
        SizedBox(height: 0.04 * dimensions.height),
      ],
    );
  }

  /// Builds the OTP input containers
  static Widget buildOtpContainers({
    required Dimensions dimensions,
    required OtpScreenBloc bloc,
    required List<TextEditingController> controllers,
    required List<FocusNode> focusNodes,
    required Function(String, int) onChanged,
  }) {
    List<Widget> widgets = [];

    for (int i = 0; i < controllers.length; i++) {
      widgets.add(
        SizedBox(
          width: 0.12 * dimensions.width,
          child: StreamBuilder<String>(
              stream: bloc.streams[i],
              initialData: "",
              builder: (context, snapshot) {
                controllers[i].text = snapshot.data!;

                return Container(
                  decoration: BoxDecoration(
                      color: otpDigitContainerColor,
                      border: Border.all(
                          color: snapshot.data!.isNotEmpty
                              ? snapshot.data == OtpStatus.invalid.toString()
                                  ? colorRed
                                  : secondaryBlue
                              : unSelectedTextBoxColor,
                          width: 1.4),
                      borderRadius: BorderRadius.circular(8.0)),
                  height: 0.072 * dimensions.height,
                  child: Center(
                    child: TextField(
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp('[0-9]')),
                      ],
                      onTap: () {
                        controllers[i].selection = TextSelection(
                            baseOffset: controllers[i].text.length,
                            extentOffset: controllers[i].text.length);
                      },
                      onChanged: (value) => onChanged(value, i),
                      maxLength: 1,
                      textAlign: TextAlign.center,
                      style: urbanistTextStyle(0.029 * dimensions.height,
                          colorBlack, FontWeight.w700),
                      keyboardType: TextInputType.number,
                      controller: controllers[i],
                      focusNode: focusNodes[i],
                      decoration: InputDecoration(
                        counterText: "",
                        labelStyle: urbanistTextStyle(0.029 * dimensions.height,
                            colorBlack, FontWeight.w700),
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                );
              }),
        ),
      );

      if (i + 1 < controllers.length) {
        widgets.add(SizedBox(width: 15 / 414 * dimensions.width));
      }
    }

    return Row(children: widgets);
  }

  /// Builds the resend OTP section
  static Widget buildResendSection({
    required bool isResendEnabled,
    required int countdown,
    required VoidCallback onResend,
  }) {
    return Row(
      children: [
        isResendEnabled
            ? Row(
                children: [
                  Text(
                    "${otpVerificationScreen["text3"]!} ",
                    style: urbanistTextStyle(
                      14 / 414 * 414,
                      primaryBlueLight,
                      FontWeight.w500,
                    ),
                  ),
                  GestureDetector(
                    onTap: onResend,
                    child: Text(
                      otpVerificationScreen["text4"]!,
                      style: urbanistTextStyle(
                        14 / 414 * 414,
                        secondaryBlue,
                        FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              )
            : Text(
                otpVerificationScreen["text9"]!
                    .replaceFirst("@value", '$countdown')
                    .toString(),
                style: urbanistTextStyle(
                  12 / 414 * 414,
                  otpVerificationScreenGrey,
                  FontWeight.w500,
                ),
              ),
      ],
    );
  }

  /// Builds the verify button
  static Widget buildVerifyButton({
    required OtpScreenBloc bloc,
    required bool isEnabled,
    required VoidCallback onVerify,
  }) {
    return StreamBuilder<String>(
      stream: bloc.otp,
      initialData: "",
      builder: (streamContext, snapshot) {
        return snapshot.data!.length == bloc.streams.length
            ? GestureDetector(
                onTap: onVerify,
                child: CustomButton.gesture(
                  text: otpVerificationScreen["text5"]!,
                  backgroundColor: loginThemeColor,
                  size: CustomButtonSize.fullWidth,
                  textColor: colorWhite,
                  fontWeight: FontWeight.w600,
                ))
            : CustomButton.gesture(
                text: otpVerificationScreen["text5"]!,
                backgroundColor: loginThemeColor,
                size: CustomButtonSize.fullWidth,
                textColor: colorWhite,
                fontWeight: FontWeight.w600,
              );
      },
    );
  }

  /// Helper method to start resend timer
  static Timer startResendTimer({
    required Function(bool, int) onTimerUpdate,
  }) {
    onTimerUpdate(false, 10);

    return Timer.periodic(const Duration(seconds: 1), (timer) {
      int countdown = 10 - timer.tick;
      if (countdown > 0) {
        onTimerUpdate(false, countdown);
      } else {
        onTimerUpdate(true, 0);
        timer.cancel();
      }
    });
  }

  /// Helper method to handle resend OTP
  static Future<void> handleResendOtp({
    required BuildContext context,
    required String phoneNumber,
    required List<TextEditingController> controllers,
    required Function() onStartTimer,
  }) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    // Clear text controllers
    for (var controller in controllers) {
      controller.clear();
    }

    await LoginService.sendLoginOtp(phoneNumber);

    if (context.mounted) {
      Navigator.pop(context);
      onStartTimer();
    }
  }
}

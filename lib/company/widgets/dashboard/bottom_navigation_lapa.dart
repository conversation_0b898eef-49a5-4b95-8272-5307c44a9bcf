import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_event.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/company/factoryFiles/insights_factory.dart';
import 'package:nds_app/company/factoryFiles/profile_factory.dart';
import 'package:nds_app/company/factoryFiles/vehicle_factory.dart';
import '../../../blocs/navigation/navigation_bar_stream.dart';
import '../../../common/image_urls.dart';
import '../../factoryFiles/home_screen_factory.dart';

/// Bottom Navigation for Lapa Company
class BottomNavigationLapa {
  static final NavigationBarStream _bloc = NavigationBarStream();

  static Widget getBody(
      Function action, BuildContext context, Color color, ColorType colorType) {
    Dimensions dimensions = Dimensions(context);

    List<Widget> widgets = [
      HomeScreenFactory.createHomeScreen(
        dashboardAction: action,
      ),
      Padding(
        padding: EdgeInsets.only(
            left: 20 / 414 * dimensions.width,
            right: 20 / 414 * dimensions.width),
        child: VehicleFactory.createVehicle(color: color, colorType: colorType),
      ),
      InsightsFactory.createInsights(color, colorType),
      Padding(
        padding: EdgeInsets.only(
            left: 0 * dimensions.width, right: 0 * dimensions.width),
        child: ProfileFactory.createProfile(),
      ),
    ];

    return StreamBuilder<int>(
        stream: _bloc.navigationBar,
        builder: (blocContext, snapshot) {
          OverlayEntry? entry =
              context.read<EditRiderDropDownBloc>().state.overlayEntry;
          if (entry != null) {
            entry.remove();
            entry = null;
            context
                .read<EditRiderDropDownBloc>()
                .add(const EditRiderDropDownEvent(overlayEntry: null));
          }
          return InkWell(
              splashFactory: NoSplash.splashFactory,
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              onTap: () {
                OverlayEntry? entry =
                    context.read<EditRiderDropDownBloc>().state.overlayEntry;
                if (entry != null) {
                  entry.remove();
                  entry = null;
                  context
                      .read<EditRiderDropDownBloc>()
                      .add(const EditRiderDropDownEvent(overlayEntry: null));
                }
              },
              child: widgets[snapshot.data ?? bottomNavigationIndex]);
        });
  }

  static Widget getCustomBottomNavigationBar(
      BuildContext context, Function action, Color color, ColorType colorType) {
    Dimensions dimensions = Dimensions(context);

    ThemeMode themeMode = MyApp.of(context).getCurrentThemeMode();
    _bloc.submitIndex(bottomNavigationIndex);

    return Stack(
      alignment: AlignmentDirectional.bottomCenter,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 9 / 896 * dimensions.height),
          child: Container(
            width: dimensions.width * 390 / 414,
            height: dimensions.height * 88 / 896,
            padding: EdgeInsets.all(dimensions.width * 8 / 414),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              border: Border.all(color: colorGrey300, width: 1.5),
              borderRadius: BorderRadius.all(
                Radius.circular(44 / 414 * dimensions.width),
              ),
            ),
            child: Container(
              width: dimensions.width * 360 / 414,
              height: dimensions.height * 58 / 896,
              decoration: BoxDecoration(
                  color: Theme.of(context).primaryColorLight,
                  borderRadius: BorderRadius.all(
                      Radius.circular(32 / 414 * dimensions.width)),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).indicatorColor,
                      offset: const Offset(1.0, 1.0),
                      blurRadius: 1.0,
                      spreadRadius: 1.0,
                    ),
                  ]),
              child: FittedBox(
                child: Row(
                  children: _getItems(
                      action,
                      dimensions,
                      color == colorGrey800 ? colorWhite : color,
                      themeMode,
                      colorType,
                      context),
                ),
              ),
            ),
          ),
        ),
        // Lapa users don't have a central scan button
      ],
    );
  }

  static List<Widget> _getItems(
      Function action,
      Dimensions dimensions,
      Color iconColor,
      ThemeMode themeMode,
      ColorType colorType,
      BuildContext context) {
    List<String> unselectedImageUrls = [
      bottomNavigationImages['home']!,
      getVehicleIcon(),
      bottomNavigationImages['insights']!,
      bottomNavigationImages['profile']!,
    ];

    List<Widget> items = [];
    for (int i = 0; i < unselectedImageUrls.length; i++) {
      if (i == 0) {
        // Lapa users always get spacing at the beginning
        items.add(SizedBox(
          width: 20 / 414 * dimensions.width,
        ));
      }
      items.add(
        InkWell(
            onTap: () {
              bottomNavigationIndex = i;
              _bloc.submitIndex(bottomNavigationIndex);
            },
            child: StreamBuilder(
              stream: _bloc.navigationBar,
              builder: (context, snapshot) {
                int? index = bottomNavigationIndex;
                Color vehicleColor =
                    currentVehicleStatus == VehicleStatus.connected
                        ? colorType == ColorType.light
                            ? Theme.of(context).highlightColor
                            : iconColor
                        : loginThemeColor;

                if (snapshot.data != null) {
                  index = snapshot.data;
                }
                return i == index
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Stack(
                          alignment: AlignmentDirectional.center,
                          children: [
                            Container(
                              height: 48 / 414 * dimensions.width,
                              width: 48 / 414 * dimensions.width,
                              decoration: BoxDecoration(
                                color: vehicleColor,
                                borderRadius: BorderRadius.all(
                                  Radius.circular(44 / 414 * dimensions.width),
                                ),
                              ),
                            ),
                            SvgPicture.asset(
                              unselectedImageUrls[i],
                              colorFilter: ColorFilter.mode(
                                  (vehicleColor.computeLuminance() < 0.5
                                      ? Colors.white
                                      : Colors.black),
                                  BlendMode.srcIn),
                            ),
                          ],
                        ),
                      )
                    : Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 15 / 414 * dimensions.width),
                        child: SvgPicture.asset(unselectedImageUrls[i],
                            color: colorType == ColorType.light
                                ? colorWhite
                                : Theme.of(context).primaryColorDark),
                      );
              },
            )),
      );
      // Lapa users always get spacing between items
      items.add(SizedBox(
        width: 30 / 414 * dimensions.width,
      ));

      if (i == ((unselectedImageUrls.length / 2) - 1)) {
        // Lapa users don't need center spacing (no scan button)
        items.add(SizedBox(
          width: 0 / 414 * dimensions.width,
        ));
      }
    }
    return items;
  }
}

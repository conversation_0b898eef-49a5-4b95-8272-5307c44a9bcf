import 'dart:async';

import 'package:nds_app/blocs/otp/otp_events.dart';

class OtpScreenBloc {
  final _eventController = StreamController<OtpEvent>();
  Sink<OtpEvent> get eventSink => _eventController.sink;
  bool isCreated = false;
  late List<String> _digits;
  late List<StreamController<String>> _streamControllers;

  late List<StreamSink<String>> _streamSinks;
  late List<Stream<String>> streams;

  OtpScreenBloc() {
    _eventController.stream.listen(_mapEventToState);
    if (!isCreated) {
      _digits = [];
      _streamControllers = [];
      _streamSinks = [];
      streams = [];
      for (int i = 0; i < 6; i++) {
        _digits.add("");
        final controller = StreamController<String>.broadcast();
        _streamControllers.add(controller);
        _streamSinks.add(controller.sink);
        streams.add(controller.stream);
      }
      isCreated = true;
    }
  }

  void _mapEventToState(OtpEvent otpEvent) {
    if (otpEvent is SubmitOtpEvent) {
    } else if (otpEvent is FillDigitEvent) {
      submitDigit(otpEvent.digit, otpEvent.index);
    }
  }

  void submitDigit(String digit, int index) {
    _digits[index] = digit;
    _streamSinks[index].add(_digits[index]);
    if (index + 1 == _digits.length) {
      submitOtp();
    }
  }

  /*Submit Otp*/

  late String _otp;
  final _otpStreamController = StreamController<String>.broadcast();
  StreamSink<String> get _inOtp => _otpStreamController.sink;
  Stream<String> get otp => _otpStreamController.stream;

  void submitOtp() {
    _otp = "";
    for (int i = 0; i < _digits.length; i++) {
      _otp += _digits[i].trim();
    }
    _inOtp.add(_otp);
  }

  close() {
    _otpStreamController.close();
    for (var element in _streamControllers) {
      element.close();
    }
    _eventController.close();
  }
}

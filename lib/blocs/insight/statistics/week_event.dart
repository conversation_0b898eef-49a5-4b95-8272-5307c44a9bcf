import 'package:nds_app/blocs/insight/statistics/statistics_event.dart';
import 'package:nds_app/models/enums/date_time_type.dart';

class WeekEvent extends StatisticsEvent {
  final DateTime startTime;
  final DateTime endTime;
  final DateTimeType? dateTimeType;
  final bool isLeftThresold;
  final bool isRightThresold;
  const WeekEvent({
    this.dateTimeType,
    required this.startTime,
    required this.endTime,
    required this.isLeftThresold,
    required this.isRightThresold,
  });

  @override
  List<Object> get props => [
        startTime,
        endTime,
        dateTimeType ?? DateTimeType.week,
        isLeftThresold,
        isRightThresold
      ];
}

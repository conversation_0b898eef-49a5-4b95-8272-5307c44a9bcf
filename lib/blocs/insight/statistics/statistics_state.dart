import 'package:equatable/equatable.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/enums/date_time_type.dart';
import 'package:nds_app/models/enums/statistics_data_type.dart';
import 'package:nds_app/models/statistics.dart';
import 'package:nds_app/models/statistics_details.dart';

class StatisticsState extends Equatable {
  final ApiStatus apiStatus;
  final Statistics statistics;
  final StatisticsDetails statisticsDetails;
  final StatisticsDataType statisticsDataType;
  final String message;
  final DateTime startTime;
  final DateTime endTime;
  final DateTimeType dateTimeType;
  final bool isLeftThresold;
  final bool isRightThresold;
  static const Map<String, dynamic> initialStatistics = {
    "distanceTravelled": 0,
    "rideDuration": 0,
    "avgSpeed": 0,
    "topSpeed": 0,
    "chargingTime": 0,
    "numberOfSwaps": 0,
    "avgDriveModesRange": {}
  };

  static const Map<String, dynamic> initialStatisticsDetails = {
    "value": null,
    "dataPoints": {},
    "modeRangeDataPoints": {},
    "avgModeRange": {}
  };

  StatisticsState(
      {DateTime? startTime,
      DateTime? endTime,
      this.apiStatus = ApiStatus.loading,
      Statistics? statistics,
      StatisticsDetails? statisticsDetails,
      this.message = '',
      this.statisticsDataType = StatisticsDataType.statistics,
      this.dateTimeType = DateTimeType.day,
      this.isLeftThresold = false,
      this.isRightThresold = true})
      : startTime = startTime ??
            DateTime(
                DateTime.now().year, DateTime.now().month, DateTime.now().day),
        endTime = endTime ?? DateTime.now(),
        statistics = statistics ?? Statistics.fromJson(initialStatistics),
        statisticsDetails = statisticsDetails ??
            StatisticsDetails.fromJson(initialStatisticsDetails);

  StatisticsState copyWith(
      {ApiStatus? apiStatus,
      Statistics? statistics,
      String? message,
      StatisticsDataType? statisticsDataType,
      DateTime? startTime,
      DateTime? endTime,
      DateTimeType? dateTimeType,
      StatisticsDetails? statisticsDetails,
      bool? isLeftThresold,
      bool? isRightThresold}) {
    return StatisticsState(
        apiStatus: apiStatus ?? this.apiStatus,
        statistics: statistics ?? this.statistics,
        message: message ?? this.message,
        statisticsDataType: statisticsDataType ?? this.statisticsDataType,
        startTime: startTime ?? this.startTime,
        endTime: endTime ?? this.endTime,
        dateTimeType: dateTimeType ?? this.dateTimeType,
        statisticsDetails: statisticsDetails ?? this.statisticsDetails,
        isLeftThresold: isLeftThresold ?? this.isLeftThresold,
        isRightThresold: isRightThresold ?? this.isRightThresold);
  }

  @override
  List<Object?> get props => [
        apiStatus,
        statistics,
        message,
        statisticsDataType,
        startTime,
        endTime,
        dateTimeType,
        isLeftThresold,
        isRightThresold,
      ];
}

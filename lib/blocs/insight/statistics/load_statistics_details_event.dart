import 'package:nds_app/blocs/insight/statistics/statistics_event.dart';
import 'package:nds_app/models/enums/statistics_data_type.dart';

class LoadStatisticsDetails extends StatisticsEvent {
  final DateTime startTime;
  final DateTime endTime;
  final StatisticsDataType statisticsDataType;

  const LoadStatisticsDetails(
    this.statisticsDataType, {
    required this.startTime,
    required this.endTime,
  });

  @override
  List<Object> get props => [startTime, endTime, statisticsDataType];
}

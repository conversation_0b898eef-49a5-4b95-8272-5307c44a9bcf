import 'package:bloc/bloc.dart';
import 'package:nds_app/blocs/insight/statistics/day_event.dart';
import 'package:nds_app/blocs/insight/statistics/load_statistics_details_event.dart';
import 'package:nds_app/blocs/insight/statistics/load_statistics_event.dart';
import 'package:nds_app/blocs/insight/statistics/month_event.dart';
import 'package:nds_app/blocs/insight/statistics/week_event.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/enums/statistics_data_type.dart';
import 'package:nds_app/repository/statistics_repository.dart';
import 'package:nds_app/repository/statistics_repository_details.dart';
import 'statistics_event.dart';
import 'statistics_state.dart';

class StatisticsBloc extends Bloc<StatisticsEvent, StatisticsState> {
  StatisticsRepository statisticsRepository = StatisticsRepository();
  StatisticsDetailsRepository statisticsDetailsRepository =
      StatisticsDetailsRepository();
  StatisticsBloc() : super(StatisticsState()) {
    on<LoadStatisticsDetails>(fetchStatisticsDetailsData);
    on<LoadStatistics>(fetchStatisticsData);
    on<DayEvent>(_changeDayData);
    on<WeekEvent>(_changeWeekData);
    on<MonthEvent>(_changeMonthData);
  }

  void fetchStatisticsData(
      LoadStatistics event, Emitter<StatisticsState> emit) async {
    emit(state.copyWith(
      apiStatus: ApiStatus.loading,
      statisticsDataType: StatisticsDataType.statistics,
      dateTimeType: state.dateTimeType,
      startTime: event.startTime,
      endTime: event.endTime,
    ));
    await statisticsRepository
        .fetchData(event.startTime, event.endTime)
        .then((value) {
      emit(state.copyWith(
        apiStatus: ApiStatus.success,
        statistics: value,
        statisticsDetails: null,
        message: 'success',
        statisticsDataType: StatisticsDataType.statistics,
        dateTimeType: state.dateTimeType,
        startTime: event.startTime,
        endTime: event.endTime,
      ));
    }).onError((error, stackTrace) {
      emit(state.copyWith(
        apiStatus: ApiStatus.failure,
        message: error.toString(),
        dateTimeType: state.dateTimeType,
        startTime: event.startTime,
        endTime: event.endTime,
      ));
    });
  }

  void _changeDayData(DayEvent event, Emitter<StatisticsState> emit) {
    emit(state.copyWith(
        startTime: event.startTime,
        endTime: event.endTime,
        dateTimeType: event.dateTimeType,
        statisticsDataType: state.statisticsDataType,
        apiStatus: state.apiStatus,
        statistics: state.statistics,
        statisticsDetails: state.statisticsDetails,
        message: state.message,
        isLeftThresold: event.isLeftThresold,
        isRightThresold: event.isRightThresold));
  }

  void _changeWeekData(WeekEvent event, Emitter<StatisticsState> emit) {
    emit(state.copyWith(
        startTime: event.startTime,
        endTime: event.endTime,
        dateTimeType: event.dateTimeType,
        statisticsDataType: state.statisticsDataType,
        apiStatus: state.apiStatus,
        statistics: state.statistics,
        statisticsDetails: state.statisticsDetails,
        message: state.message,
        isLeftThresold: event.isLeftThresold,
        isRightThresold: event.isRightThresold));
  }

  void _changeMonthData(MonthEvent event, Emitter<StatisticsState> emit) {
    emit(state.copyWith(
        startTime: event.startTime,
        endTime: event.endTime,
        dateTimeType: event.dateTimeType,
        statisticsDataType: state.statisticsDataType,
        apiStatus: state.apiStatus,
        statistics: state.statistics,
        statisticsDetails: state.statisticsDetails,
        message: state.message,
        isLeftThresold: event.isLeftThresold,
        isRightThresold: event.isRightThresold));
  }

  void fetchStatisticsDetailsData(
      LoadStatisticsDetails event, Emitter<StatisticsState> emit) async {
    emit(state.copyWith(
      apiStatus: ApiStatus.loading,
      statisticsDataType: event.statisticsDataType,
      dateTimeType: state.dateTimeType,
      startTime: event.startTime,
      endTime: event.endTime,
    ));

    await statisticsDetailsRepository
        .fetchData(event.startTime, event.endTime, event.statisticsDataType,
            state.dateTimeType)
        .then((value) {
      emit(state.copyWith(
        apiStatus: ApiStatus.success,
        statistics: null,
        statisticsDetails: value,
        message: 'success',
        statisticsDataType: event.statisticsDataType,
        dateTimeType: state.dateTimeType,
        startTime: event.startTime,
        endTime: event.endTime,
      ));
    }).onError((error, stackTrace) {
      emit(state.copyWith(
        apiStatus: ApiStatus.failure,
        message: error.toString(),
        dateTimeType: state.dateTimeType,
        startTime: event.startTime,
        endTime: event.endTime,
      ));
    });
  }
}

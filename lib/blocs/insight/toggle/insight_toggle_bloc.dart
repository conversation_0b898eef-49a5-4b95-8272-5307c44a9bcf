import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'insight_toggle_event.dart';
part 'insight_toogle_state.dart';

class InsightToggleBloc extends Bloc<InsightToggleEvent, InsightToggleState> {
  InsightToggleBloc() : super(const InsightToggleState()) {
    on<SwitchEvent>(_changeSwitchButton);
  }

  void _changeSwitchButton(
      SwitchEvent event, Emitter<InsightToggleState> emit) {
    emit(state.copyWith(isSwitchRight: !state.isSwitchRight));
  }
}

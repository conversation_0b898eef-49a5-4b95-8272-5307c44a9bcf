import 'dart:async';

class NavigationBarStream {
  static final NavigationBarStream _singleton = NavigationBarStream._internal();

  factory NavigationBarStream() {
    return _singleton;
  }
  NavigationBarStream._internal();
  final _navigationBarStreamController = StreamController<int>.broadcast();
  StreamSink<int> get _inNavigationBar => _navigationBarStreamController.sink;
  Stream<int> get navigationBar => _navigationBarStreamController.stream;

  void submitIndex(int index) {
    _inNavigationBar.add(index);
  }

  void closeStream() {
    _navigationBarStreamController.close();
  }
}

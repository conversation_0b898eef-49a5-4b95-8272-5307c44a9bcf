import 'dart:async';

import 'package:nds_app/blocs/login/login_events.dart';
import 'package:nds_app/models/enums/mobile_number_status.dart';

class LoginScreenBloc {
  //Create method to return streamcontroller

  final _eventController = StreamController<LoginEvent>();
  Sink<LoginEvent> get eventSink => _eventController.sink;

  LoginScreenBloc() {
    _mobileNumber = "";
    _eventController.stream.listen(_mapEventToState);
  }

  void _mapEventToState(LoginEvent loginEvent) {
    if (loginEvent is EnterNumberEvent) {
      //switch case doesn't work, only works when "is" condition is used
      fillNumber(loginEvent.mobileNumber);
    } else if (loginEvent is LoginErrorEvent) {
      _inMobileNumberStatus.add(MobileNumberStatus.invalid);
    }
  }

  /* Fill Mobile Number */

  late String _mobileNumber;
  final _mobileNumberStreamController = StreamController<String>.broadcast();
  StreamSink<String> get _inMobileNumber => _mobileNumberStreamController.sink;
  Stream<String> get mobileNumber => _mobileNumberStreamController.stream;

  void fillNumber(String mobileNumber) {
    _mobileNumber = mobileNumber;
    validateMobileNumber(mobileNumber);
    _inMobileNumber.add(_mobileNumber);
  }

  /* Validate Mobile Number */

  MobileNumberStatus _mobileNumberStatus = MobileNumberStatus.notEntered;
  final _mobileNumberStatusStreamController =
      StreamController<MobileNumberStatus>.broadcast();
  StreamSink<MobileNumberStatus> get _inMobileNumberStatus =>
      _mobileNumberStatusStreamController.sink;
  Stream<MobileNumberStatus> get mobileNumberStatus =>
      _mobileNumberStatusStreamController.stream;

  void validateMobileNumber(String mobileNumber) {
    //validation to change?

    if (mobileNumber.isNotEmpty) {
      if (mobileNumber.length == 10) {
        _mobileNumberStatus = MobileNumberStatus.valid;
      } else {
        _mobileNumberStatus = MobileNumberStatus.invalid;
      }
    } else {
      _mobileNumberStatus = MobileNumberStatus.notEntered;
    }
    _inMobileNumberStatus.add(_mobileNumberStatus);
  }

  close() {
    _mobileNumberStatusStreamController.close();
    _mobileNumberStreamController.close();
    _eventController.close();
  }
}

import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_change_event.dart';
import 'package:nds_app/blocs/connectivity/connectivity_event.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_initial.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';

class InternetConnectivityBloc
    extends Bloc<InternetConnectivityEvent, InternetConnectivityState> {
  late StreamSubscription<ConnectivityResult> _connectivitySubscription;

  InternetConnectivityBloc() : super(InternetConnectivityInitial()) {
    // Listen to connectivity changes
    _connectivitySubscription = Connectivity()
        .onConnectivityChanged
        .listen((ConnectivityResult result) {
      // Determine if there is an internet connection
      bool isConnected = result != ConnectivityResult.none;
      // Add the ConnectivityChanged event
      add(InternetConnectivityChangedEvent(isConnected));
    });

    // Handle the ConnectivityChanged event
    on<InternetConnectivityChangedEvent>((event, emit) {
      if (event.isConnected) {
        emit(InternetConnectivitySuccess(event.isConnected));
      } else {
        emit(InternetConnectivityFailure());
      }
    });
  }

  @override
  Future<void> close() {
    _connectivitySubscription.cancel();
    return super.close();
  }
}

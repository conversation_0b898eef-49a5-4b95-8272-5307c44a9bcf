import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/services/api_service.dart';

class ProfileStream {
  static final ProfileStream _singleton = ProfileStream._internal();

  factory ProfileStream() => _singleton;

  ProfileStream._internal();

  final _profileStreamController =
      StreamController<Map<String, dynamic>>.broadcast();

  Stream<Map<String, dynamic>> get profileStream =>
      _profileStreamController.stream;

  Future<void> fetchProfileDetails() async {
    try {
      http.Response profileDetailsResponse =
          await BackendApi.initiateGetCall(ApiUrls.profileDetails);
      if (profileDetailsResponse.statusCode == 200) {
        final data =
            jsonDecode(profileDetailsResponse.body) as Map<String, dynamic>;
        _profileStreamController.add(data);
      } else {
        _profileStreamController.addError(
            'Failed to load profile details: ${profileDetailsResponse.statusCode}');
      }
    } catch (e) {
      _profileStreamController.addError('An error occurred: $e');
    }
  }

  void closeStream() {
    _profileStreamController.close();
  }
}

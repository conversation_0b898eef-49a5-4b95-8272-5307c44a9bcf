import 'package:bloc/bloc.dart';
import 'package:nds_app/blocs/vehicle/fleet/fleet_event.dart';
import 'package:nds_app/blocs/vehicle/fleet/fleet_state.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/fleet.dart';
import 'package:nds_app/repository/fleet_repository.dart';

class FleetBloc extends Bloc<FleetEvent, FleetState> {
  final FleetRepository fleetRepository = FleetRepository();

  FleetBloc() : super(const FleetState()) {
    on<LoadFleetEvent>(_loadFleets);
  }

  Future<void> _loadFleets(
      LoadFleetEvent event, Emitter<FleetState> emit) async {
    final bool isFirstPage = event.page == 0;
    
    // Prevent duplicate requests
    if (!isFirstPage && state.isLoadingMore) return;
    
    if (isFirstPage) {
      emit(state.copyWith(
        apiStatus: ApiStatus.loading,
        message: 'Loading fleets...',
        currentPage: event.page,
        isLoadingMore: false,
        items: [], // Clear items on first page load
      ));
    } else {
      emit(state.copyWith(
        isLoadingMore: true,
        currentPage: event.page,
      ));
    }

    try {
      final fleetsResponse = await fleetRepository.fetchFleets(
        page: event.page,
        size: event.size,
        sort: event.sort,
      );

      if (fleetsResponse != null) {
        final List<Fleet> newItems = isFirstPage
            ? fleetsResponse.fleets
            : [...state.items, ...fleetsResponse.fleets];

        emit(state.copyWith(
          apiStatus: ApiStatus.success,
          fleetsResponse: fleetsResponse,
          items: newItems,
          message: 'Fleets loaded successfully',
          isLoadingMore: false,
        ));
      } else {
        emit(state.copyWith(
          apiStatus: ApiStatus.failure,
          message: 'Failed to load fleets - No data received',
          isLoadingMore: false,
        ));
      }
    } catch (error) {
      emit(state.copyWith(
        apiStatus: ApiStatus.failure,
        message: error.toString(),
        isLoadingMore: false,
      ));
    }
  }
}

import 'package:equatable/equatable.dart';

abstract class FleetEvent extends Equatable {
  const FleetEvent();

  @override
  List<Object> get props => [];
}

class LoadFleetEvent extends FleetEvent {
  final int page;
  final int size;
  final String sort;

  const LoadFleetEvent({
    this.page = 0,
    this.size = 10,
    this.sort = '',
  });

  @override
  List<Object> get props => [page, size, sort];
}

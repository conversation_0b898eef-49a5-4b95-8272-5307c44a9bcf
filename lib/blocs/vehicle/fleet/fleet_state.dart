import 'package:equatable/equatable.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/fleet.dart';

class FleetState extends Equatable {
  final ApiStatus apiStatus;
  final FleetsResponse? fleetsResponse;
  final List<Fleet> items;
  final int currentPage;
  final bool isLoadingMore;
  final String message;

  const FleetState({
    this.apiStatus = ApiStatus.initial,
    this.fleetsResponse,
    this.items = const [],
    this.currentPage = 0,
    this.isLoadingMore = false,
    this.message = '',
  });

  @override
  List<Object?> get props => [apiStatus, fleetsResponse, items, currentPage, isLoadingMore, message];

  FleetState copyWith({
    ApiStatus? apiStatus,
    FleetsResponse? fleetsResponse,
    List<Fleet>? items,
    int? currentPage,
    bool? isLoadingMore,
    String? message,
  }) {
    return FleetState(
      apiStatus: apiStatus ?? this.apiStatus,
      fleetsResponse: fleetsResponse ?? this.fleetsResponse,
      items: items ?? this.items,
      currentPage: currentPage ?? this.currentPage,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      message: message ?? this.message,
    );
  }
}

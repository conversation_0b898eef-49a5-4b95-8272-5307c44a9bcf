import 'package:bloc/bloc.dart';
import 'package:nds_app/blocs/vehicle/vehicle_page_details/vehicle_details_event.dart';
import 'package:nds_app/blocs/vehicle/vehicle_page_details/vehicle_details_state.dart';
import 'package:nds_app/blocs/vehicle/vehicle_page_details/vehicle_page_viewed_event.dart';

class VehicleDetailsBloc
    extends Bloc<VehicleDetailsEvent, VehicleDetailsState> {
  VehicleDetailsBloc() : super(const VehicleDetailsState()) {
    on<ViewEvent>(_changeView);
  }
  Future<void> _changeView(
      ViewEvent event, Emitter<VehicleDetailsState> emit) async {
    emit(state.copyWith(isViewed: event.isViewed));
  }
}

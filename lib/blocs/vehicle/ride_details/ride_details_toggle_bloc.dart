import 'package:bloc/bloc.dart';
import 'package:nds_app/blocs/vehicle/ride_details/ride_details_toggle_event.dart';
import 'package:nds_app/blocs/vehicle/ride_details/ride_details_toggle_state.dart';

class RideDetailsToggleBloc
    extends Bloc<RideDetailsToggleEvent, RideDetailsToggleState> {
  RideDetailsToggleBloc() : super(const RideDetailsToggleState()) {
    on<SwitchEvent>(_changeSwitchButton);
  }

  Future<void> _changeSwitchButton(
      SwitchEvent event, Emitter<RideDetailsToggleState> emit) async {
    emit(state.copyWith(isSwitchRight: !state.isSwitchRight));
  }
}

import 'package:equatable/equatable.dart';

/// Events for leadership vehicle detail toggle functionality
abstract class LeadershipVehicleDetailToggleEvent extends Equatable {
  const LeadershipVehicleDetailToggleEvent();

  @override
  List<Object?> get props => [];
}

/// Event to switch to Range view
class SwitchToRangeEvent extends LeadershipVehicleDetailToggleEvent {
  const SwitchToRangeEvent();
}

/// Event to switch to Motor view
class SwitchToMotorEvent extends LeadershipVehicleDetailToggleEvent {
  const SwitchToMotorEvent();
}

/// Event to switch to Battery view
class SwitchToBatteryEvent extends LeadershipVehicleDetailToggleEvent {
  const SwitchToBatteryEvent();
}

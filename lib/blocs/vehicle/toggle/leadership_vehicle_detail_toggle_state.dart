import 'package:equatable/equatable.dart';

/// States for leadership vehicle detail toggle functionality
class LeadershipVehicleDetailToggleState extends Equatable {
  final int selectedIndex;

  const LeadershipVehicleDetailToggleState({
    this.selectedIndex = 0,
  });

  LeadershipVehicleDetailToggleState copyWith({
    int? selectedIndex,
  }) {
    return LeadershipVehicleDetailToggleState(
      selectedIndex: selectedIndex ?? this.selectedIndex,
    );
  }

  @override
  List<Object?> get props => [selectedIndex];
}

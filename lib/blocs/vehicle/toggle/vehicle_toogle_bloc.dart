import 'package:bloc/bloc.dart';
import 'package:nds_app/blocs/vehicle/toggle/vehicle_toogle_event.dart';
import 'package:nds_app/blocs/vehicle/toggle/vehicle_toogle_state.dart';

class VehicleToogleBloc
    extends Bloc<ConnectVehicleToogleEvent, VehicleToogleState> {
  VehicleToogleBloc() : super(const VehicleToogleState()) {
    on<SwitchEvent>(_changeSwitchButton);
  }
  Future<void> _changeSwitchButton(
      SwitchEvent event, Emitter<VehicleToogleState> emit) async {
    emit(state.copyWith(isSwitchRight: !state.isSwitchRight));
  }
}

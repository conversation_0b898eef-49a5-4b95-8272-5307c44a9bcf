import 'package:bloc/bloc.dart';
import 'package:nds_app/blocs/vehicle/toggle/leadership_vehicle_toggle_event.dart';
import 'package:nds_app/blocs/vehicle/toggle/leadership_vehicle_toggle_state.dart';

class LeadershipVehicleToggleBloc
    extends Bloc<LeadershipVehicleToggleEvent, LeadershipVehicleToggleState> {
  LeadershipVehicleToggleBloc() : super(const LeadershipVehicleToggleState()) {
    on<SwitchToModelEvent>(_switchToModel);
    on<SwitchToFleetEvent>(_switchToFleet);
    on<SwitchToVehicleEvent>(_switchToVehicle);
  }

  Future<void> _switchToModel(
      SwitchToModelEvent event, Emitter<LeadershipVehicleToggleState> emit) async {
    emit(state.copyWith(selectedIndex: 0));
  }

  Future<void> _switchToFleet(
      SwitchToFleetEvent event, Emitter<LeadershipVehicleToggleState> emit) async {
    emit(state.copyWith(selectedIndex: 1));
  }

  Future<void> _switchToVehicle(
      SwitchToVehicleEvent event, Emitter<LeadershipVehicleToggleState> emit) async {
    emit(state.copyWith(selectedIndex: 2));
  }
}

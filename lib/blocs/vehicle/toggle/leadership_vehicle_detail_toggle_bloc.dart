import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/vehicle/toggle/leadership_vehicle_detail_toggle_event.dart';
import 'package:nds_app/blocs/vehicle/toggle/leadership_vehicle_detail_toggle_state.dart';

/// Bloc for managing leadership vehicle detail toggle state
class LeadershipVehicleDetailToggleBloc extends Bloc<LeadershipVehicleDetailToggleEvent, LeadershipVehicleDetailToggleState> {
  LeadershipVehicleDetailToggleBloc() : super(const LeadershipVehicleDetailToggleState()) {
    on<SwitchToRangeEvent>(_onSwitchToRange);
    on<SwitchToMotorEvent>(_onSwitchToMotor);
    on<SwitchToBatteryEvent>(_onSwitchToBattery);
  }

  void _onSwitchToRange(SwitchToRangeEvent event, Emitter<LeadershipVehicleDetailToggleState> emit) {
    emit(state.copyWith(selectedIndex: 0));
  }

  void _onSwitchToMotor(SwitchToMotorEvent event, Emitter<LeadershipVehicleDetailToggleState> emit) {
    emit(state.copyWith(selectedIndex: 1));
  }

  void _onSwitchToBattery(SwitchToBatteryEvent event, Emitter<LeadershipVehicleDetailToggleState> emit) {
    emit(state.copyWith(selectedIndex: 2));
  }
}

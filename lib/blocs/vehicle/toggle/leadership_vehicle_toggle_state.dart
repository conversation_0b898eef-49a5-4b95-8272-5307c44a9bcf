import 'package:equatable/equatable.dart';

class LeadershipVehicleToggleState extends Equatable {
  final int selectedIndex;
  const LeadershipVehicleToggleState({this.selectedIndex = 0});

  @override
  List<Object> get props => [selectedIndex];

  LeadershipVehicleToggleState copyWith({int? selectedIndex}) {
    return LeadershipVehicleToggleState(
        selectedIndex: selectedIndex ?? this.selectedIndex);
  }
}

import 'package:equatable/equatable.dart';

abstract class VehiclesEvent extends Equatable {
  const VehiclesEvent();
  @override
  List<Object?> get props => [];
}

class LoadVehiclesPageEvent extends VehiclesEvent {
  final int page;
  final int size;
  final String sort;
  const LoadVehiclesPageEvent({required this.page, this.size = 10, this.sort = ''});
  @override
  List<Object?> get props => [page, size, sort];
}

class RefreshVehiclesEvent extends VehiclesEvent {
  const RefreshVehiclesEvent();
}

import 'package:bloc/bloc.dart';
import 'package:nds_app/blocs/vehicle/vehicles/vehicles_event.dart';
import 'package:nds_app/blocs/vehicle/vehicles/vehicles_state.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/repository/vehicles_repository.dart';

class VehiclesBloc extends Bloc<VehiclesEvent, VehiclesState> {
  final VehiclesRepository repository = VehiclesRepository();
  VehiclesBloc() : super(const VehiclesState()) {
    on<LoadVehiclesPageEvent>(_loadPage);
    on<RefreshVehiclesEvent>(_refresh);
  }

  Future<void> _loadPage(LoadVehiclesPageEvent event, Emitter<VehiclesState> emit) async {
    final bool isFirstPage = event.page == 0;
    
    // Prevent duplicate requests
    if (!isFirstPage && state.isLoadingMore) return;
    
    if (isFirstPage) {
      emit(state.copyWith(
        apiStatus: ApiStatus.loading,
        message: 'Loading vehicles...',
        currentPage: event.page,
        isLoadingMore: false,
        items: [], // Clear items on first page load
        totalPages: 0,
        totalElements: 0,
      ));
    } else {
      emit(state.copyWith(
        isLoadingMore: true,
        currentPage: event.page,
      ));
    }

    try {
      final response = await repository.fetchVehicles(page: event.page, size: event.size, sort: event.sort);
      if (response != null) {
        final merged = isFirstPage ? response.vehicleDetailDto : [...state.items, ...response.vehicleDetailDto];
        emit(state.copyWith(
          apiStatus: ApiStatus.success,
          items: merged,
          totalPages: response.totalPages,
          totalElements: response.totalElements,
          currentPage: event.page,
          message: 'Vehicles loaded successfully',
          isLoadingMore: false,
        ));
      } else {
        emit(state.copyWith(
          apiStatus: ApiStatus.failure,
          message: 'Failed to load vehicles - No data received',
          isLoadingMore: false,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        apiStatus: ApiStatus.failure,
        message: e.toString(),
        isLoadingMore: false,
      ));
    }
  }

  Future<void> _refresh(RefreshVehiclesEvent event, Emitter<VehiclesState> emit) async {
    add(const LoadVehiclesPageEvent(page: 0));
  }
}

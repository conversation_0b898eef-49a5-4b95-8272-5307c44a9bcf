import 'package:equatable/equatable.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/vehicle_details.dart';

class VehiclesState extends Equatable {
  final ApiStatus apiStatus;
  final List<VehicleDetail> items;
  final int totalPages;
  final int totalElements;
  final int currentPage;
  final String message;
  final bool isLoadingMore;

  const VehiclesState({
    this.apiStatus = ApiStatus.initial,
    this.items = const [],
    this.totalPages = 0,
    this.totalElements = 0,
    this.currentPage = 0,
    this.message = '',
    this.isLoadingMore = false,
  });

  VehiclesState copyWith({
    ApiStatus? apiStatus,
    List<VehicleDetail>? items,
    int? totalPages,
    int? totalElements,
    int? currentPage,
    String? message,
    bool? isLoadingMore,
  }) {
    return VehiclesState(
      apiStatus: apiStatus ?? this.apiStatus,
      items: items ?? this.items,
      totalPages: totalPages ?? this.totalPages,
      totalElements: totalElements ?? this.totalElements,
      currentPage: currentPage ?? this.currentPage,
      message: message ?? this.message,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
    );
  }

  @override
  List<Object?> get props => [apiStatus, items, totalPages, totalElements, currentPage, message, isLoadingMore];
}

import 'package:equatable/equatable.dart';
import 'package:nds_app/models/enums/vehicle_performace_type.dart';

/// Base class for all leadership performance events
abstract class LeadershipPerformanceEvent extends Equatable {
  const LeadershipPerformanceEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load performance data for a specific type (battery/motor)
class LoadPerformanceDataEvent extends LeadershipPerformanceEvent {
  final int entityId;
  final int startTime;
  final int endTime;
  final VehicleType vehicleType;
  final SummaryType summaryType;

  const LoadPerformanceDataEvent({
    required this.entityId,
    required this.startTime,
    required this.endTime,
    required this.vehicleType,
    required this.summaryType,
  });

  @override
  List<Object?> get props =>
      [entityId, startTime, endTime, vehicleType, summaryType];
}

/// Event to change time filter and reload data
class ChangeTimeFilterEvent extends LeadershipPerformanceEvent {
  final String timeFilter;
  final int entityId;
  final VehicleType vehicleType;
  final SummaryType summaryType;

  const ChangeTimeFilterEvent({
    required this.timeFilter,
    required this.entityId,
    required this.vehicleType,
    required this.summaryType,
  });

  @override
  List<Object?> get props => [timeFilter, entityId, vehicleType, summaryType];
}

/// Event to refresh current data
class RefreshPerformanceDataEvent extends LeadershipPerformanceEvent {
  final int entityId;
  final VehicleType vehicleType;
  final SummaryType summaryType;

  const RefreshPerformanceDataEvent({
    required this.entityId,
    required this.vehicleType,
    required this.summaryType,
  });

  @override
  List<Object?> get props => [entityId, vehicleType, summaryType];
}

/// Event to clear performance data
class ClearPerformanceDataEvent extends LeadershipPerformanceEvent {
  const ClearPerformanceDataEvent();
}

/// Event to toggle between graph and table view
class ToggleViewEvent extends LeadershipPerformanceEvent {
  const ToggleViewEvent();

  @override
  List<Object> get props => [];
}

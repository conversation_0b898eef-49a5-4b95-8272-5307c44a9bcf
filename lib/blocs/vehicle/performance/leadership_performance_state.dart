import 'package:equatable/equatable.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/enums/vehicle_performace_type.dart';
import 'package:nds_app/models/battery_performance_model.dart';
import 'package:nds_app/models/alarm_performance_model.dart';
import 'package:nds_app/models/range_performance_model.dart';

/// State class for leadership performance data
class LeadershipPerformanceState extends Equatable {
  final ApiStatus apiStatus;
  final String message;
  final String selectedTimeFilter;
  final DateTime startTime;
  final DateTime endTime;
  final SummaryType? currentSummaryType;
  final VehicleType? currentVehicleType;
  final int? currentEntityId;
  final bool isGraphView; // Toggle between graph and table view

  // Performance data
  final BatteryPerformanceModel? batteryPerformance;
  final MotorPerformanceModel? motorPerformance;
  final RangePerformanceModel? rangePerformance;

  const LeadershipPerformanceState({
    this.apiStatus = ApiStatus.initial,
    this.message = '',
    this.selectedTimeFilter = 'Today',
    required this.startTime,
    required this.endTime,
    this.currentSummaryType,
    this.currentVehicleType,
    this.currentEntityId,
    this.isGraphView = true, // Default to graph view
    this.batteryPerformance,
    this.motorPerformance,
    this.rangePerformance,
  });

  // Factory constructor with proper default values
  factory LeadershipPerformanceState.initial() {
    final now = DateTime.now();
    return LeadershipPerformanceState(
      startTime: now.subtract(const Duration(days: 1)),
      endTime: now,
      isGraphView: true, // Default to graph view
    );
  }

  LeadershipPerformanceState copyWith({
    ApiStatus? apiStatus,
    String? message,
    String? selectedTimeFilter,
    DateTime? startTime,
    DateTime? endTime,
    SummaryType? currentSummaryType,
    VehicleType? currentVehicleType,
    int? currentEntityId,
    bool? isGraphView,
    BatteryPerformanceModel? batteryPerformance,
    MotorPerformanceModel? motorPerformance,
    RangePerformanceModel? rangePerformance,
    // Add flag to explicitly set performance data to null when needed
    bool clearBatteryPerformance = false,
    bool clearMotorPerformance = false,
    bool clearRangePerformance = false,
  }) {
    return LeadershipPerformanceState(
      apiStatus: apiStatus ?? this.apiStatus,
      message: message ?? this.message,
      selectedTimeFilter: selectedTimeFilter ?? this.selectedTimeFilter,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      currentSummaryType: currentSummaryType ?? this.currentSummaryType,
      currentVehicleType: currentVehicleType ?? this.currentVehicleType,
      currentEntityId: currentEntityId ?? this.currentEntityId,
      isGraphView: isGraphView ?? this.isGraphView,
      batteryPerformance: clearBatteryPerformance ? null : (batteryPerformance ?? this.batteryPerformance),
      motorPerformance: clearMotorPerformance ? null : (motorPerformance ?? this.motorPerformance),
      rangePerformance: clearRangePerformance ? null : (rangePerformance ?? this.rangePerformance),
    );
  }

  // Helper methods to check if data is available
  bool get hasBatteryData => batteryPerformance != null;
  bool get hasMotorData => motorPerformance != null;
  bool get hasRangeData => rangePerformance != null;
  bool get isLoading => apiStatus == ApiStatus.loading;
  bool get hasError => apiStatus == ApiStatus.failure;
  bool get isSuccess => apiStatus == ApiStatus.success;

  @override
  List<Object?> get props => [
        apiStatus,
        message,
        selectedTimeFilter,
        startTime,
        endTime,
        currentSummaryType,
        currentVehicleType,
        currentEntityId,
        isGraphView,
        batteryPerformance,
        motorPerformance,
        rangePerformance,
      ];
}

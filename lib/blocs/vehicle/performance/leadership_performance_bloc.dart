import 'package:bloc/bloc.dart';
import 'package:nds_app/blocs/vehicle/performance/leadership_performance_event.dart';
import 'package:nds_app/blocs/vehicle/performance/leadership_performance_state.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/enums/vehicle_performace_type.dart';
import 'package:nds_app/models/battery_performance_model.dart';
import 'package:nds_app/models/alarm_performance_model.dart';
import 'package:nds_app/models/range_performance_model.dart';
import 'package:nds_app/repository/vehicle_performance_repo.dart';
import 'package:nds_app/utils/time_filter_utils.dart';

/// BLoC for managing leadership performance data (battery and motor)
class LeadershipPerformanceBloc
    extends Bloc<LeadershipPerformanceEvent, LeadershipPerformanceState> {
  final VehiclePerformanceRepo _repository;

  LeadershipPerformanceBloc({VehiclePerformanceRepo? repository})
      : _repository = repository ?? VehiclePerformanceRepo(),
        super(LeadershipPerformanceState.initial()) {
    on<LoadPerformanceDataEvent>(_onLoadPerformanceData);
    on<ChangeTimeFilterEvent>(_onChangeTimeFilter);
    on<RefreshPerformanceDataEvent>(_onRefreshPerformanceData);
    on<ClearPerformanceDataEvent>(_onClearPerformanceData);
    on<ToggleViewEvent>(_onToggleView);
  }

  /// Handle loading performance data
  Future<void> _onLoadPerformanceData(
    LoadPerformanceDataEvent event,
    Emitter<LeadershipPerformanceState> emit,
  ) async {
    emit(state.copyWith(
      apiStatus: ApiStatus.loading,
      message: 'Loading ${event.summaryType.value} performance data...',
      currentSummaryType: event.summaryType,
      currentVehicleType: event.vehicleType,
      currentEntityId: event.entityId,
    ));

    try {
      if (event.summaryType == SummaryType.battery) {
        final batteryData =
            await _repository.getPerformance<BatteryPerformanceModel>(
          entityId: event.entityId,
          startTime: event.startTime,
          endTime: event.endTime,
          vehicleType: event.vehicleType,
          summaryType: event.summaryType,
        );

        if (batteryData != null) {
          emit(state.copyWith(
            apiStatus: ApiStatus.success,
            message: 'Battery performance data loaded successfully',
            batteryPerformance: batteryData,
            // Preserve existing range and motor data
            rangePerformance: state.rangePerformance,
            motorPerformance: state.motorPerformance,
          ));
        } else {
          emit(state.copyWith(
            apiStatus: ApiStatus.failure,
            message:
                'Failed to load battery performance data - No data received',
            // Preserve existing data even on failure
            rangePerformance: state.rangePerformance,
            motorPerformance: state.motorPerformance,
          ));
        }
      } else if (event.summaryType == SummaryType.motor) {
        final motorData =
            await _repository.getPerformance<MotorPerformanceModel>(
              entityId: event.entityId,
          startTime: event.startTime,
          endTime: event.endTime,
          vehicleType: event.vehicleType,
          summaryType: event.summaryType,
        );

        if (motorData != null) {
          emit(state.copyWith(
            apiStatus: ApiStatus.success,
            message: 'Motor performance data loaded successfully',
            motorPerformance: motorData,
            // Preserve existing range and battery data
            rangePerformance: state.rangePerformance,
            batteryPerformance: state.batteryPerformance,
          ));
        } else {
          emit(state.copyWith(
            apiStatus: ApiStatus.failure,
            message: 'Failed to load motor performance data - No data received',
            // Preserve existing data even on failure
            rangePerformance: state.rangePerformance,
            batteryPerformance: state.batteryPerformance,
          ));
        }
      } else if (event.summaryType == SummaryType.range) {
        final rangeData =
            await _repository.getPerformance<RangePerformanceModel>(
              entityId: event.entityId,
          startTime: event.startTime,
          endTime: event.endTime,
          vehicleType: event.vehicleType,
          summaryType: event.summaryType,
        );

        if (rangeData != null) {
          emit(state.copyWith(
            apiStatus: ApiStatus.success,
            message: 'Range performance data loaded successfully',
            rangePerformance: rangeData,
            // Preserve existing motor and battery data
            motorPerformance: state.motorPerformance,
            batteryPerformance: state.batteryPerformance,
          ));
        } else {
          emit(state.copyWith(
            apiStatus: ApiStatus.failure,
            message: 'Failed to load range performance data - No data received',
            // Preserve existing data even on failure
            motorPerformance: state.motorPerformance,
            batteryPerformance: state.batteryPerformance,
          ));
        }
      }
    } catch (error) {
      emit(state.copyWith(
        apiStatus: ApiStatus.failure,
        message:
            'Error loading ${event.summaryType.value} performance data: ${error.toString()}',
        // Preserve existing data even on error
        rangePerformance: state.rangePerformance,
        motorPerformance: state.motorPerformance,
        batteryPerformance: state.batteryPerformance,
      ));
    }
  }

  /// Handle time filter change
  Future<void> _onChangeTimeFilter(
    ChangeTimeFilterEvent event,
    Emitter<LeadershipPerformanceState> emit,
  ) async {
    final timeRange = TimeFilterUtils.calculateTimeRange(event.timeFilter);
    final startTime =
        DateTime.fromMillisecondsSinceEpoch(timeRange['startTime']!);
    final endTime = DateTime.fromMillisecondsSinceEpoch(timeRange['endTime']!);

    emit(state.copyWith(
      selectedTimeFilter: event.timeFilter,
      startTime: startTime,
      endTime: endTime,
    ));

    // Reload data with new time range
    add(LoadPerformanceDataEvent(
      entityId: event.entityId,
      startTime: timeRange['startTime']!,
      endTime: timeRange['endTime']!,
      vehicleType: event.vehicleType,
      summaryType: event.summaryType,
    ));
  }

  /// Handle refresh performance data
  Future<void> _onRefreshPerformanceData(
    RefreshPerformanceDataEvent event,
    Emitter<LeadershipPerformanceState> emit,
  ) async {
    if (state.currentSummaryType != null) {
      final timeRange =
          TimeFilterUtils.calculateTimeRange(state.selectedTimeFilter);

      add(LoadPerformanceDataEvent(
        entityId: event.entityId,
        startTime: timeRange['startTime']!,
        endTime: timeRange['endTime']!,
        vehicleType: event.vehicleType,
        summaryType: event.summaryType,
      ));
    }
  }

  /// Handle clear performance data
  Future<void> _onClearPerformanceData(
    ClearPerformanceDataEvent event,
    Emitter<LeadershipPerformanceState> emit,
  ) async {
    emit(state.copyWith(
      clearBatteryPerformance: true,
      clearMotorPerformance: true,
      clearRangePerformance: true,
      apiStatus: ApiStatus.initial,
      message: '',
    ));
  }

  /// Handle toggle between graph and table view
  Future<void> _onToggleView(
    ToggleViewEvent event,
    Emitter<LeadershipPerformanceState> emit,
  ) async {
    emit(state.copyWith(isGraphView: !state.isGraphView));
  }
}

import 'package:equatable/equatable.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/vehicle_model.dart';

class VehicleModelsState extends Equatable {
  final ApiStatus apiStatus;
  final VehicleModelsResponse? vehicleModelsResponse;
  final List<VehicleModel> items;
  final int currentPage;
  final bool isLoadingMore;
  final String message;

  const VehicleModelsState({
    this.apiStatus = ApiStatus.initial,
    this.vehicleModelsResponse,
    this.items = const [],
    this.currentPage = 0,
    this.isLoadingMore = false,
    this.message = '',
  });

  @override
  List<Object?> get props => [apiStatus, vehicleModelsResponse, items, currentPage, isLoadingMore, message];

  VehicleModelsState copyWith({
    ApiStatus? apiStatus,
    VehicleModelsResponse? vehicleModelsResponse,
    List<VehicleModel>? items,
    int? currentPage,
    bool? isLoadingMore,
    String? message,
  }) {
    return VehicleModelsState(
      apiStatus: apiStatus ?? this.apiStatus,
      vehicleModelsResponse: vehicleModelsResponse ?? this.vehicleModelsResponse,
      items: items ?? this.items,
      currentPage: currentPage ?? this.currentPage,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      message: message ?? this.message,
    );
  }
}

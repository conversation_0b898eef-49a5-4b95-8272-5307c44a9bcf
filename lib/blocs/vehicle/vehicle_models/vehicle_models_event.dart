import 'package:equatable/equatable.dart';

abstract class VehicleModelsEvent extends Equatable {
  const VehicleModelsEvent();

  @override
  List<Object> get props => [];
}

class LoadVehicleModelsEvent extends VehicleModelsEvent {
  final int page;
  final int size;
  final String sort;

  const LoadVehicleModelsEvent({
    this.page = 0,
    this.size = 10,
    this.sort = '',
  });

  @override
  List<Object> get props => [page, size, sort];
}

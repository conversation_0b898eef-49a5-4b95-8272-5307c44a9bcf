import 'package:bloc/bloc.dart';
import 'package:nds_app/blocs/vehicle/vehicle_models/vehicle_models_event.dart';
import 'package:nds_app/blocs/vehicle/vehicle_models/vehicle_models_state.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/repository/vehicle_models_repository.dart';

class VehicleModelsBloc extends Bloc<VehicleModelsEvent, VehicleModelsState> {
  final VehicleModelsRepository vehicleModelsRepository = VehicleModelsRepository();

  VehicleModelsBloc() : super(const VehicleModelsState()) {
    on<LoadVehicleModelsEvent>(_loadVehicleModels);
  }

  Future<void> _loadVehicleModels(
      LoadVehicleModelsEvent event, Emitter<VehicleModelsState> emit) async {
    final bool isFirstPage = event.page == 0;
    
    // Prevent duplicate requests
    if (!isFirstPage && state.isLoadingMore) return;
    
    if (isFirstPage) {
      emit(state.copyWith(
        apiStatus: ApiStatus.loading,
        message: 'Loading vehicle models...',
        currentPage: event.page,
        isLoadingMore: false,
      ));
    } else {
      emit(state.copyWith(
        isLoadingMore: true,
        currentPage: event.page,
      ));
    }

    try {
      final vehicleModelsResponse = await vehicleModelsRepository.fetchVehicleModels(
        page: event.page,
        size: event.size,
        sort: event.sort,
      );

      if (vehicleModelsResponse != null) {
        final merged = isFirstPage
            ? vehicleModelsResponse.vehicleModels
            : [...state.items, ...vehicleModelsResponse.vehicleModels];
        emit(state.copyWith(
          apiStatus: ApiStatus.success,
          vehicleModelsResponse: vehicleModelsResponse,
          items: merged,
          message: 'Vehicle models loaded successfully',
          isLoadingMore: false,
        ));
      } else {
        emit(state.copyWith(
          apiStatus: ApiStatus.failure,
          message: 'Failed to load vehicle models - No data received',
          isLoadingMore: false,
        ));
      }
    } catch (error) {
      emit(state.copyWith(
        apiStatus: ApiStatus.failure,
        message: error.toString(),
        isLoadingMore: false,
      ));
    }
  }
}

import 'dart:async';

import 'package:nds_app/blocs/connect/connect_events.dart';

class ConnectScreenStream {
  final _eventController = StreamController<ConnectEvent>();
  Sink<ConnectEvent> get eventSink => _eventController.sink;
  bool isCreated = false;
  late List<String> _digits;
  late List<StreamController<String>> _streamControllers;
  late List<StreamSink<String>> _streamSinks;
  late List<Stream<String>> streams;

  ConnectScreenStream() {
    _eventController.stream.listen(_mapEventToState);
    if (!isCreated) {
      _digits = [];
      _streamControllers = [];
      _streamSinks = [];
      streams = [];
      for (int i = 0; i < 4; i++) {
        _digits.add("");
        final controller = StreamController<String>.broadcast();
        _streamControllers.add(controller);
        _streamSinks.add(controller.sink);
        streams.add(controller.stream);
      }
      isCreated = true;
    }
  }

  final _messageStreamController = StreamController<String>.broadcast();
  StreamSink<String> get _inMessage => _messageStreamController.sink;
  Stream<String> get message => _messageStreamController.stream;

  void _mapEventToState(ConnectEvent connectEvent) {
    if (connectEvent is FillDigitEvent) {
      submitDigit(connectEvent.digit, connectEvent.index);
    } else if (connectEvent is MessageEvent) {
      messageSubmit(connectEvent.message);
    }
  }

  void submitDigit(String digit, int index) {
    _digits[index] = digit;
    _streamSinks[index].add(_digits[index]);
    submitCode();
  }

  messageSubmit(String message) {
    _inMessage.add(message);
  }

  void updateMessage(String message) {
    messageSubmit(message);
  }

  late String _code;

  final _codeStreamController = StreamController<String>.broadcast();
  StreamSink<String> get _inCode => _codeStreamController.sink;
  Stream<String> get code => _codeStreamController.stream;

  void submitCode() {
    _code = "";
    for (int i = 0; i < _digits.length; i++) {
      _code += _digits[i].trim();
    }
    _inCode.add(_code);
  }

  close() {
    _codeStreamController.close();
    _messageStreamController.close();
    for (var element in _streamControllers) {
      element.close();
    }
    _eventController.close();
  }
}

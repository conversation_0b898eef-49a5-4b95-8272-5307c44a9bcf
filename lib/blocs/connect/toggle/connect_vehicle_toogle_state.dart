import 'package:equatable/equatable.dart';

class ConnectVehicleToogleState extends Equatable {
  final bool isSwitchRight;
  const ConnectVehicleToogleState({this.isSwitchRight = false});

  @override
  List<Object> get props => [isSwitchRight];

  ConnectVehicleToogleState copyWith({bool? isSwitchRight}) {
    return ConnectVehicleToogleState(
        isSwitchRight: isSwitchRight ?? this.isSwitchRight);
  }
}

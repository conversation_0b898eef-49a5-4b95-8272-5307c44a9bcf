import 'package:bloc/bloc.dart';
import 'package:nds_app/blocs/connect/toggle/connect_vehicle_toogle_state.dart';
import 'package:nds_app/blocs/vehicle/toggle/vehicle_toogle_event.dart';

class ConnectVehicleToogleBloc
    extends Bloc<ConnectVehicleToogleEvent, ConnectVehicleToogleState> {
  ConnectVehicleToogleBloc() : super(const ConnectVehicleToogleState()) {
    on<SwitchEvent>(_changeSwitchButton);
  }
  Future<void> _changeSwitchButton(
      SwitchEvent event, Emitter<ConnectVehicleToogleState> emit) async {
    emit(state.copyWith(isSwitchRight: !state.isSwitchRight));
  }
}

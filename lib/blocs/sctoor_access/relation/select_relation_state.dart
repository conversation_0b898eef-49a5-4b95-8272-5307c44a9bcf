import 'package:equatable/equatable.dart';
import 'package:nds_app/models/enums/relation.dart';

class SelectRelationState extends Equatable {
  final Relation relation;
  const SelectRelationState({this.relation = Relation.family});

  @override
  List<Object> get props => [relation];

  SelectRelationState copyWith({Relation? relation}) {
    return SelectRelationState(relation: relation ?? this.relation);
  }
}

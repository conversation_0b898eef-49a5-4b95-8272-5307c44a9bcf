import 'package:bloc/bloc.dart';
import 'package:nds_app/blocs/sctoor_access/relation/select_relation_event.dart';
import 'package:nds_app/blocs/sctoor_access/relation/select_relation_state.dart';

class SelectRelationBloc
    extends Bloc<SelectRelationEvent, SelectRelationState> {
  SelectRelationBloc() : super(const SelectRelationState()) {
    on<SelectRelationEvent>(_changeRelation);
  }
  void _changeRelation(
      SelectRelationEvent event, Emitter<SelectRelationState> emit) async {
    emit(state.copyWith(relation: event.relation));
  }
}

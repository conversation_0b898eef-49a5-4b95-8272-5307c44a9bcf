import 'package:equatable/equatable.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/rider.dart'; // Assuming you have a Rider model

class RiderFetchState extends Equatable {
  final ApiStatus apiStatus;
  final List<Rider> authorizedRiders;
  final List<Rider> verificationPendingRiders;
  final String message;

  // Initial static values for authorized and verification pending riders
  static const List<Map<String, dynamic>> initialRidersData = [];

  RiderFetchState({
    this.apiStatus = ApiStatus.loading,
    List<Rider>? authorizedRiders,
    List<Rider>? verificationPendingRiders,
    this.message = '',
  })  : authorizedRiders = authorizedRiders ?? [],
        verificationPendingRiders = verificationPendingRiders ?? [];

  // Copy method to create a new state with changes
  RiderFetchState copyWith({
    ApiStatus? apiStatus,
    List<Rider>? authorizedRiders,
    List<Rider>? verificationPendingRiders,
    String? message,
  }) {
    return RiderFetchState(
      apiStatus: apiStatus ?? this.apiStatus,
      authorizedRiders: authorizedRiders ?? this.authorizedRiders,
      verificationPendingRiders:
          verificationPendingRiders ?? this.verificationPendingRiders,
      message: message ?? this.message,
    );
  }

  @override
  List<Object?> get props => [
        apiStatus,
        authorizedRiders,
        verificationPendingRiders,
        message,
      ];
}

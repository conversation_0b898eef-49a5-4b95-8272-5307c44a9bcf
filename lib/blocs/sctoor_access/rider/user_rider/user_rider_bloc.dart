import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_rider/user_rider_event.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_rider/user_rider_state.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/repository/user_rider_repository.dart';

class UserRiderBloc extends Bloc<UserRiderEvent, RiderFetchState> {
  final UserRiderRepository riderRepository;

  UserRiderBloc({UserRiderRepository? riderRepository})
      : riderRepository = riderRepository ?? UserRiderRepository(),
        super(RiderFetchState()) {
    on<LoadUserRidersEvent>(fetchRidersData);
    on<AddOrUpdateUserRiderEvent>(addOrUpdateRider);
  }
  // Fetch riders data
  void fetchRidersData(
      LoadUserRidersEvent event, Emitter<RiderFetchState> emit) async {
    emit(state.copyWith(
      apiStatus: ApiStatus.loading,
      message: 'Loading riders...',
    ));

    try {
      final riders = await riderRepository.fetchRiders();
      emit(state.copyWith(
        apiStatus: ApiStatus.success,
        authorizedRiders: riders['authorizedRiders'],
        verificationPendingRiders: riders['verificationPendingRiders'],
        message: 'Riders loaded successfully.',
      ));
    } catch (error) {
      emit(state.copyWith(
        apiStatus: ApiStatus.failure,
        message: error.toString(),
      ));
    }
  }

  void addOrUpdateRider(
      AddOrUpdateUserRiderEvent event, Emitter<RiderFetchState> emit) async {
    if (!event.riderBody.isEdit) {
      emit(state.copyWith(
        apiStatus: ApiStatus.loading,
        message: 'Adding rider...',
      ));
    }

    try {
      // Call the API to add the rider
      await riderRepository.addRider(event.riderBody);

      add(LoadUserRidersEvent());

      emit(state.copyWith(
        apiStatus: ApiStatus.success,
        message: 'Rider added successfully.',
      ));
    } catch (error) {
      emit(state.copyWith(
        apiStatus: ApiStatus.failure,
        message: error.toString(),
      ));
    }
  }
}

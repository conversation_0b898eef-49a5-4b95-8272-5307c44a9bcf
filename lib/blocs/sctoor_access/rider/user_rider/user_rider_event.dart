import 'package:equatable/equatable.dart';
import 'package:nds_app/models/add_rider_body.dart';

abstract class UserRiderEvent {}

class LoadUserRidersEvent extends UserRiderEvent with EquatableMixin {
  LoadUserRidersEvent();

  @override
  List<Object?> get props => [];
}

class AddOrUpdateUserRiderEvent extends UserRiderEvent with EquatableMixin {
  final VehicleRiderBody riderBody;

  AddOrUpdateUserRiderEvent(this.riderBody);

  @override
  List<Object?> get props => [riderBody];
}

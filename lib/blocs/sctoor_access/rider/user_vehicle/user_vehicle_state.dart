import 'package:equatable/equatable.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/rider.dart';

class UserVehicleFetchState extends Equatable {
  final ApiStatus apiStatus;
  final List<Rider> riders;
  final String message;
  final String? selectedRegNo;

  UserVehicleFetchState({
    this.apiStatus = ApiStatus.loading,
    List<Rider>? riders,
    this.message = '',
    this.selectedRegNo = '',
  }) : riders = riders ?? [];

  UserVehicleFetchState copyWith(
      {ApiStatus? apiStatus,
      List<Rider>? riders,
      String? message,
      String? selectedRegNo}) {
    return UserVehicleFetchState(
        apiStatus: apiStatus ?? this.apiStatus,
        riders: riders ?? this.riders,
        message: message ?? this.message,
        selectedRegNo: selectedRegNo);
  }

  @override
  List<Object?> get props => [apiStatus, riders, message, selectedRegNo];
}

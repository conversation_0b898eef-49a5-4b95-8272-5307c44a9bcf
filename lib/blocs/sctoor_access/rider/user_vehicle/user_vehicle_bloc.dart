import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_event.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_state.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/repository/user_vehicle_repository.dart';

class UserVehicleBloc extends Bloc<UserVehicleEvent, UserVehicleFetchState> {
  final UserVehicleRepository userVehicleRepository;

  UserVehicleBloc({UserVehicleRepository? riderRepository})
      : userVehicleRepository = riderRepository ?? UserVehicleRepository(),
        super(UserVehicleFetchState()) {
    on<LoadUserVehicleEvent>(_fetchRiders);
    on<ConnectUserVehicleEvent>(_connectRider);
    on<DisconnectUserVehicleEvent>(_disconnectRider);
    on<AcceptVehicleInviteEvent>(_acceptInvite);
    on<SelectRiderEvent>(_selectRider);
  }

  Future<void> _fetchRiders(
      LoadUserVehicleEvent event, Emitter<UserVehicleFetchState> emit) async {
    emit(state.copyWith(
        apiStatus: ApiStatus.loading, message: 'Loading riders...'));

    try {
      final riders = await userVehicleRepository.fetchUserVehicles();
      emit(state.copyWith(
        apiStatus: ApiStatus.success,
        riders: riders,
        message: 'Riders loaded successfully.',
        selectedRegNo: null,
      ));
    } catch (error) {
      emit(state.copyWith(
        apiStatus: ApiStatus.failure,
        message: 'Failed to load riders: ${error.toString()}',
      ));
    }
  }

  Future<void> _connectRider(ConnectUserVehicleEvent event,
      Emitter<UserVehicleFetchState> emit) async {
    emit(state.copyWith(
        apiStatus: ApiStatus.loading, message: 'Connecting rider...'));

    try {
      await userVehicleRepository.connectRider(event.riderId);
      emit(state.copyWith(
        apiStatus: ApiStatus.success,
        message: 'Rider connected successfully.',
      ));
    } catch (error) {
      emit(state.copyWith(
        apiStatus: ApiStatus.failure,
        message: 'Failed to connect rider: ${error.toString()}',
      ));
    }
  }

  Future<void> _disconnectRider(DisconnectUserVehicleEvent event,
      Emitter<UserVehicleFetchState> emit) async {
    emit(state.copyWith(
        apiStatus: ApiStatus.loading, message: 'Disconnecting rider...'));

    try {
      await userVehicleRepository.disconnectRider(event.riderId);
      emit(state.copyWith(
        apiStatus: ApiStatus.success,
        message: 'Rider disconnected successfully.',
      ));
    } catch (error) {
      emit(state.copyWith(
        apiStatus: ApiStatus.failure,
        message: 'Failed to disconnect rider: ${error.toString()}',
      ));
    }
  }

  Future<void> _acceptInvite(AcceptVehicleInviteEvent event,
      Emitter<UserVehicleFetchState> emit) async {
    emit(state.copyWith(
        apiStatus: ApiStatus.loading,
        message: 'verifing user for vehicle ...'));

    try {
      await userVehicleRepository.acceptInvite(
          event.regNo, event.verificationStatus);

      add(LoadUserVehicleEvent());

      emit(state.copyWith(
        apiStatus: ApiStatus.success,
        message: 'you are accepted vehicle invitation',
      ));
    } catch (error) {
      emit(state.copyWith(
        apiStatus: ApiStatus.failure,
        message: 'Failed to connect rider: ${error.toString()}',
      ));
    }
  }

  Future<void> _selectRider(
      SelectRiderEvent event, Emitter<UserVehicleFetchState> emit) async {
    emit(state.copyWith(selectedRegNo: event.regNo));
  }
}

import 'package:equatable/equatable.dart';
import 'package:nds_app/models/enums/verification_status.dart';

abstract class UserVehicleEvent {}

class LoadUserVehicleEvent extends UserVehicleEvent with EquatableMixin {
  @override
  List<Object?> get props => [];
}

class ConnectUserVehicleEvent extends UserVehicleEvent with EquatableMixin {
  final String riderId;

  ConnectUserVehicleEvent(this.riderId);

  @override
  List<Object?> get props => [riderId];
}

class DisconnectUserVehicleEvent extends UserVehicleEvent with EquatableMixin {
  final String riderId;

  DisconnectUserVehicleEvent(this.riderId);

  @override
  List<Object?> get props => [riderId];
}

class AcceptVehicleInviteEvent extends UserVehicleEvent with EquatableMixin {
  final String regNo;
  final VerificationStatus verificationStatus;

  AcceptVehicleInviteEvent(this.regNo, this.verificationStatus);

  @override
  List<Object?> get props => [regNo, verificationStatus];
}

class SelectRiderEvent extends UserVehicleEvent with EquatableMixin {
  final String? regNo;

  SelectRiderEvent(this.regNo);

  @override
  List<Object?> get props => [regNo];
}

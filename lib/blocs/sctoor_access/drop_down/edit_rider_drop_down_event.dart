import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class EditRiderDropDownEvent extends Equatable {
  final OverlayEntry? overlayEntry;
  const EditRiderDropDownEvent({required this.overlayEntry});

  @override
  List<Object?> get props => [overlayEntry];
}

class CloseEditRiderDropDownEvent extends EditRiderDropDownEvent {
  const CloseEditRiderDropDownEvent({required super.overlayEntry});

  @override
  List<Object?> get props => [overlayEntry];
}

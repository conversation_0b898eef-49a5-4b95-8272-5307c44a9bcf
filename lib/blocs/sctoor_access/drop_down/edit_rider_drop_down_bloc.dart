import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_event.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_state.dart';

class EditRiderDropDownBloc
    extends Bloc<EditRiderDropDownEvent, EditRiderDropDownState> {
  EditRiderDropDownBloc() : super(const EditRiderDropDownState()) {
    on<EditRiderDropDownEvent>(_changeDropDownList);
    on<CloseEditRiderDropDownEvent>(_closeDropDown);
  }
  void _changeDropDownList(EditRiderDropDownEvent event,
      Emitter<EditRiderDropDownState> emit) async {
    emit(state.copyWith(overlayEntry: event.overlayEntry));
  }

  void _closeDropDown(CloseEditRiderDropDownEvent event,
      Emitter<EditRiderDropDownState> emit) async {
    OverlayEntry? entry = state.overlayEntry;

    if (entry != null) {
      entry.remove();
      entry = null;
      emit(state.copyWith(overlayEntry: null));
    }
  }
}

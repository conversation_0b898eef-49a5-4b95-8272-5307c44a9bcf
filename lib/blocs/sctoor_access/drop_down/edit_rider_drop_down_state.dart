import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class EditRiderDropDownState extends Equatable {
  final OverlayEntry? overlayEntry;
  const EditRiderDropDownState({this.overlayEntry});

  @override
  List<Object?> get props => [overlayEntry];

  EditRiderDropDownState copyWith({OverlayEntry? overlayEntry}) {
    return EditRiderDropDownState(overlayEntry: overlayEntry);
  }
}

name: Deploy Build To Slack Channel

on:
  repository_dispatch:
    types: [deploy-to-slack-channel]
  push:
    branches:
      - ci-cd-fix

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      BRANCH_NAME: ${{ github.event.client_payload.branch || github.ref_name }}
      COMPANY: ${{ github.event.client_payload.company }}
      ENVIRONMENT: ${{ github.event.client_payload.env }}
      ORGANISATION_ID: ${{ github.event.client_payload.org_id }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.client_payload.branch }}
          fetch-depth: 1

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: "21"

      - name: Install Android SDK components
        run: |
          echo "sdk.dir=/usr/local/lib/android/sdk" > android/local.properties
          echo "y" | /usr/local/lib/android/sdk/cmdline-tools/latest/bin/sdkmanager \
            "platforms;android-33" \
            "ndk;27.0.12077973" \
            "cmake;3.22.1" \
            --sdk_root=/usr/local/lib/android/sdk

      - name: Get Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.29.0"
          channel: stable

      - name: Configure for performance
        run: |
          mkdir -p android/
          cat > android/gradle.properties << 'EOL'
          org.gradle.jvmargs=-Xmx8g -XX:MaxMetaspaceSize=3g -XX:+UseParallelGC -Dfile.encoding=UTF-8
          org.gradle.daemon=false
          org.gradle.parallel=true
          org.gradle.workers.max=4
          android.useAndroidX=true
          android.enableJetifier=true
          EOL

      - name: Prepare app
        run: |
          flutter pub get
          dart run lib/branding/app_name.dart ${{ env.COMPANY }}
          flutter pub run flutter_launcher_icons:main -f lib/branding/${{ env.COMPANY }}.yaml

      - name: Build targeted APK
        run: |
          export JAVA_TOOL_OPTIONS="-Xmx8g -XX:MaxMetaspaceSize=3g -XX:+UseParallelGC"
          flutter build apk --release \
            --target-platform android-arm64 \
            --dart-define=company=${{ env.COMPANY }} \
            --dart-define=env=${{ env.ENVIRONMENT }} \
            --dart-define=org_id=${{ env.ORGANISATION_ID }}

      - name: Upload to Slack
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
          SLACK_CHANNEL: ev-app-int
          APK_PATH: ./build/app/outputs/flutter-apk/app-release.apk
          MESSAGE: "App build for ${{ env.COMPANY }} (${{ env.ENVIRONMENT }}) from branch ${{ env.BRANCH_NAME }}"
        run: |
          curl -F file=@"$APK_PATH" \
               -F "initial_comment=$MESSAGE" \
               -F channels=$SLACK_CHANNEL \
               -H "Authorization: Bearer $SLACK_BOT_TOKEN" \
               https://slack.com/api/files.upload
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>NDS ProRide</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>www.ndsecomotors.com</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>NDS ProRide</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Our app uses bluetooth to find, connect and transfer data between different devices</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>The app uses bluetooth to find, connect and transfer data between different devices</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We request location access to help you track your vehicle's distance, with an option to disable tracking if preferred.v</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>We request location access to help you track your vehicle's distance, with an option to disable tracking if preferred.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We request location access to help you track your vehicle's distance, with an option to disable tracking if preferred.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need access to your photo library to allow you to select and upload photos.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>This app needs camera access to scan QR codes</string>
	<key>FLTEnableImpeller</key>
	<false/>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<true/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
  		<string>whatsapp</string>
   		<string>https</string>
	</array>
	<key>LSApplicationQueriesSchemes</key>
    <array>
        <string>https</string>
    </array>
</dict>
</plist>

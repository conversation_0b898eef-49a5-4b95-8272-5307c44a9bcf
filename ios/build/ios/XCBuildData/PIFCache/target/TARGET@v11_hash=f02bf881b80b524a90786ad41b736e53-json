{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bb49cd9b1d1fccb58af528ddab29f719", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f292fa1a2e602e72cbae03ca4381d0ef", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986a3b710db6ffde736d901a8822f98e1d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981c2c7bb83ef85df2b5738875e12a3b9e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986a3b710db6ffde736d901a8822f98e1d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9802786e96202a989faf8737401d76d959", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f9b13b2da24721306fed72ae4ec2d3f5", "guid": "bfdfe7dc352907fc980b868725387e98e37596068807202609d79cb2ba55aed5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de02994d37c964fd34fd507b76f543b5", "guid": "bfdfe7dc352907fc980b868725387e9812a9fa87849cc9179de6f34a281f65ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ca9f2a86a8d4dba2e0db7840d7eb07f", "guid": "bfdfe7dc352907fc980b868725387e9866049a69f03173caf3a2bcb8b35f0887"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983437a5e5b6cc3cb149b89c0d7395e241", "guid": "bfdfe7dc352907fc980b868725387e980a59644c1167bd898d05200f7aca08f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ea4a7b3c910805f3b6bd99991c35adf", "guid": "bfdfe7dc352907fc980b868725387e98be52d8f2ffcfae0d762fd5feab2d5d84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860f68b4815d21f4b5a7d808bfd8fcf10", "guid": "bfdfe7dc352907fc980b868725387e985a9f6e28322b0011fed0412a48945bcc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fb3b856a310df7f24fb9d6eb1b02b31", "guid": "bfdfe7dc352907fc980b868725387e9822c1005cc542fc46f7f099e8b03ed57a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0ca335e07239a0a71bf45537775e7d3", "guid": "bfdfe7dc352907fc980b868725387e984ed8728f9b8064dbea0e1431c61b4c12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea51291ee8c62637d919da841904e970", "guid": "bfdfe7dc352907fc980b868725387e9837bae2330eb456d85ca04c8faf421cd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1a47154e56c3eb5208034edeff7d4ca", "guid": "bfdfe7dc352907fc980b868725387e9894578d0224fe212455df9c778c45153f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d6695ebb140361f11980e70fb51a8df", "guid": "bfdfe7dc352907fc980b868725387e98cb9e14a44852ae0982578005eb3e5c47", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc0ddc54bb6135a13dd10a74dc6ebef6", "guid": "bfdfe7dc352907fc980b868725387e9824980f278abefcda9b6890da499826dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98939cf41a96ae26d471e5a542ac7321a9", "guid": "bfdfe7dc352907fc980b868725387e982685571389870ef8daf2d32b41116fe2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7737d117bd72bd6c9f97ddec6fcb085", "guid": "bfdfe7dc352907fc980b868725387e98269e2841d3430844460c563ae5e01b6b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b9732a048f02ac03affb4b4fa7b710bf", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988d9909f7380d3e8e8319d3f9d78985dd", "guid": "bfdfe7dc352907fc980b868725387e98db6db7db22361c5e7722392e4da662e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982586df527ea78ab29b1fad4d10e29d03", "guid": "bfdfe7dc352907fc980b868725387e981e9730263a56b94e053b6bb20e9bd70f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98895b4caefdd6fd4d5b60572872d35bbd", "guid": "bfdfe7dc352907fc980b868725387e98f35a912481b88e47986aa5bac1355527"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876ed0e4d641d7887b318dea08916b7c2", "guid": "bfdfe7dc352907fc980b868725387e989cae3e928435594e3e5d30368fe669a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa1a97206c5d9a906904b6338d671d67", "guid": "bfdfe7dc352907fc980b868725387e98b309caf61ed84ca13ea160126583272c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e24b371ede11f1834936cc01a09de3e", "guid": "bfdfe7dc352907fc980b868725387e98c10f66b3dff0dc0f8ebe2401c34d9eac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872b102d8a484db30f567ad4fb5852369", "guid": "bfdfe7dc352907fc980b868725387e98015ab1ee2d0583cec2879f88f8a0efa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98119b3bb07d321e02d4b0df751c810bf3", "guid": "bfdfe7dc352907fc980b868725387e986233691fc832178385693d5af8e125bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982021c0c472d71f7dd439ebe5bf3921b5", "guid": "bfdfe7dc352907fc980b868725387e98c9989002d87adf886213685d3570c30a"}], "guid": "bfdfe7dc352907fc980b868725387e98964da8c41b24636d9d338de21fb08fe2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e98015c062afdb30ba698f98b6ea63a1d36"}], "guid": "bfdfe7dc352907fc980b868725387e9856f337a0270b8ff9a09062fba0a9859f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98301a0f773483e7a62c7a59d2ce137d3c", "targetReference": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8"}], "guid": "bfdfe7dc352907fc980b868725387e9877cd28b09d57fbb5f9abe0ec343ccf66", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8", "name": "sqflite_darwin-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e981304d3d2169071b3ca365b19f5340b7c", "name": "sqflite_darwin", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98dbbec3eebed26c79cc653713be723aba", "name": "sqflite_darwin.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9844ebcd2486ca4b8ec4c452091309f9f3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98848747cdea01136495a2f6291b19c178", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dc2a708dc1e2ee2b876c0db9584b4df2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988108cc3f5ede479af3880bbc9aafc0d7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dc2a708dc1e2ee2b876c0db9584b4df2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98df4ce3e756bc84c7c8656d78f5859a67", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c3b36c2732b1415f913cb6eb03085de3", "guid": "bfdfe7dc352907fc980b868725387e98ab3f20ef75be17d887344492a1197e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d404619501a44cd2dff6cbc5dd51ad9", "guid": "bfdfe7dc352907fc980b868725387e984d23464094c6cc2aba2ac2591a66e894"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805c273448dc61edaf8c41c9305e629d5", "guid": "bfdfe7dc352907fc980b868725387e98ea9e79eceafca318b4d057d9486ee1cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e03fe145ac87193cd49f17fee58c0ac", "guid": "bfdfe7dc352907fc980b868725387e989cdd7bd633f9b8430898735b887cea75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825b46db8fb662998737421f4cfd8166a", "guid": "bfdfe7dc352907fc980b868725387e98f09c9d2d0ff6f967ca65145dd621c6a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a30207c45b78f9971542e59cd583079", "guid": "bfdfe7dc352907fc980b868725387e98317d85decc6dbd40dfbb2f853dcbdf59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987501f1da80e3b351630913157827d253", "guid": "bfdfe7dc352907fc980b868725387e98ccadcc4e0520e5e00a8fdc3da6f12129"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed48476444c17454e870e7d622bb1906", "guid": "bfdfe7dc352907fc980b868725387e9833355508b227cad66af003beb4adb493"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3e0f5f669939ebb7f42fca3a25e1da5", "guid": "bfdfe7dc352907fc980b868725387e980088d61e4de30614a0602dfeceb9e469"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b5a4e76e3ab5d9767ff15655767a575", "guid": "bfdfe7dc352907fc980b868725387e98950a2b1d393ca140947b78002edc5ee6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826989922a9cd669a70375d361dddbb29", "guid": "bfdfe7dc352907fc980b868725387e9821029ff446538ac9e196b0a9957d0d9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e22dbed7f4b2642f89c99b09e6e2af4e", "guid": "bfdfe7dc352907fc980b868725387e987da940cc06be102ce550602d5b770ddb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987acede1e736d09af8b4a27937c0700b7", "guid": "bfdfe7dc352907fc980b868725387e9887ea2561d9b0e2a9652bf0c11c8e384e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888fb1ed9dada6ac9535ae02d7e3faa5c", "guid": "bfdfe7dc352907fc980b868725387e98f7ece129de5e2e217f6d9162375f3238"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e3fdae75d97a62a581355a3e6bd5292", "guid": "bfdfe7dc352907fc980b868725387e9847eba0417a0c940871aa3dabc40eb195"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833b1857feb13b110d89cac7ab0c67b7e", "guid": "bfdfe7dc352907fc980b868725387e9822e3289a99979c858e8d79920003a872"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98323435bec20219b7d571d4dd13d84603", "guid": "bfdfe7dc352907fc980b868725387e984500819304cf10c0aa8bd9043dfa2749"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886bc52dd096a700afe6c90ebec1b916c", "guid": "bfdfe7dc352907fc980b868725387e9854953c8155e9873df9ef493ca8e9b60a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7f3436de4fd3a5f0f18ab65645d9099", "guid": "bfdfe7dc352907fc980b868725387e98793a3ede9bb77b7fce0baa4fbeace8e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e5278e2dc4ecc9870bda84e8b2f2f3c", "guid": "bfdfe7dc352907fc980b868725387e98d16d591376e25b7746001cb2ee30a049"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e50c93c102833f017b5bb7cbdb7edf1", "guid": "bfdfe7dc352907fc980b868725387e988fba20036e4d2b82f3e6c4316bb59232"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a8748f1ae99906d3fbe084b48793bec", "guid": "bfdfe7dc352907fc980b868725387e9828e6a441986eb505078f79222a2f286c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b74617a8c89a8e8c7d488f5d9baa1d7", "guid": "bfdfe7dc352907fc980b868725387e983c0c04475221e72c4496b48f2ea67fd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894d4ba8942782fb9f4a51513bf874207", "guid": "bfdfe7dc352907fc980b868725387e98f448b0706bad43afd24c408bc5e03200"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b5a92f38dcbb7cad337eb6b56489dea", "guid": "bfdfe7dc352907fc980b868725387e98e8e28cb851d971c42e145064dc587128"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9f6d57180ebbe4c087d55bee77a2d15", "guid": "bfdfe7dc352907fc980b868725387e98081ce48c31b2c41e67680d927cccc40d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d177b0b57d0e311638be07d971620af", "guid": "bfdfe7dc352907fc980b868725387e98a28c91e9c638e749025cc68a68de3ee1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2829eaea894a190f53b4eb359e0814b", "guid": "bfdfe7dc352907fc980b868725387e98fab2987ad1caf5c290fcfcc3e55e434f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d9b86c26be04403e80b71de37fa6478", "guid": "bfdfe7dc352907fc980b868725387e98cc3ee5041ee2a796f12397caa9045464"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b90b5fbbaebdf38739364304bb8fc03", "guid": "bfdfe7dc352907fc980b868725387e988fa104ab8121c5632ef72c81ecdfa139"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dae1fec042b7b14f90239b0aadc69b90", "guid": "bfdfe7dc352907fc980b868725387e982daf882faa22579eae18e1b4561ba48a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f2cd9b238042db1f1b2922bb3c1b214", "guid": "bfdfe7dc352907fc980b868725387e988294258095618aa5762857c8b894827a"}], "guid": "bfdfe7dc352907fc980b868725387e98f31fdcbfb02fca0248686a73a60e2dd1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c0da1424b5cf98df3de7dc27238e3211", "guid": "bfdfe7dc352907fc980b868725387e98df722629764a9acf819885051d04c7ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0305b5506d29f18f009d96a54b0fb73", "guid": "bfdfe7dc352907fc980b868725387e981b311f156f1f179eca4d54b0c7d79bb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ba6dd98714cf1af785834bc10f963c8", "guid": "bfdfe7dc352907fc980b868725387e98072f91dc74b8ba83db1833bf2faff845"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec5359b55ede605f4810ef90e5f1ef7d", "guid": "bfdfe7dc352907fc980b868725387e98737563be94731a5960d4b45263ec9244"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98626891244d540cbd46f8d17ed730f902", "guid": "bfdfe7dc352907fc980b868725387e98f2d18b12a92c44ddb4595ed497f64e08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845fdc164951001a3735cab5c0f3bca5b", "guid": "bfdfe7dc352907fc980b868725387e98f17ded400ec489cfd84ef40be6503ff1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846960695ec13c8795ffc0c52e424e776", "guid": "bfdfe7dc352907fc980b868725387e98298082399cfd728700e4189675ed0ccb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98083f76169cc2696f01fb3860874c99d0", "guid": "bfdfe7dc352907fc980b868725387e9833ca452282933f95f2d0028facf479f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98506c10247e8e32d90e183ecb3bf02a96", "guid": "bfdfe7dc352907fc980b868725387e98f0ae49e0e60ade927d14902246c2035b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98057092a1567e056b6cdf8b4bc9b9fea8", "guid": "bfdfe7dc352907fc980b868725387e98eb0cb5b6fb903a9532af1eaa3faf0250"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fed991f623f3410aa18cecfbcff4c32a", "guid": "bfdfe7dc352907fc980b868725387e98a367a8c60d208adda417e8068df3fe3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987173f3cb69afad746281265e44072df5", "guid": "bfdfe7dc352907fc980b868725387e98356e2df38b65faf239f4b7dbfee204a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98031d0d78ca05204668f94b7303c5db85", "guid": "bfdfe7dc352907fc980b868725387e981df9f982005d08f986b88ca97f8ec3d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987edca659dc9f5e97cc88337993505544", "guid": "bfdfe7dc352907fc980b868725387e981608206e7b66bb5cf6847c6fa2b7b2e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a19fcd65e18f0c0c05b1f7007b7c2d10", "guid": "bfdfe7dc352907fc980b868725387e98ce5c289b9ee025948f166d96357e7e63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98543651556693cdcebfb5d5b194a1a46b", "guid": "bfdfe7dc352907fc980b868725387e98c03913306beb0c27b862b921cfffb5a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e2af382b4eddd41d41665fbc8c4a349", "guid": "bfdfe7dc352907fc980b868725387e9833fa3511707a328949c5f6da4e056521"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad70eff4f9fac85b3db61a0fb4988f9c", "guid": "bfdfe7dc352907fc980b868725387e98a742ac5e9de1fb8ba9efe4549eda66a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cae8e52ff7d914740aeeabffa42f883", "guid": "bfdfe7dc352907fc980b868725387e98b657a429318658d0e365582aeaa7c228"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d49254f7ee3dcbde4328f33c622137b", "guid": "bfdfe7dc352907fc980b868725387e9806ff658098a0de63b4b6fd4db96d9941"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988792d7863f14cf5e653095cdce9112d8", "guid": "bfdfe7dc352907fc980b868725387e988cb7fcdee967f91d45c1b02a725fc95e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866d992494fe5e8df765715978d729a99", "guid": "bfdfe7dc352907fc980b868725387e981947aa1e5e43e2f40b1a0693bbb31839"}], "guid": "bfdfe7dc352907fc980b868725387e986251051ce9969034401483383a21ff76", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e98a7c1811f56b2dae8df02bb1b3dbf5463"}], "guid": "bfdfe7dc352907fc980b868725387e9833b01e841be90bcc97790488ec3507b0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c895d6bfac89e4437d7aa92f9aa2391a", "targetReference": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca"}], "guid": "bfdfe7dc352907fc980b868725387e98d2f4638b907b93e67bb0e206d7905868", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca", "name": "FirebaseRemoteConfig-FirebaseRemoteConfig_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e984b1e8e5f67fa144e5e34058df6e2f50c", "name": "FirebaseRemoteConfigInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98928855ae8620d13300183deed96c33a1", "name": "FirebaseRemoteConfig", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980b80126605cba44506bfa90fbbd69742", "name": "FirebaseRemoteConfig.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b979c8a57a5be4799d6fa24e21ee4e2d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c632df4d68722208f394a75b06e8ad17", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c29b351e6657a369b90e039a2b4161ed", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983a44aa7028a6030fd134fdc90e02ab68", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c29b351e6657a369b90e039a2b4161ed", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986e3f07f05d03ca1c4a8a9c78a26376a9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984f48e94c2dd1ff32b8cf32e933e203ed", "guid": "bfdfe7dc352907fc980b868725387e986da6b37b9cbc730c06ca16d165f6248e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c4b59506321718fe5d2f4eecda918686", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fb1b9e5d684b66d57bcb285aa25d5e9b", "guid": "bfdfe7dc352907fc980b868725387e980fadd8cba80479875c8eb0f14ceacb75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9e8e794a2a8ea90ab23086e052066b7", "guid": "bfdfe7dc352907fc980b868725387e984cc2d15b502be750284a4be98e9922a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814527f8781199dcc90c6bbfc355f5fa4", "guid": "bfdfe7dc352907fc980b868725387e9807b0b0247e0d1a4bfadef87e75c2fed7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bbce9136e0908b4b80f89669a2a076d", "guid": "bfdfe7dc352907fc980b868725387e98afa9d2c378837aa1b0ee237dbfb52581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adf318d68d7c0c9a5ef262478073fe54", "guid": "bfdfe7dc352907fc980b868725387e98bc2a79705774a6c331a96bca34a9f57e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5052d70f9ddf522d8addf9339448c55", "guid": "bfdfe7dc352907fc980b868725387e987757ca23f9d56cfe3d7ee03f459cb097"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860344ed4e516f1735bb5c90861413829", "guid": "bfdfe7dc352907fc980b868725387e98fa5968e2ad13e8fbd3b5a5f8346442fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bba0fe97f7b02821aa78e4d0a18e8521", "guid": "bfdfe7dc352907fc980b868725387e98f0098443a9c96d6a3bdcbcdb87d02524"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd2b6ddce2bcd8e4ef343ebdaff82ee8", "guid": "bfdfe7dc352907fc980b868725387e982831da109ddcd993c9668cf305fdcda9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f0cf5b9a97510ff6afb5d7a2fe9bc22", "guid": "bfdfe7dc352907fc980b868725387e983da92417ac5a3885d50fb2803d798ab2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818b9ef565e5cfff6f100de21220fc857", "guid": "bfdfe7dc352907fc980b868725387e98c6c57d340be4261cfbce69cce0c569d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828412527bfc4ad3f8c828defe49bc3bc", "guid": "bfdfe7dc352907fc980b868725387e98cbaeba17319857b90c8c11c5e32339f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f2eb2adb1af98f24c80c98fdacfbadf", "guid": "bfdfe7dc352907fc980b868725387e982442c516b6de6da2738efa2dea596aa5"}], "guid": "bfdfe7dc352907fc980b868725387e98e4b284a4672aa37bf13cbeda676efb32", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e9860e1bbed826abdeaefb99b7e9309c391"}], "guid": "bfdfe7dc352907fc980b868725387e981f7e61cadba2a8913843516c49e90a69", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b9e8efd697ea97715407e7d23d851104", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e9810a7a898fe5cc5d1b066af991dd4e0d9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
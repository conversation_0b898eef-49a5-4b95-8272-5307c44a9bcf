{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bf463cdb59d2b3883903d6dafd49a147", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98abcf740806edf520876510944b931c96", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b0cb901641810e22df8e236ed77382be", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9846c7e21ff56a34c841f6721450e12c9c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b0cb901641810e22df8e236ed77382be", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98580b1a4c19a05311983e2e5adc9cd90b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98774ebcb9dc020fab6f1a96c5803f8e0d", "guid": "bfdfe7dc352907fc980b868725387e98a406099d05a3947658d70bbf22df9d42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981df71b9dbc6556250221f0712f0a7a51", "guid": "bfdfe7dc352907fc980b868725387e98734d90750de3ce5175eb9ae0491181da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869f0d23c56a5b665af5373790506dca0", "guid": "bfdfe7dc352907fc980b868725387e98c4b57467ff78a5761bf249385e59b4ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98044651a79ef66246577f78f37cb64cc3", "guid": "bfdfe7dc352907fc980b868725387e98a8b65930251373abe942a0e2125d24cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dfb24aa3abeb1b14e30812dc4c6f6b1", "guid": "bfdfe7dc352907fc980b868725387e9836e245bd3ffb8c417811c89c73fe3830"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0dcff27213c9e8eff68b068dae1ce67", "guid": "bfdfe7dc352907fc980b868725387e98b8a385d740b9ed91d069ae846ab74d9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dc9dff64f35e4135537ccccbf62c0cf", "guid": "bfdfe7dc352907fc980b868725387e986014ed19a60c8fd846b747307ecfa443"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984104c25062c82bbfb4706acbeaa3c6c5", "guid": "bfdfe7dc352907fc980b868725387e984fbdfdbc4078bc425cf7e2774a37a732"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989eacdf82a2dd4d0286466b1a9b782ad4", "guid": "bfdfe7dc352907fc980b868725387e981129ed8354a3b40333533f809711915e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98272c1d8051d614a988a86028e3a99d37", "guid": "bfdfe7dc352907fc980b868725387e982735bd160f7a47d97fb4608484d34927", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985231b533d04a5ddbe91951bd3ac466b7", "guid": "bfdfe7dc352907fc980b868725387e988e2149c509649ca070479350f42499dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871ea6742ecdc9b6a888cd99a7e109647", "guid": "bfdfe7dc352907fc980b868725387e982fb99e697c96464c02391470e31fed84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839c781567ba4fde200684acd11bbad9d", "guid": "bfdfe7dc352907fc980b868725387e980dea54b8f328898d9c5e26177860ac75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae2787dbd84cc20189d4995f7c5311e1", "guid": "bfdfe7dc352907fc980b868725387e986f3e0ea1738855ab0b919b1be412a575", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fcef143e03982f439625aa2b916766d", "guid": "bfdfe7dc352907fc980b868725387e985746baf5ead0eef567193a311818e628"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98137f5c46a292d15c4b54f6d231e7c03c", "guid": "bfdfe7dc352907fc980b868725387e98b2ef8f71dc6231b91619f3dd24478c64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe1bc04d690f80a668e665d3390a4c9e", "guid": "bfdfe7dc352907fc980b868725387e981654facd2f7c59a4a60be25f00f9902b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98721f5e9602f21d39ca4481aec009f456", "guid": "bfdfe7dc352907fc980b868725387e9834038466fdb86e4244e3826ddaff0a15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a64133fa63e9f205a8e58ec3e38995e", "guid": "bfdfe7dc352907fc980b868725387e98a126aea18b5427c86b2869c098f60602", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bb6a6c3ea6910f87a370261c8ce6ca7", "guid": "bfdfe7dc352907fc980b868725387e98c9c7b79e644270a7ece105d0a48aa8d7"}], "guid": "bfdfe7dc352907fc980b868725387e9832fa67882519d184b8a0657d9d0922de", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7c742ced815f34c72c9dd023c4c068e", "guid": "bfdfe7dc352907fc980b868725387e98d15125b2fec17f9bd6ad5bbe9667d2e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc5db1a6f6eae66fc94e654a4331e11b", "guid": "bfdfe7dc352907fc980b868725387e98803e351fab5dbe13e5673996cf5ad845"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98849b01e45427d8a951db23b4589c3e78", "guid": "bfdfe7dc352907fc980b868725387e98cef6b4d983b174f18f65b4b5d02f0c05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a48995c26d427d569204b2308de53089", "guid": "bfdfe7dc352907fc980b868725387e984d45eda364e15d1888fadd3ba3843151"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b584730a3e46d856c850dfc5f2dfdef2", "guid": "bfdfe7dc352907fc980b868725387e98241f7e13bfd1a16df49b29be7bfb6b79"}], "guid": "bfdfe7dc352907fc980b868725387e98f1cdbaab6c61f0b0605c0b4186d9132c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e98afcaeafbf7560fd13e2fd7cba66bd877"}], "guid": "bfdfe7dc352907fc980b868725387e98032d997e808e9c7746324f5bc43ef7ad", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984e59c0f779895429730c228f5a8b203f", "targetReference": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443"}], "guid": "bfdfe7dc352907fc980b868725387e981643e088e3a19ecca61aac07c5b9574d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443", "name": "FirebaseABTesting-FirebaseABTesting_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}], "guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98388ecc0b6beee3823c42c78ba6025714", "name": "FirebaseABTesting.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98178ca6e56bf0251248d88a697fbadc6e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9850cf5f05781891fa16605db01db1459c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fbf811dbd09201f411b3a59c916f53ef", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986063368a315fa3b85a27706c8c05d255", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fbf811dbd09201f411b3a59c916f53ef", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981292fd0312cd0d285c427c02abb96cc8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9876eb51e407cfe7500f3f7c7f60d57d5b", "guid": "bfdfe7dc352907fc980b868725387e98835a8761629e46af7a67553504354e4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e8b9ae6053d1863be76f66537a60936", "guid": "bfdfe7dc352907fc980b868725387e98e12ded97e1a314dd904e5e1e4541d731", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e082693f7350e4b4c8f47667afc6ac1", "guid": "bfdfe7dc352907fc980b868725387e98cb97140f3b87ec11f586ce317af13ad9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a0c96b6d3f053f8d90531176cc91543", "guid": "bfdfe7dc352907fc980b868725387e981a9d2816610771c9a826e9a711a1924c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f18e35eee4dfd50be45ff028fba5b58e", "guid": "bfdfe7dc352907fc980b868725387e98b7a2fdbcbde45417386aca3e450f3859", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b67225edb20647eae4c8214b01cf329", "guid": "bfdfe7dc352907fc980b868725387e986d39c1e781ed923b79e9e9ffa1779ea1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98974facbf3ebe4598d2651830d75be3f7", "guid": "bfdfe7dc352907fc980b868725387e98587bfa3098791bdaebd112078b8ab404", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891de142b69a6cf7cd5a958cb4f29a790", "guid": "bfdfe7dc352907fc980b868725387e98e34924a1b5c22fdbe2eaa23da73c13c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a921ec9bfcfd7e2d7e37493ad0e3c80", "guid": "bfdfe7dc352907fc980b868725387e986095e29b2d6b61b2eef9e3f2c80331ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884b4c65ccbbe9d8a0647770d6ee833a4", "guid": "bfdfe7dc352907fc980b868725387e98008375be370ac23678632d607afc7707", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ef5f17d0886c01cc77bd8c066c75115", "guid": "bfdfe7dc352907fc980b868725387e98a70ddab3c5d8593339da639abf5c8ca0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f3b0b0e444dd883e413f6d095d77b44", "guid": "bfdfe7dc352907fc980b868725387e986490d9d11a979d02ef6f4973c389d2b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e02c60b98efcc6255528bbc236f13d8", "guid": "bfdfe7dc352907fc980b868725387e98f5fd18d6b5b1826b28c5a2d3f4219cbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5c2e48f29b4aa268c34629476ef7014", "guid": "bfdfe7dc352907fc980b868725387e9884a9bd95c65c391e79223a1a755ef3ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1adc959903c742392f736c986c19d5f", "guid": "bfdfe7dc352907fc980b868725387e981cc016c8fe128b974ff2808f5364cf39", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98634c80ffc3c2ea61abfe11eb8b548ab3", "guid": "bfdfe7dc352907fc980b868725387e98c70984b8018c7c055c8f2fbce3f419b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98476ebc8a914efeccd07f407073472c3d", "guid": "bfdfe7dc352907fc980b868725387e987f230b7417f4c0e033b1f6771dbd7f5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab431ddca760c2cb8d0d9be4a8ade2f4", "guid": "bfdfe7dc352907fc980b868725387e9817195b982c346f37746726aabaeb8a9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857630f66e00a0bac5706295632ec43a4", "guid": "bfdfe7dc352907fc980b868725387e98cba540cd893e7a103cd77211472f3267", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98355078ce20de33295c8eb08a8c11e51e", "guid": "bfdfe7dc352907fc980b868725387e980fbd0af54dce43bdfe7f125d05dc2745", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9869bead6059c1e09ed350037ea708d985", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c23800ff5a0fb7dd1d6d8e612d0c6555", "guid": "bfdfe7dc352907fc980b868725387e9888bd372530c3bbaa2abc34d39398055a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c21d62ce722c59950c598dbfee16e23", "guid": "bfdfe7dc352907fc980b868725387e9864a3919a2d3620b4936ba0cc6812b9e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985027e88293822e84eaf28a80b4eb7ef3", "guid": "bfdfe7dc352907fc980b868725387e98a3a2eec431ef32512a251b5fad8242a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5758c0f38cb4d4af6696ce7a7ad5de0", "guid": "bfdfe7dc352907fc980b868725387e9893f395e52796091eb59fb77ffae60d19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3d97b30c63c99bc28c05121887905e1", "guid": "bfdfe7dc352907fc980b868725387e98155680374455778e71349d4536145380"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d27ec2cc4db02079795da366596eb45", "guid": "bfdfe7dc352907fc980b868725387e984d7bd22ff33d6e7edd5286f461a8855e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98131b1719933f9e0c1ecf89f81d15bfd7", "guid": "bfdfe7dc352907fc980b868725387e986e22f007f4d67a5f8567fae09e868b59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824633158e78823655d7d7b45a052e697", "guid": "bfdfe7dc352907fc980b868725387e98daa8bcef5809f5b373f6fde40869f54d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835f9d9a2ad7a8c015121c971fe6faff2", "guid": "bfdfe7dc352907fc980b868725387e98e82a04e54b286887520559775eb2335c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98122727a393e253c8b240f4358a6c7132", "guid": "bfdfe7dc352907fc980b868725387e9857df2d34a886fd416448fe1a25bdd9d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865a17e4650f6975a7d6106675e04f3dc", "guid": "bfdfe7dc352907fc980b868725387e983e425bd5d54b1ae505afe2f3d2a76610"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98076cd85f777d27dcc90c5536668fd9e7", "guid": "bfdfe7dc352907fc980b868725387e98b2cdc5bd27a478c149d68bfdd017e282"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98950f7ce160f7cbf89fe5eb4b7caa07d2", "guid": "bfdfe7dc352907fc980b868725387e9894026b34b37fea2b4bef5593b4c7a50c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98521d9e760004a447ae0e91e1047aaca2", "guid": "bfdfe7dc352907fc980b868725387e9851ce246bdd54447f95edb5637b945bbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b2abf890f3683dbef86608998ffc2b7", "guid": "bfdfe7dc352907fc980b868725387e98246284a72efd172d7954982d7e37c298"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98078d78585eba3905aba711c5ff3fbe57", "guid": "bfdfe7dc352907fc980b868725387e98e12b1917e1773a6b43f2333b9d2e45e1"}], "guid": "bfdfe7dc352907fc980b868725387e981f382c37ed1a124a8b30a08cc9d8b43a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e9861104480019374d5194221f9451137ec"}], "guid": "bfdfe7dc352907fc980b868725387e98244808205456ebbadf9727d780866f11", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98cafa211e8dcdbfef0ac5c0abec96d32c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
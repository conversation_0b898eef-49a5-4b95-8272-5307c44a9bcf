{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9847e59bca5cd567cc8fdf9f285255b9ec", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986abf7f067f8be56ed91954dedf25377e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ff4494de1236e8f5f041fdddbfbb103d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7741d300a647f0b19b1d962311b03f1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ff4494de1236e8f5f041fdddbfbb103d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fb4308ffc2ddc06f3e072cdb7b56bbf1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f6172b2ec4cee2ff9496d6c23e620f40", "guid": "bfdfe7dc352907fc980b868725387e982c117b25c795f345eb7121cdf23dbc06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa1779645c77bbd913f51fa42cfea24a", "guid": "bfdfe7dc352907fc980b868725387e98e6475bf72be6ad0fe7e03b6911f2d437", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829039922d2829476e30d8eff4a299ba7", "guid": "bfdfe7dc352907fc980b868725387e988be3e4ddc38e0322b921c46071fe1a3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855ddc9f2c027d853050cab2b12896f1f", "guid": "bfdfe7dc352907fc980b868725387e981780e76305ea94e7e26c469ce07968a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dead63d1e254a34c3e482588c178897b", "guid": "bfdfe7dc352907fc980b868725387e989f9887c0a758bd33e5ef941add584450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a31d77bf7bf5e4f9438297508865d632", "guid": "bfdfe7dc352907fc980b868725387e98c4042b81f4bcd16570b7e1a97442287d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98309cef8a517127c2345e94b626b6c461", "guid": "bfdfe7dc352907fc980b868725387e985936ec9432bf924cfb74fc84e8c8c220"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e69cc74c42b24fb84a809dc2b232536f", "guid": "bfdfe7dc352907fc980b868725387e98da03c80c98fc6f9f1a9001c63ca76b1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4a6d44a752310f77f6c639850302368", "guid": "bfdfe7dc352907fc980b868725387e98035fecd8379f76a91f0ab94dec233982", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be5bddc5ad65a0497a121d0574957956", "guid": "bfdfe7dc352907fc980b868725387e98a43fb8ba9f8c2f9aebdd059a2a1bd705"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc483a4f6639634540b18cd2a5ff279b", "guid": "bfdfe7dc352907fc980b868725387e9876112b1dde581dd5b76c9252f3ca998e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c148638f3e0c7045794c2152bf28fac", "guid": "bfdfe7dc352907fc980b868725387e983bb03a40d700a761297800b39b669e6e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98066805ad2e9bf3d003ca6bd204cac3cd", "guid": "bfdfe7dc352907fc980b868725387e98ee1d7d87a17eea0403bfc383412146e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fee25a09ba044da7c9fcaac27bff7bc5", "guid": "bfdfe7dc352907fc980b868725387e98771065698231db8e7e54aeef77257d99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2d43194a715215df84a9d0b448583e6", "guid": "bfdfe7dc352907fc980b868725387e98b969763646a14ad416f4b31da82e0f9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98651467d815401cdb1ff9b486f1a62478", "guid": "bfdfe7dc352907fc980b868725387e98f3b92539baec5be77ec3c43761f89705"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bd9c9af98b6598678b72c56b20a5fbf", "guid": "bfdfe7dc352907fc980b868725387e98dc023cce3f24ecc93c98a80bea2bb2a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ef47619e34296acc69d6c74e65e15ec", "guid": "bfdfe7dc352907fc980b868725387e98e87ba0cacc65b8d679652b4b6482a0f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f8ebb392170a43bc165a15f75a7bf08", "guid": "bfdfe7dc352907fc980b868725387e98ed0bb0c2fcd9475b32d2a4cbad0a5ae6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983907dc743a689f6a6aae7a45a44dc2ca", "guid": "bfdfe7dc352907fc980b868725387e9891725bb7f11f3de9ecc246fa6c8f4394"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98622f7a3baf8cfe2e664557611413a0f8", "guid": "bfdfe7dc352907fc980b868725387e98eae6e0c3ff44523f97d94963dccd5dfe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e135a8693cb019d0b2fac62b485fb734", "guid": "bfdfe7dc352907fc980b868725387e98dee386ee257e4610c925e360a66053c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eea2a89d1638d11f56f765b1ef8cb444", "guid": "bfdfe7dc352907fc980b868725387e98d51bb398fc11199b547a77a943326823", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981a8c9994f8bb3da0b53a57422a91fcc0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cff61f7f6873e1e7582b53022a1d3893", "guid": "bfdfe7dc352907fc980b868725387e987bbb06ba0d71839bfe5f17ae4236ffa3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889ce2198a133d80da99d201c7e4e82f3", "guid": "bfdfe7dc352907fc980b868725387e9895d63a0c00c11015c1a3405486fedf08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988faa9ac2374d8ccf7931543acb5c96b1", "guid": "bfdfe7dc352907fc980b868725387e986f916f7f393836649d535e64639d08bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fdc37a09a46a0424339171486a47ea6", "guid": "bfdfe7dc352907fc980b868725387e98143d3114e18ebc32d84387406a5ee024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc406c15c0d1e3b9de59a8dd491488ec", "guid": "bfdfe7dc352907fc980b868725387e98e3f93c061f9016191e5b10182cae2780"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985faf5f17f878b13aea316ebe6cf1ec08", "guid": "bfdfe7dc352907fc980b868725387e98185cc57feac4179974953554617cd793"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e2fc75d9a5079a5cfc8080a7f7924fc", "guid": "bfdfe7dc352907fc980b868725387e982501a470cfc69e79847fcfd0a8837938"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0564983ea44919e38427be134fc3732", "guid": "bfdfe7dc352907fc980b868725387e98aa72c989fad868d708ee21283bca4e1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d726e4cc4cb4c29ed9d7bd10d98d2d48", "guid": "bfdfe7dc352907fc980b868725387e98e6e9aeaec05dfa323d247a57ab330fe0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846f58aab287273c3d76826384020d186", "guid": "bfdfe7dc352907fc980b868725387e98e7c9e6ccdb18ed9e5a049923cd00282a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98defb94555f4272d1fa6f02a525708adf", "guid": "bfdfe7dc352907fc980b868725387e98abdf53184ad8bdd5a449dce529ee0932"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888488e43f42f8689008bd5a913212bfa", "guid": "bfdfe7dc352907fc980b868725387e98b18b1d0ee4ecc0417835d374e71f81be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9ad35e4395dca3ba9da51a011f358a0", "guid": "bfdfe7dc352907fc980b868725387e9827036a03a5ff3a1bec280a9ea2b44552"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98509ec591e1a4095cc100ced176ebd22e", "guid": "bfdfe7dc352907fc980b868725387e98d31683a95e097777a2f953ebc1a53940"}], "guid": "bfdfe7dc352907fc980b868725387e989f46a7d2b77f69b59035ae785fcb4c38", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e98cf03f6b3e57aa7caf6484cb6cfd26aab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a8637d2caae4f65dfd82cf1c6f281e2", "guid": "bfdfe7dc352907fc980b868725387e986e2ec2b4e9bdd29bdab0844db5b4de2e"}], "guid": "bfdfe7dc352907fc980b868725387e983841f2de22b3246e288451ef2dcd9a72", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e989f665ed07f517dc57903ab4a93344467", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e988177c99c437abb5745e7b6ec98829828", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
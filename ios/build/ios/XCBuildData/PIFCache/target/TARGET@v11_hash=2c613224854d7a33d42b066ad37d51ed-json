{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983d6bbc68cafb8faa0945e568f5346a36", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a34239fec075f2a39ba9d0e83ca84428", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9888b93fa81405c68864dead10f5775060", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ac5e88a536df301cebd533a66e845d0f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9888b93fa81405c68864dead10f5775060", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b48af3fd4482cfca5187ce9ec6b9d580", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989a3fa3ecdc81b119a42fc7a4d150b56c", "guid": "bfdfe7dc352907fc980b868725387e9837ee9011208595f5d39ddf1a106d4942"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a56c5d4ed02a0692dad65dd4234517f7", "guid": "bfdfe7dc352907fc980b868725387e98fab82a147883b727e2d7fd1a1426b9bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c224017de6a8472803c65367cf67ed68", "guid": "bfdfe7dc352907fc980b868725387e98f1deaeadbc484d64b07898ac645e89a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ee4631dc97d80dfb57c9043c0806a1d", "guid": "bfdfe7dc352907fc980b868725387e98f7b1cb1c603d0a956964cdf9d210aa19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab5dc73f0abe72dd499903d8b94ccea2", "guid": "bfdfe7dc352907fc980b868725387e9805981e9dd72ad466e54ad6564bd228d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98223502a8566ae08711faa8e4c54c6683", "guid": "bfdfe7dc352907fc980b868725387e98ac6e4a5e213b55b6cb846050510c03e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dad78c41edbbc2afc1921c11d9ac92f4", "guid": "bfdfe7dc352907fc980b868725387e984d5bbbb1d440245a43bd5440b5f22275", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dc85785bd2503c819f275d1db68b2e8", "guid": "bfdfe7dc352907fc980b868725387e98e632dd4a7646111d9d84d2346d913385", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf0f67a1a7d7074d3ea65db4cc4d473e", "guid": "bfdfe7dc352907fc980b868725387e98c11aa04554784c56b14d7293bbd57484"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806a4cb88ad3f3867302a45f0b0946cec", "guid": "bfdfe7dc352907fc980b868725387e98576748bd0f58d744a1fe9930b4c3787a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98058df59c32d201477bd2c9fea7392241", "guid": "bfdfe7dc352907fc980b868725387e986e4d8e19a68ed1392116de0eb51a66df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e43f09814a7a77ca48b1a67cf74f007d", "guid": "bfdfe7dc352907fc980b868725387e98bcf7d9a3df01d44df1699e7c5d8f54c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842c7e1cfd31e96b88bf6e771a5b34aa2", "guid": "bfdfe7dc352907fc980b868725387e981770c2183532a93b0245653d07555ed0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeb8efff0c67acb4422e19228464182f", "guid": "bfdfe7dc352907fc980b868725387e988e75032fecfc6315d898e0206ad2a825"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c15abd6e218dd388602ae1039179c9e0", "guid": "bfdfe7dc352907fc980b868725387e9863964b637c3fb3ee1103e844db34d80e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f26ee1f62896ab7e7a97ddfd4d03aeae", "guid": "bfdfe7dc352907fc980b868725387e9898ee967b20d3ae888a7ef66364e1374a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98deededd8278718a22e9fd603ae2e2e26", "guid": "bfdfe7dc352907fc980b868725387e98f70c6c9c972428c951f142bfc3c2411f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f75995efc39366890f56b4d1d231a68c", "guid": "bfdfe7dc352907fc980b868725387e98cccdf60a1cbb722053d358822f480653"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836d69eb0ffce733bea14126103c6a691", "guid": "bfdfe7dc352907fc980b868725387e9832906934c908b64148be5b2e8e194cf8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894d703b24a53516e9390dbf440cbdab2", "guid": "bfdfe7dc352907fc980b868725387e9877f2e704a153d41f2ecb0a33b70f8452"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813996a434159650623a8fcc107371e9a", "guid": "bfdfe7dc352907fc980b868725387e9887420bdfebce3b5fd487833077d6d9c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7361c370a3538118e25b9d8ac75aff2", "guid": "bfdfe7dc352907fc980b868725387e98e8a08f5d9bc8dc9be269ede8b191d23e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb17beb9571e26373b6b7528238755a9", "guid": "bfdfe7dc352907fc980b868725387e98612e24c057d469136c864a6bdd411509"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1ee4f00e7ec7c42ced6847942d9aa8b", "guid": "bfdfe7dc352907fc980b868725387e984033984f4f8f4bbf66563d45d5098aba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834bd2e8e0f5e00ab828630ea42f1063b", "guid": "bfdfe7dc352907fc980b868725387e98c98db99fb7338611f50d1d58996d07e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ad490441a9a6d06a5f8adffbffa110e", "guid": "bfdfe7dc352907fc980b868725387e98e266c32f65241260fd25ddb0e4f72f49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9d8fb463728f240ee619beb5f0dfb10", "guid": "bfdfe7dc352907fc980b868725387e986cb0572a5e557cb1e5a17096c832f747"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ba18f6f3e481524cd3851a63bc095fd", "guid": "bfdfe7dc352907fc980b868725387e98561bd11bae03316ba3f9b21d83a1b81f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897235e7bb00871f04b49d797018cdcba", "guid": "bfdfe7dc352907fc980b868725387e982556032d5edaebcbbc6a7e9ff1048429"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ebed19a6b0743c4a060b74f54f44f37", "guid": "bfdfe7dc352907fc980b868725387e98e6076cf47d8e7ce6ba37fe95af8051fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d454fa81fc025a1bd5e2b42b1bf9fc9", "guid": "bfdfe7dc352907fc980b868725387e98a40a595a8cec89d56c4cac89a988bd03"}], "guid": "bfdfe7dc352907fc980b868725387e986e04cffc8317f8ccd1e39279ced068e7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a32b4aed186448200b061d3c98abd847", "guid": "bfdfe7dc352907fc980b868725387e98264c34622cd224889110aecc3e82bbe6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834b62614b4fd128f0fb9bbd412eeadfc", "guid": "bfdfe7dc352907fc980b868725387e98f62026896eb9e9497415defa582d95c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852ddc801de4bbae8d8e12dd46825f891", "guid": "bfdfe7dc352907fc980b868725387e982c1da778403ffde4a4e413df3b2a7bd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b84d8553706ef75171ce7a78b039cc67", "guid": "bfdfe7dc352907fc980b868725387e983842581ec91fdc130facefefb57b60b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987baccc0d4881ecd106c9ec3290bacd6a", "guid": "bfdfe7dc352907fc980b868725387e98b37ea2efbdebc67ee656a22bf6222e8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984df7550f6c3739527ac9dfa953b75542", "guid": "bfdfe7dc352907fc980b868725387e98160c20fe385afb0b0d2bff9d8201bb31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcc232c2851040c9a7d644892f9f6558", "guid": "bfdfe7dc352907fc980b868725387e981667a0be471c7fca51086d2152f49b2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823aad13fa7ffeb9e98807d6d54b9d624", "guid": "bfdfe7dc352907fc980b868725387e98ff595d7433b127051baedbb361fecc69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98934304f243136db24718acfd040f5aef", "guid": "bfdfe7dc352907fc980b868725387e98cdc343c16e8e3c81aaa4fc67eff0c707"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f94452341f1c261e2fc1ef5f44c52cf7", "guid": "bfdfe7dc352907fc980b868725387e98c71cb6c49c95bd5588cd25320582c1de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eca90e91a5308aa60e3fa388807e2547", "guid": "bfdfe7dc352907fc980b868725387e984787be7c2b5b0a48fdbdd0e969405574"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98077e4885dba78772970bd16aa3c40f21", "guid": "bfdfe7dc352907fc980b868725387e98a10a8fd40f83bf21f16740c558093912"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983634bcfb1c5971d93e901795c7634a50", "guid": "bfdfe7dc352907fc980b868725387e98b39e0ccd1d68effd1e6542eb02926bc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858107a96510f906472a13bd0bb0c2d60", "guid": "bfdfe7dc352907fc980b868725387e981cdb0a30fc771878bfcb3795e17eb44f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833d1c4e9fef6ece17ecd2185c22533ec", "guid": "bfdfe7dc352907fc980b868725387e980485159f8023523ff449593900e7e305"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988245eb2179083403146db800e53defd5", "guid": "bfdfe7dc352907fc980b868725387e985153aee418635746c2d344dd493a27c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981660dd3792271b506cc30dbe499e5534", "guid": "bfdfe7dc352907fc980b868725387e988244166bb8f15ec30c1c6bb61975167c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adefded2a9ae7eae787256a7beca4ab8", "guid": "bfdfe7dc352907fc980b868725387e983cb19cab1c0824ae68931833b2859e1c"}], "guid": "bfdfe7dc352907fc980b868725387e98aa014f59aee4c192965c7862d9d502ee", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e98b7454a23364be3db047d441d7c919b0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982753cfb120d9cba6aed6fd719b664a8c", "guid": "bfdfe7dc352907fc980b868725387e981078a77343c6414368cad6d29111b7d7"}], "guid": "bfdfe7dc352907fc980b868725387e98309069a8a2ca6feb39069ee928618694", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98edb6e7836cdad4df98873bcd6219a5d4", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98cf15425e88a022385a86e619a553221c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
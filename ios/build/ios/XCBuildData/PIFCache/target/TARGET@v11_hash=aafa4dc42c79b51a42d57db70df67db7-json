{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98561fc4c07ff2f84a40b856871f038ed9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_local_notifications/flutter_local_notifications-prefix.pch", "INFOPLIST_FILE": "Target Support Files/flutter_local_notifications/flutter_local_notifications-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_local_notifications/flutter_local_notifications.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_local_notifications", "PRODUCT_NAME": "flutter_local_notifications", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9858b697956b6fd07522ccd636aea6bfc1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988a8ec8c355438084f64dc9f3a62c890e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_local_notifications/flutter_local_notifications-prefix.pch", "INFOPLIST_FILE": "Target Support Files/flutter_local_notifications/flutter_local_notifications-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_local_notifications/flutter_local_notifications.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_local_notifications", "PRODUCT_NAME": "flutter_local_notifications", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9807c31ab08aac52dd68aa6dd8a194d7f1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988a8ec8c355438084f64dc9f3a62c890e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_local_notifications/flutter_local_notifications-prefix.pch", "INFOPLIST_FILE": "Target Support Files/flutter_local_notifications/flutter_local_notifications-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_local_notifications/flutter_local_notifications.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_local_notifications", "PRODUCT_NAME": "flutter_local_notifications", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9862d0706dcffd9e2de68b07fb4588c2dc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d6cd263e4f2af6500508c41ceba8189a", "guid": "bfdfe7dc352907fc980b868725387e984c05294407cef8c51e3842359ab42590", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832de6efb874b00d0b14e878eac84078f", "guid": "bfdfe7dc352907fc980b868725387e988c59db271791022770e6be5df065cd8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c36c9d074d2acc312a73ac886b7c5690", "guid": "bfdfe7dc352907fc980b868725387e9876ff6ad3d758467342829a0a8c9444c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c734ce58595452823ba11cf0df4723be", "guid": "bfdfe7dc352907fc980b868725387e98e2fded4a4cd3f34080c86c1e6ad16cb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0c8343f6bd21525fc8fe969ec5eefca", "guid": "bfdfe7dc352907fc980b868725387e983380b9034c120bbb1365780feb4b01bf", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986ebb581b50bb5a4da55c815499424646", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98579a6bf6c1d5b8807edf6b65a0473361", "guid": "bfdfe7dc352907fc980b868725387e980c44e5db9ebacf5a115b091da4e852b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9c356607302d94d428b3d947d3d4459", "guid": "bfdfe7dc352907fc980b868725387e98dfec469434ae95a56fa9495388c012e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989054cfee5167b60681b077a440f1fcbf", "guid": "bfdfe7dc352907fc980b868725387e9822f36e87e2b9059a9ef9c3806c8f5a21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7f9915af1d099f226cbd4bc019a83f6", "guid": "bfdfe7dc352907fc980b868725387e98607e0d074d10c2c04209d66c78e0b723"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828ecf30ed3f401925edb90157cb2cd66", "guid": "bfdfe7dc352907fc980b868725387e98aa8d9d6eac51178be002312e14b563bb"}], "guid": "bfdfe7dc352907fc980b868725387e9805115e40deed1502c7564cfbe2320507", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e98f9e9bfe4edc54a6bef5735d77758f1b4"}], "guid": "bfdfe7dc352907fc980b868725387e9881842722d0f78140c7135d9f9a7bceeb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984243cfb2cf2763469a82f71ffead81d5", "targetReference": "bfdfe7dc352907fc980b868725387e987434bc9491ab71790f372f1bf966d056"}], "guid": "bfdfe7dc352907fc980b868725387e982f2d6bf16e0ea060b87821ba5246f2a8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987434bc9491ab71790f372f1bf966d056", "name": "flutter_local_notifications-flutter_local_notifications_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f49b18868e69e442795477541dae1d9f", "name": "flutter_local_notifications", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b85276e53a4583cee7a183c9d2fc893d", "name": "flutter_local_notifications.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984ada7db82e4e1e80594a7fe368b25272", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec6513c0aaabee61550087568851d5df", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988666f82178957669be8b331c99b303f7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f2271dc50c5a5d42c9eac97285556cd3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988666f82178957669be8b331c99b303f7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983bf51c018d2d3245810c76e76762001f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdefd744c38dc495a9921ff3aef79fb4", "guid": "bfdfe7dc352907fc980b868725387e9832a44dbfd670e9ac36d8a38413ded46f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd34e075fea4b4c4a289ebfdae2590b6", "guid": "bfdfe7dc352907fc980b868725387e987f8b411aaed9d021c190f89881d23bc4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981103cbff14760dbe33afdeaec24a07b4", "guid": "bfdfe7dc352907fc980b868725387e98591d58f8326c37fe42f69c62bacafe5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98279c181abeb4278aaca641a37d843ebf", "guid": "bfdfe7dc352907fc980b868725387e98a8b61b070787fc1fdb52e16321350e09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e904a17f4705d1772705a1d92fba637d", "guid": "bfdfe7dc352907fc980b868725387e98b212e7abcf3eac0e4712d497b30c7772", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dbd6ab21d5af9f25b5a19a8031bcb66", "guid": "bfdfe7dc352907fc980b868725387e987f21b29aad8afb4582ec5bf06fe80990", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cd987651656cce40181571e66e33355", "guid": "bfdfe7dc352907fc980b868725387e9864cef7f58ca4b972337341525b8e3673", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98902ccc75e9a07cb65e8395874ad177e8", "guid": "bfdfe7dc352907fc980b868725387e98f5a2aa491a67389399237f8eb91098fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f6b0f79e43c724ed81df13fd1bdd3c1", "guid": "bfdfe7dc352907fc980b868725387e98b352b3698a77ecd504ae53fdbc53084a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac2b5f6e377af100ef8935b78a3285b4", "guid": "bfdfe7dc352907fc980b868725387e98bde00bb3be941e768a3bd4668ed82591", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ef082801b67c3e79b492bd22f89f9a3", "guid": "bfdfe7dc352907fc980b868725387e986acd3a5fdf2a0215c1b035a82e959eb0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ba5c9acb701907bb574408e47c25a0f", "guid": "bfdfe7dc352907fc980b868725387e98dd104127f8b25426d0eea9e2839a8c8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edda52b3f242d2c985393ce67d5a663c", "guid": "bfdfe7dc352907fc980b868725387e983478b8dd51e848649300825dd42f2e5e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7b91a0fe40740736068375efa63a2c5", "guid": "bfdfe7dc352907fc980b868725387e981d402d517ce03c43a70ad30d194b7079", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892e0f3264b9e6e4ce70c12e7fc8de2e3", "guid": "bfdfe7dc352907fc980b868725387e9861e4e4dff8b5118c27625cf24d35a47b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a88957eade975a2e7737c2eaff9c98b", "guid": "bfdfe7dc352907fc980b868725387e98002cd7d180242787c970e9de98af6fa7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981179f9d19e96ff68c2f8fb57a8ac119b", "guid": "bfdfe7dc352907fc980b868725387e984d21c6d763cd748ec86097835e0cfa64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874b1ff77d066eaf4dab2af01470f83bf", "guid": "bfdfe7dc352907fc980b868725387e98ed0d0d51fe1f981146f69363adf39774", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c784b2b54c14faf4973fe4e499396361", "guid": "bfdfe7dc352907fc980b868725387e983a3fae988cbacf1a701d1ad74a59e2d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a8665d8fe64129e3aed06123402ce43", "guid": "bfdfe7dc352907fc980b868725387e9826e69d3158203badd151bad470e87846", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982352c88794360b9b2bb782359a641975", "guid": "bfdfe7dc352907fc980b868725387e98d6983ee8d62c4e026b3743888748ba33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9f7d8313a0558bbafcdae744e4143a6", "guid": "bfdfe7dc352907fc980b868725387e98f54546a8f1d444a0e5dc08a8a41b13f3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981cc41afc16769633e698651827a8794e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c02d1226456df0f2f8decdb90b62af93", "guid": "bfdfe7dc352907fc980b868725387e98eff2e1a6961301e6d0b370dab736c044"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985986163ea08e95f26a91564002f5c20c", "guid": "bfdfe7dc352907fc980b868725387e98bc8b4a867e76d5d53cf9d13a822d5488"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa59d452e1c68ccc4e064a2e00b83dfa", "guid": "bfdfe7dc352907fc980b868725387e981f8c51fa55464f8f54fe4a7430d5cd38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee4fa302c565d5381a48b6a4c821353e", "guid": "bfdfe7dc352907fc980b868725387e98a44360551b8800e1e02a1ed2719f15ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d05c6d6b45e3869d0b3ca2496215e03", "guid": "bfdfe7dc352907fc980b868725387e98a6f192d8b18be2677b560b8ec05c29dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f74c86cb733b4b53fa868c0038ae8c0b", "guid": "bfdfe7dc352907fc980b868725387e9874d34b46bec27263f3259cf600b6f4b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a665174b8bd1a6ad9dce5078463837ba", "guid": "bfdfe7dc352907fc980b868725387e98de6e59576a995c32469466d98d24b7b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f918b131d881ba4b881cdaf5fd5d4d23", "guid": "bfdfe7dc352907fc980b868725387e984bcbe169c1e85063f69ef6f48d67e1c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df489490e867016563745a884b5f45f0", "guid": "bfdfe7dc352907fc980b868725387e989f9ac3cbf0c4a23098f7e4cea55fe0f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865521a98509831901dd8db586deaa9db", "guid": "bfdfe7dc352907fc980b868725387e9861583b7a853aa8d9163432292ca3d67c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbef22610c322c6bde8eac5255b5fa16", "guid": "bfdfe7dc352907fc980b868725387e987f54104339a51c7a8887e24bb3717584"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b35d3485e1f8754792b3172f5ed85db3", "guid": "bfdfe7dc352907fc980b868725387e983eb7ff92cc699eb8a1570cac18658ee2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c46886ba58dbc47cdff225a620a21432", "guid": "bfdfe7dc352907fc980b868725387e980869473cd4a1f6711071eca850e8a95f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ec48112045ada4afc3e759faafa9a98", "guid": "bfdfe7dc352907fc980b868725387e9857e6a7bb0466708d2e9dcd0026614f4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855a7c0eaec3022f6dbf94e4d5ae86d70", "guid": "bfdfe7dc352907fc980b868725387e98770fc3aa373e6843d8523806446e28b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e171a182d7dc68b8b3dee3cc4de4442e", "guid": "bfdfe7dc352907fc980b868725387e980ae0a9befd84f6aa2971caf6823d97e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98215a150fec336a2335639dba42829abd", "guid": "bfdfe7dc352907fc980b868725387e9809f69f3b2a5bcb9e4e288b8be75dd178"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9500dbd3a354b8a0894a85d089a6c35", "guid": "bfdfe7dc352907fc980b868725387e9853d2e762bf969e279f90462ea2c28da5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881c5cd5e2d7771f6d9776dc934ba410a", "guid": "bfdfe7dc352907fc980b868725387e9823d0043664bb56835e5f3098434621d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4c5bb41ce1600ef8426cd8ddffa7189", "guid": "bfdfe7dc352907fc980b868725387e98032a4d7a1c44d9bd08578d68ba4438e5"}], "guid": "bfdfe7dc352907fc980b868725387e9826c920163f60d101777269726f969ec1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e985849b17ca6f79cb44f6b9106485446a9"}], "guid": "bfdfe7dc352907fc980b868725387e985aa17677208a73ac8388bddd063749ce", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f56a46dc238859b76d9e9f61d85f6088", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98c54b4ee17a360f618536a84bb279de0d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980ce16bc8d1d797d1455b2763e53bd9b6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980bd663fd5566f86b622d38bd6cdb4dbe", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d2532485b7d245fb21b0f6b56d3b6c7e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b2d284106b5cab1e55909fdcea77a1af", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d2532485b7d245fb21b0f6b56d3b6c7e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bc8635a793d9e2fb4f5d4dea3d7ae4c7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989887eae8928bc2a50410209f109ad3cd", "guid": "bfdfe7dc352907fc980b868725387e98d6817a41c9d74cd10709e7926fc0ace5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b03952e7a51a960fb5f1f7aa0b391e10", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9879f42f6571a97a41260171dc4f15756d", "guid": "bfdfe7dc352907fc980b868725387e98899312175495be3ecd2e1413f667ba37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee60d616a4f5b64eeb9ca7a46c7fcff6", "guid": "bfdfe7dc352907fc980b868725387e98ed57693c1ed67ce29e8cb5fc212df7ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0acffdd066a3c58f1e90324dc0df7d9", "guid": "bfdfe7dc352907fc980b868725387e9890ffd0c8acd0ded76f88a859ea743d3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98613c4a1bebbe215edcd1ba37f3144817", "guid": "bfdfe7dc352907fc980b868725387e98e60ce56eaab0a39bebd353b72036f6ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fc5cacb20d8ae293030934c4dc15952", "guid": "bfdfe7dc352907fc980b868725387e983129f84e7a2d1d04cae212a6a2ec560f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f65d307ce573cf1473120d3734f8b86", "guid": "bfdfe7dc352907fc980b868725387e98640bf519c2b49d5c315ef5b3506eb45e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98964cee111784590f02110577c387416d", "guid": "bfdfe7dc352907fc980b868725387e98f4c974836cf32b1e08c19af1736eccb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c8bd4caf91c855b1f1f2d3f2e1432ea", "guid": "bfdfe7dc352907fc980b868725387e9897334b05dae9d3f0b2c0c0f2c989785e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807badef08b212df52683fabbaa4a7f43", "guid": "bfdfe7dc352907fc980b868725387e9888bb87090cd246cb71aa6a333ec8d05a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9f59d18e8fc4b29128a1b6e47fb51e4", "guid": "bfdfe7dc352907fc980b868725387e985b3fd60799ae7fe8b5b8d1c5a8df63cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d345816eb7eec0fc4f7842d6c57d3f20", "guid": "bfdfe7dc352907fc980b868725387e982c82b5f77a03ba1b9c012d52d8cf2913"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877a522d93a6c7f9928dd9e21c4f2c6ec", "guid": "bfdfe7dc352907fc980b868725387e98b7a1f797e6818524447f46e0c7a6abf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbbfd0dde2f5df5333f6ecf7c35762fc", "guid": "bfdfe7dc352907fc980b868725387e98156397fe97ae3dc9b2f7796325fc3346"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac1ae2764b45f6e21e9b7d71bca83781", "guid": "bfdfe7dc352907fc980b868725387e98709eba235273241a454ed3736ffe0e5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a99a0e4e39f37b0aab850c5270c933d0", "guid": "bfdfe7dc352907fc980b868725387e98ff9a4ca291e7169060f268525a42502e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981212acca62196e9fe10995b1e9415439", "guid": "bfdfe7dc352907fc980b868725387e9887233b191285aad5779ff77a8c3cd798"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fa2f047dab0b19f38337a2ab2c4ad49", "guid": "bfdfe7dc352907fc980b868725387e98677631ae8cd853fd286bb993e37661a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987df66a216464af971ed7ed9a4e8e8809", "guid": "bfdfe7dc352907fc980b868725387e98a793e0d3eaff38127c335b9cbfcc0808"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983199d6ba008a99e94b4105804ab92fa2", "guid": "bfdfe7dc352907fc980b868725387e989b806c70a84e1408e311c0cf3afe4999"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d840029c323283791c3853cb480af47", "guid": "bfdfe7dc352907fc980b868725387e9859c8b7454c76f5648099938cc55be8e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e581cc6aa42e9ae0f52aa82b43b39982", "guid": "bfdfe7dc352907fc980b868725387e984dc674715da861b690be1d16b88461fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa7259980cdb62c5b451ebd1f54723a6", "guid": "bfdfe7dc352907fc980b868725387e98df2f5d2c0dfdb539b7273196ddfa02c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da451f09d03dc9687587cc5d4e5b52d1", "guid": "bfdfe7dc352907fc980b868725387e981cc819e62c1fb70b4a5b55e4e6052574"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98897ca8c8e97485e7bb9c660a7f01f24e", "guid": "bfdfe7dc352907fc980b868725387e98b16ca015e629c84ced5881d0f687a49c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae5700aacceae7d93692670a5c3e9476", "guid": "bfdfe7dc352907fc980b868725387e982399d4145b185a632d7409f46314192a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981093b70d0a334123e877ae8eda20b66e", "guid": "bfdfe7dc352907fc980b868725387e98e52c6493a291bc4fbf688946b814a4bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98810103ed94b91066440d38d8adb93ea2", "guid": "bfdfe7dc352907fc980b868725387e980acae6627fb8866448fc50a2d7df036d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcbb81139b02f38d98813a24c433654c", "guid": "bfdfe7dc352907fc980b868725387e98a810af4e3e73ff2165ed9f4d6492310f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981967382b8b2eaae6ad4419e4e4b1cb5c", "guid": "bfdfe7dc352907fc980b868725387e9814ecee95439537f5a2d9fc59c87bac14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba797479e8ec4222ecf31344707de9d5", "guid": "bfdfe7dc352907fc980b868725387e98478df3254a539f3573b5aaddc7c66429"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbf535a4ef4a041470217d08093494ca", "guid": "bfdfe7dc352907fc980b868725387e988beaa59add748e2280730b331511a83a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a2fb97ae8f1467e90d5a885522cc114", "guid": "bfdfe7dc352907fc980b868725387e988f687a6d9c13b37a39685b0e9085d9a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854377e9b43cb0312cafcd176bf8d3a2b", "guid": "bfdfe7dc352907fc980b868725387e9860ef283865246f1324d202143e651e39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba59c2777f1a42c1269f054f2a725c55", "guid": "bfdfe7dc352907fc980b868725387e984cbd9ac3fe6ac6bf34177d647c4384d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815abe38dff3e41e686eb91fa90c25c23", "guid": "bfdfe7dc352907fc980b868725387e98987f78a05ee5e4e22e41d6786aa4bbe0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b8f9b61a02ac8905773e5d7005055d4", "guid": "bfdfe7dc352907fc980b868725387e981345d6cab26f5947a85781a1dcc0da9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a82414d2830f87c9dd31d27c4c0435ef", "guid": "bfdfe7dc352907fc980b868725387e98cd045567340f68bdd7448b8db25c729e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98831557e62ae8889873573e752124c53d", "guid": "bfdfe7dc352907fc980b868725387e980a98c21ede887df4fa4b66008cdefcaf"}], "guid": "bfdfe7dc352907fc980b868725387e98af84039cc8780bfbe794ecff750ec7f0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e98a2c0d79ac932aba295dc0c601b2b2c7a"}], "guid": "bfdfe7dc352907fc980b868725387e983afa8ccd97658438b903ee25fe197ca9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98759a4216fbe801e35e523edcc36dab0d", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98bf2cbf94ff1c9ebd467b6cc79d907438", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
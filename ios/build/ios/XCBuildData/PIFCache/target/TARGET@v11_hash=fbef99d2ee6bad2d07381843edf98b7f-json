{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b9d47cf224f4ec896839eea0c98bc75a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982adec9daa7bd199b0a014807d3b66424", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981c44fa2f6e81428dc61531a2635b1d12", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c55bee7d8949d2da8233ccc1f9bd470", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981c44fa2f6e81428dc61531a2635b1d12", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b2fe738fb998344e72c28b73646bba9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9896f9f4965d73f049c188cc090d80728c", "guid": "bfdfe7dc352907fc980b868725387e98806d577bad2016bb80bbae4622f5b0f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d2c4b24d394984d8c101a80345aa907", "guid": "bfdfe7dc352907fc980b868725387e98b52aeddf01615b933ea3f7b1301bd5d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98197d5bc7c88c1923fd02b21772670ba9", "guid": "bfdfe7dc352907fc980b868725387e981f0c4ee31df4effca09f018d090901d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825c3631a705e0bf36f80ad8adca477d2", "guid": "bfdfe7dc352907fc980b868725387e984bd29d1c5424203a0a8dbbe89a2d5b70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dd41a1b9054c056e0930d5a85ec0b98", "guid": "bfdfe7dc352907fc980b868725387e9820a253d24d61cc6afe2ec71ac6f165ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b66f0f1e21f57fefcaf61bd9f9e2a1a", "guid": "bfdfe7dc352907fc980b868725387e98c024b990787ab9cb3e90a4dbc9ac4978", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a36cc311f5a3eb97083955fe569d1dd", "guid": "bfdfe7dc352907fc980b868725387e98066c2c1add0e89de57b36d0e29a1e87d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ac2ee902ef3221db68ddedddad77f90", "guid": "bfdfe7dc352907fc980b868725387e98631feeb5266786d1c3bb0a55d76fda01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe27b3b9139d03fc68ecff2beaa02fe0", "guid": "bfdfe7dc352907fc980b868725387e98ada9d39588245fa6085f8cc6535f4d60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987427710accef123467d2661be60ab979", "guid": "bfdfe7dc352907fc980b868725387e985452e4e134f034492bcb006d6682ac5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb923b161c1e04ea12864bbd05092965", "guid": "bfdfe7dc352907fc980b868725387e984e6964ce4cb18daa9e642463bde705cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c8991ec8b9ca67976eb84ae1f9e1288", "guid": "bfdfe7dc352907fc980b868725387e982854599d643bc39e9b12d34d7dae1e7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1632cd53fb56a5b42134008904e76a8", "guid": "bfdfe7dc352907fc980b868725387e9805adfe8c34e3f169ad5498ce4abbd97b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98336bf3a1cb2137ece0c362769b699c8a", "guid": "bfdfe7dc352907fc980b868725387e98302a5b19e7005d8592622a5cff8f95a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989de489c60dc1d466f9bdd19f73d628e2", "guid": "bfdfe7dc352907fc980b868725387e984e5147a47a180cfcab2f0bf6b4426416", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e14ed6e311ca4d5cfc18ad2c819db659", "guid": "bfdfe7dc352907fc980b868725387e98188c3d9573569928f5ee9f15726c3908", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ec1033d3da12ad940da04c334e746ff", "guid": "bfdfe7dc352907fc980b868725387e9868db6b56461e051eaa26c19b11a2a643", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f20204466be6b7e7760ca75cff60dbc3", "guid": "bfdfe7dc352907fc980b868725387e9831eab19b1b7473bb88ed5bc194390bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaec97865d3b6086e18ec94701709b9a", "guid": "bfdfe7dc352907fc980b868725387e981128dcb7a549845e4bc592abeecc459d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878b2ecf3acb0d7bfce0f633abaa7dfac", "guid": "bfdfe7dc352907fc980b868725387e984fc6c0daa1b25e893c4ecec825a42e9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c2418afed2cfac2de7bfd4e3c1f13d3", "guid": "bfdfe7dc352907fc980b868725387e9858d41b3c378c78b69f61ac72915fcb83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987efce01f2920667a6b164c300e2d0b32", "guid": "bfdfe7dc352907fc980b868725387e9822e8770adc4cf059bdf5b3f4dbfe42ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c8e40e3626e07eb062e6d1ae3509f45", "guid": "bfdfe7dc352907fc980b868725387e9811cd6b2970e9b422b995a69528369665", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f0de1fc2d6a3b63498d65a6c5135735", "guid": "bfdfe7dc352907fc980b868725387e98cdc65435808a446c0643c07c34f59585", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854c029c671ec1f01d6fbadf0908c05d9", "guid": "bfdfe7dc352907fc980b868725387e9894f3a21cca31e1a09fcdafeaea72ab80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849d929425be60d2397702bbdcc7b981f", "guid": "bfdfe7dc352907fc980b868725387e98082cf411fcbe74fb23a61afd9bedb1a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c0bcee7c20bab5e4ba4fd37f3f62459", "guid": "bfdfe7dc352907fc980b868725387e98d54e69fad7910d525524d112c6e7d2dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98152aed6efa9b374e2845bf75ac565ee4", "guid": "bfdfe7dc352907fc980b868725387e98f57c60869a755422a10504d247af146a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9b56d504a13968089b7904f29633543", "guid": "bfdfe7dc352907fc980b868725387e983a0f7028169cc406eb03bc300345650a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98506d1ce67ed1a058f0c4765ec20072e4", "guid": "bfdfe7dc352907fc980b868725387e983fe22d2ac352d8046628d96476337160", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9105c31c9d98f7bb05a4ad241871c5b", "guid": "bfdfe7dc352907fc980b868725387e98448bfacde8c2dc67d9c1534e20f44fd0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f6cc7f31685f43dd6a349f473cc3c67", "guid": "bfdfe7dc352907fc980b868725387e98ba2f9a3057cc1f7dcd22cb70ddd79daa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98392a0b566e871056a8ddb590dfae4c61", "guid": "bfdfe7dc352907fc980b868725387e98cdaaf6ba907e44c7b82a0401bbde2a98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d711ca22a3906b5863189608359965a9", "guid": "bfdfe7dc352907fc980b868725387e987c36f34a5cd3f35d8c57c8f7eb043ba8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834a6d3950189f84072552592e6939e26", "guid": "bfdfe7dc352907fc980b868725387e98b4295129df590939e25c137f47ccf1f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed6eafb6dd15f4906590e52e781addd1", "guid": "bfdfe7dc352907fc980b868725387e988abed2dc3193f070f0cf1a3fba2fdd92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f404cb8654d83a74786358058c2ed2bc", "guid": "bfdfe7dc352907fc980b868725387e98ada10369131e74d1b788a33f7dcd6e12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817b45db8378bf8043a506a6698b0e3f6", "guid": "bfdfe7dc352907fc980b868725387e98f9df3cd61852b428dfa680bd35ea754b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3bfc4de4b1a7bc5cae7a7ee9717e8b2", "guid": "bfdfe7dc352907fc980b868725387e9898b212dc8787ce3d4b7fd6a87a0a4415", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc3ac5386904e7cb3261092d42fd1619", "guid": "bfdfe7dc352907fc980b868725387e98b6cb90ce781b07e5914863ad71fa8377", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaad469d3760921101804de4d68114db", "guid": "bfdfe7dc352907fc980b868725387e98a89e2b5db28b4c2e870a39ff023e4205", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b797b83e0fe704db8468be55c6fb2ec5", "guid": "bfdfe7dc352907fc980b868725387e9853c68b4efc105e2de2e7d3ceb51e9aac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98757ecbd0bbdc4b18c279ff14deb713c0", "guid": "bfdfe7dc352907fc980b868725387e98a8473b3693a6ee75ede363a092b64add", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b034ddbfa4ef653bed00c2871b8cf021", "guid": "bfdfe7dc352907fc980b868725387e98f170c1345a02f6fa218d9153e7fc8b08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b55b23bc69e4ec79d05c3d242c14b041", "guid": "bfdfe7dc352907fc980b868725387e989d2461733f5d7437ce547fb0f0bd4558", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c5ea82cf2eb447a4cc330f2f71553653", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989a44abe52637401b6d1dd88967525740", "guid": "bfdfe7dc352907fc980b868725387e98b33f86cb67045f508cf2ace5a98d6bec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876a60f0300ed9d524d775281d3117998", "guid": "bfdfe7dc352907fc980b868725387e9816b1bb8c09b939ba28fdce5a0b98a268"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c37d4ea7d58b9d78225c40e722dbc42", "guid": "bfdfe7dc352907fc980b868725387e9820dc39a5d544b98fd5e56724280ccf59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d126471e52e1637cdadafb5d8dd318a", "guid": "bfdfe7dc352907fc980b868725387e987fea851d30d2e76ca37fe8cb655f6cf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e11cf6815ce218002fe7c97eeea33c3", "guid": "bfdfe7dc352907fc980b868725387e983e7d9aed5ea8dd70a745196c3fde312a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98174c66730e4926454d301cc99ebfe66c", "guid": "bfdfe7dc352907fc980b868725387e98cc890457a2f6ee0481b12d5821c2f63c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98465081c5890df341de16197343f82da0", "guid": "bfdfe7dc352907fc980b868725387e98a2cfa2a86b5160ed3ab4b0751e230e44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e220421bd1d571ebc08e17728a635fb6", "guid": "bfdfe7dc352907fc980b868725387e987449a7d3d6711189c31322c4a363ddba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fec30fda61b4ed8c11a433506e48960", "guid": "bfdfe7dc352907fc980b868725387e984560ced14dd67a0b0b0aa9351d88659d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866753bc56c8c2eb9791a36f52bdfda7d", "guid": "bfdfe7dc352907fc980b868725387e98205d03e4651a24e7b82c9efed3955094"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877e4c5a67e5dae2f6182002e75a8795b", "guid": "bfdfe7dc352907fc980b868725387e98d07d7f42027750948251986290dc286c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fc1072842a1ceee47ff14603aa4fdec", "guid": "bfdfe7dc352907fc980b868725387e980aef27fc8b4247a5df43db05e928558b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98362d96548c1b11aa961eaca807dcb2af", "guid": "bfdfe7dc352907fc980b868725387e98f27d34ee038512bf48712b585743a4c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd674e9f1054f6a48853b06cdf16db4a", "guid": "bfdfe7dc352907fc980b868725387e980eb550bcc03a690ffee730145a3883d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d75ffbc523a75c19b6033b89db22308", "guid": "bfdfe7dc352907fc980b868725387e98be83f66df92c6bf6fd4bd75dd680b3c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844a703482ce2bfae2466382e3089643c", "guid": "bfdfe7dc352907fc980b868725387e98616d57624f2ddab6779ec3118a0b11aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c35da83ac5b95e0d7538224f9f552d82", "guid": "bfdfe7dc352907fc980b868725387e98010d359cd88ba87b6ae74f76583700f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980480aca7c06c6f954edda49286aba91e", "guid": "bfdfe7dc352907fc980b868725387e981b38d5d55fde4d9fe1fde443b8a34c4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863f9f97e1365852f8b5af6b0bc45d118", "guid": "bfdfe7dc352907fc980b868725387e9814865e40f3a6969bcf218e4f80cae81f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98146e07b9c6c373962becf01f81c4d1ac", "guid": "bfdfe7dc352907fc980b868725387e9897b970cd6c69b01f4c1406906297cc16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882a59179218f91dee42f4b96b4f7dcfb", "guid": "bfdfe7dc352907fc980b868725387e98ca499965967e8cbdafb5206b2bf3caa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcaf7f1e1518918c0385b4accca68e60", "guid": "bfdfe7dc352907fc980b868725387e9845230914e97a9b04dc0fc28658e2bd67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e933d1e77b354252c79197a3eafdb971", "guid": "bfdfe7dc352907fc980b868725387e9894e802b6dfc20de05da7c8b6f92afa12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98255529c5ef657f933b6fd528503b3a3f", "guid": "bfdfe7dc352907fc980b868725387e9850112d6b0263163250ac7e0216ee460c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980213a847ebdbd870737f07aee1a0c30a", "guid": "bfdfe7dc352907fc980b868725387e98ae312ffcb316a9955768bdc4ea82df1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848c3a25a50e286a90c072cd9401616be", "guid": "bfdfe7dc352907fc980b868725387e983275b241aa919477874218fd448bffb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af32e644169bd3e45e24302a6f69e4da", "guid": "bfdfe7dc352907fc980b868725387e98b30b6825f205b76836fb475ed9a4fc8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceb9fc92e9a46df0e6c4fc66132a85d7", "guid": "bfdfe7dc352907fc980b868725387e98002743e5ff7bb69a9944eea5373b7602"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b95be02bec39b8018d0a8c9149e4f9", "guid": "bfdfe7dc352907fc980b868725387e98314c0bde4525ecf85bbcd48043b723f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985772741d95a5fbdf4dc988ffc255ea55", "guid": "bfdfe7dc352907fc980b868725387e9834227b514fcdfb509a53694ce72ab88e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c741866aa98e5b04332f850c645f902d", "guid": "bfdfe7dc352907fc980b868725387e980f24297b47a67a8153c561ca8afa8397"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98150e7168a67cfeeb4a75f4eb192153b6", "guid": "bfdfe7dc352907fc980b868725387e982237441783b6acbcdd3fb1dc435f04d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d318f61936a455c39bc57dcc61ea785a", "guid": "bfdfe7dc352907fc980b868725387e989d1770b9a2f6b37f93a514cb958e29ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a163d5c5e91afe738c63b6a92ef5285", "guid": "bfdfe7dc352907fc980b868725387e98327d8d630b70bdfd6daa649feec67bc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98491a9a2051cc0d1b15d37e3d6f631ee4", "guid": "bfdfe7dc352907fc980b868725387e983ee5b469a86f27b8e2c7aca3d4570043"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec7fd2ee01a0ad65c7e5e3d5bd817f7f", "guid": "bfdfe7dc352907fc980b868725387e9878c1eccc3cc4fbc3910550194fe42f0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981215512df8c52c841bdd3c43b6b4d941", "guid": "bfdfe7dc352907fc980b868725387e9878a216d321f64171005b4a39d23418fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866db018377f1ea37d9c31e1c845ec943", "guid": "bfdfe7dc352907fc980b868725387e98221fdd9b392796c927bbb85166767248"}], "guid": "bfdfe7dc352907fc980b868725387e98a4aa9c79cd0ad589e5986c6a793c7569", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e982f53c31d49d3039187aca0202c785aa8"}], "guid": "bfdfe7dc352907fc980b868725387e985a87658fd90a1d7db61dbb5b1b82aefa", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9811a6ce420babe3491436f103a769ee18", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}], "guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988d4f056f23e4e16df108000d3c5e64e7", "name": "GoogleMapsUtils.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
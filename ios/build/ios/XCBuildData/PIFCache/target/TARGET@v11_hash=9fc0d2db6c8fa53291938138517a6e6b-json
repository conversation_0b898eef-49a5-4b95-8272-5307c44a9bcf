{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989f167b5bc343b8151c30e85c2f4c50c6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/ReachabilitySwift/ReachabilitySwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/ReachabilitySwift/ReachabilitySwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/ReachabilitySwift/ReachabilitySwift.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Reachability", "PRODUCT_NAME": "Reachability", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98994d6aeff8dc6245da8bb72c9cbd4a1e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f568562480b1c4060edc2a1f59b2ce9c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/ReachabilitySwift/ReachabilitySwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/ReachabilitySwift/ReachabilitySwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/ReachabilitySwift/ReachabilitySwift.modulemap", "PRODUCT_MODULE_NAME": "Reachability", "PRODUCT_NAME": "Reachability", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c0c5acc1a9690c0ea469de5c3739659c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f568562480b1c4060edc2a1f59b2ce9c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/ReachabilitySwift/ReachabilitySwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/ReachabilitySwift/ReachabilitySwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/ReachabilitySwift/ReachabilitySwift.modulemap", "PRODUCT_MODULE_NAME": "Reachability", "PRODUCT_NAME": "Reachability", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a37420237903e5aef516deea8de3b29", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981c02beb925fe2c90ea933ff68bc07973", "guid": "bfdfe7dc352907fc980b868725387e980e7f32a647d6850a0e0c774b2cc3c657", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984f12506b0beb23b123cc8e46d53d926b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9888e7e99f041ddd8cc6b5215c9ba28136", "guid": "bfdfe7dc352907fc980b868725387e9830ee8e98de2dcd56a170cbd676821981"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809b372c6e9444f914d86dbd6c952cf02", "guid": "bfdfe7dc352907fc980b868725387e984eb4d7b420d0f92b9b0fff94b0032b24"}], "guid": "bfdfe7dc352907fc980b868725387e98c942facd31d6bd2bc178633932f93174", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987ade97a5a2b84b55ce9407f688d3854d", "guid": "bfdfe7dc352907fc980b868725387e989ed97a1951a36685ef9a856ba1eae519"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e980a85d75794019400a535225c133b7326"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819b8cce5a516b92ba7e2f696a83a0004", "guid": "bfdfe7dc352907fc980b868725387e98480fa78a4264dd60c79ef6af352bace3"}], "guid": "bfdfe7dc352907fc980b868725387e9839c25be33e13a66ea4f179f99d74665a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985b59841d1dcb0d0e927adbf3e2ef235e", "targetReference": "bfdfe7dc352907fc980b868725387e9807adac39fb3247fc816cd7da21f417b1"}], "guid": "bfdfe7dc352907fc980b868725387e9834b5bdf7d7b852cd8525a20792eec536", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9807adac39fb3247fc816cd7da21f417b1", "name": "ReachabilitySwift-ReachabilitySwift"}], "guid": "bfdfe7dc352907fc980b868725387e98a09f89f66b716da1ab2ec5e473d4f730", "name": "ReachabilitySwift", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b7c09a680103e71d45dccea570bae766", "name": "Reachability.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986dda24ae3102a8ea7783d5be0544cb23", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/wakelock_plus/wakelock_plus-prefix.pch", "INFOPLIST_FILE": "Target Support Files/wakelock_plus/wakelock_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/wakelock_plus/wakelock_plus.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "wakelock_plus", "PRODUCT_NAME": "wakelock_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988216daffd187125678cacbb651480299", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ea3f5b3662fb7fee178ef72e2168fa27", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/wakelock_plus/wakelock_plus-prefix.pch", "INFOPLIST_FILE": "Target Support Files/wakelock_plus/wakelock_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/wakelock_plus/wakelock_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "wakelock_plus", "PRODUCT_NAME": "wakelock_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fd4e48fc892bd3b25492c62c8151f880", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ea3f5b3662fb7fee178ef72e2168fa27", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/wakelock_plus/wakelock_plus-prefix.pch", "INFOPLIST_FILE": "Target Support Files/wakelock_plus/wakelock_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/wakelock_plus/wakelock_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "wakelock_plus", "PRODUCT_NAME": "wakelock_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cf6d21a2de214de91a24f65afde48397", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e5694ce0e24e8904887309befb9d70f", "guid": "bfdfe7dc352907fc980b868725387e984ff557207bd64c7a8861bf8c57f48d03", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e085a4432e4b43094c8abcd46059391a", "guid": "bfdfe7dc352907fc980b868725387e98699740a73d0ac48acc3e5763048e7ce1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1b3072519a03b3f8eb1fe272c26e665", "guid": "bfdfe7dc352907fc980b868725387e980df7793a9b1d90b97908fa63a1a35b16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d411a7304539489be66179f767573c31", "guid": "bfdfe7dc352907fc980b868725387e988c308967384137f851a5684a35e28d41", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984864b58190ba3348098d4a0d45a06f24", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986680374aed8963c9a5b1d6107076f219", "guid": "bfdfe7dc352907fc980b868725387e983230c73e2004a66d416a11e5f0d7460e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892576c3768eb262a4ba19f1cb76e97fa", "guid": "bfdfe7dc352907fc980b868725387e9841b8e5bc18fe5aa9bd0c235bf2df2697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8f429faffb7b6a390c1b7c4c34e9a73", "guid": "bfdfe7dc352907fc980b868725387e98cf540c28de1d19f6f6dd2696d6a3c43e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881d5eec3c71f706c738a797c3f842c31", "guid": "bfdfe7dc352907fc980b868725387e9816730edb354be517ed32be1f86f018b7"}], "guid": "bfdfe7dc352907fc980b868725387e98f26b73137496136f2be8ab2a22e2db2f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e986c7d572c07a9af9739dcfabc6479207f"}], "guid": "bfdfe7dc352907fc980b868725387e988e3a4c79a186f2732d86a8c08235bbd6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9816c279c955f17c1853faf90007559122", "targetReference": "bfdfe7dc352907fc980b868725387e98581b37173e8b6c1bb9293721a603b6cb"}], "guid": "bfdfe7dc352907fc980b868725387e9880af879e0ee649fc95ed3a5efa0e8ab8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98581b37173e8b6c1bb9293721a603b6cb", "name": "wakelock_plus-wakelock_plus_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e985ee86805101bc8fd279e03690a1048af", "name": "wakelock_plus", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98076c34494e4629b9e213e9d6e2a4736f", "name": "wakelock_plus.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eee93f35b5968a2ac316beb8bee85a50", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "shared_preferences_foundation", "INFOPLIST_FILE": "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "shared_preferences_foundation_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9872fb0acbdf98a46c9b650ca4117aa8ee", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989c9062faa7d35a3f108732926eb01f04", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "shared_preferences_foundation", "INFOPLIST_FILE": "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "shared_preferences_foundation_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e981cbfa9026dc87f4d695697c1e2f0ab37", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989c9062faa7d35a3f108732926eb01f04", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "shared_preferences_foundation", "INFOPLIST_FILE": "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "shared_preferences_foundation_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9826aeb611dd2fd84f0a210957b98dcf41", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9894627a9a4720e5570ab2e0e454aebae0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e986ad36bb009b64c267bff5dc52f07557a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cd99a363781f7fd68cec50366c25b42c", "guid": "bfdfe7dc352907fc980b868725387e987d87c5d557bb70f02c7872cd487198e6"}], "guid": "bfdfe7dc352907fc980b868725387e98482540e73d51a391b64b65199a2b2d48", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98e0be3b0d5ad56f1985578b1f97431765", "name": "shared_preferences_foundation-shared_preferences_foundation_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ad625504a4c1e61077bbfd33bd1d1785", "name": "shared_preferences_foundation_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9839253c923839df287a817908fd3a53b8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b05c14be3a78614e4a1f5a5f5c238b4b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989476d574c6e3a791205ee1ede54c6441", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9873a5080db5996b774248795095d7cfac", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989476d574c6e3a791205ee1ede54c6441", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f31505ab7b708bf7434707e3fc1c9a87", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9838da207ac613193cbac0cc6f978c5e0c", "guid": "bfdfe7dc352907fc980b868725387e985b42c6dd6690be9a75b0c417195799eb", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98061689ee0306c5047595999a70b09f2c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9803663afad53c24521314b7577329d6fb", "guid": "bfdfe7dc352907fc980b868725387e9857d256a52d6e78b307e943fe9d25aece"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a12f254023a9d3cf8eba5a44030365e8", "guid": "bfdfe7dc352907fc980b868725387e98b610935723f8c09eb7d4f452d51b227a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816d6af8d358a5fd7b4ef48e21273836d", "guid": "bfdfe7dc352907fc980b868725387e983036d6e41f121dd7d799f6add66c1512"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3ee749532b465d6438f59eae0c156b9", "guid": "bfdfe7dc352907fc980b868725387e9887f8067951fe0eefcaa61bcf53b3ca85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d4ae62302d826a4b4c23955ab12795c", "guid": "bfdfe7dc352907fc980b868725387e98b5129abc6ccd69a5535f2c5c3d7d88a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831321ec1dbb49714b23986060d625efd", "guid": "bfdfe7dc352907fc980b868725387e98e1db5207850e4edbbbccd1ae0d947543"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a29faf8c707a43a68cc62477c1992363", "guid": "bfdfe7dc352907fc980b868725387e98e1e50a7283fb812b84b5ccb6942e45b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fa83b605ee948820fff6b858441cb37", "guid": "bfdfe7dc352907fc980b868725387e983f35706ba4323704f6ce726791938d9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be06dbb5ce6b9268fdab0c249c42d140", "guid": "bfdfe7dc352907fc980b868725387e98fea830aa255b63d39c3c5abb2c8f5ba7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c3186886ffdc06fd78b752f68b1080b", "guid": "bfdfe7dc352907fc980b868725387e98071c14610da491a02f46ba364cb17f86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98568c218de634f4cd13568379ab58cb78", "guid": "bfdfe7dc352907fc980b868725387e98f2b0db461cb3843aa813fe44d62e8239"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810af7c34f3b45df2e7566e19d8c3dca5", "guid": "bfdfe7dc352907fc980b868725387e985690515a63a3315ca6a920df26f5cab0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986da572bc202d2a54c157d912f037eaf3", "guid": "bfdfe7dc352907fc980b868725387e9883b596ebde0f51ac95dd611735091227"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b61458c1d24c9ba1433b7fd48cceb35", "guid": "bfdfe7dc352907fc980b868725387e982c0fb3f9f6e3966d1d618e59527bf521"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844763ce7677bb6e6bde6a7c7deead74f", "guid": "bfdfe7dc352907fc980b868725387e9836053ef519053229848854a87cc68cf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df4269c23bfb317a20a64368a7aa4e0a", "guid": "bfdfe7dc352907fc980b868725387e98254e70769937528afff9b3c477d0906b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a7af5f8be0da0b90848a03b10fd69fd", "guid": "bfdfe7dc352907fc980b868725387e98dab1d9d9f50713ed3167b72017d6b92a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894102ef9a53aabfbc07a1a5ff1e7a83a", "guid": "bfdfe7dc352907fc980b868725387e9849771d207b9dd6bce8983e9d969935f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbb84a23bda034d483e3be82608cd34c", "guid": "bfdfe7dc352907fc980b868725387e98d6cd19bc08a0e52d5daafc4995cf8e76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee323b254d5ab36bfc02b8bfee6e6dd", "guid": "bfdfe7dc352907fc980b868725387e981da18ceab34ac3b09c79c7ca6da716dd"}], "guid": "bfdfe7dc352907fc980b868725387e982964a87049b7277f5fdc5580a4f7d5f7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e98afb2e7c6e90779949c2acf7cbbb838d7"}], "guid": "bfdfe7dc352907fc980b868725387e98c83609f95e2d01aee19246ddc531c185", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98503f9ef04b2a9c7d63c6b04018fe9690", "targetReference": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3"}], "guid": "bfdfe7dc352907fc980b868725387e981152b97f8785cad71cd7112d727b8a4a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3", "name": "PromisesSwift-Promises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982bfe7b75487d9ef7158f28fa2f89d57f", "name": "Promises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
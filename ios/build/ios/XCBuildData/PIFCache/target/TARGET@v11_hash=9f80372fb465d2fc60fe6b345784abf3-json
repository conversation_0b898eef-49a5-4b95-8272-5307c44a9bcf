{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98efc66d1718714c197adb820c1d26818a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-prefix.pch", "INFOPLIST_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleToolboxForMac", "PRODUCT_NAME": "GoogleToolboxForMac", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beb16f2952c6c43ac81cfedc49149963", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eda80946fb72d70b5035c68ae2c51ea0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-prefix.pch", "INFOPLIST_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac.modulemap", "PRODUCT_MODULE_NAME": "GoogleToolboxForMac", "PRODUCT_NAME": "GoogleToolboxForMac", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e02cb6ccfacfc3fae1ee49e366da24c3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eda80946fb72d70b5035c68ae2c51ea0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-prefix.pch", "INFOPLIST_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac.modulemap", "PRODUCT_MODULE_NAME": "GoogleToolboxForMac", "PRODUCT_NAME": "GoogleToolboxForMac", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d557e98d81a68c205a04cc98c4b7530b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980d6565cc474301ae07a89955b764d891", "guid": "bfdfe7dc352907fc980b868725387e98ac85f9121766114c2cc1f2be1edcf75e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869082954a6edbf1f3a23579e900cf876", "guid": "bfdfe7dc352907fc980b868725387e984175d9349a1822937ee245123deee053", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98406ba4a9a9a52e2fb72addc2d7b888bb", "guid": "bfdfe7dc352907fc980b868725387e9805cafe3699c4098383fd4323fd71bf8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7f92843ec334c7ca61fdc53423ae1ff", "guid": "bfdfe7dc352907fc980b868725387e98718554d2a542a0264c27ebe1c3fe3090", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98050654ac0932061f9abda0d041bf4a37", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ba23927ef6374ae41935eed693037c5d", "guid": "bfdfe7dc352907fc980b868725387e98967089aa625be5feab847cd01c0cdd92"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e986fb4d433aac9caf0db58c701bf2495a4", "guid": "bfdfe7dc352907fc980b868725387e986eba173434eb2e33868d031e0e9b0919"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2d13c5708b356eaff4e0bb8ce73c8", "guid": "bfdfe7dc352907fc980b868725387e985d2a8fa8dc4e3e65ccb5630f2b62170b"}], "guid": "bfdfe7dc352907fc980b868725387e980b781adeeb18073634231fbc910414b3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e98bb9e21531978b09d1d84af6eed00cd77"}], "guid": "bfdfe7dc352907fc980b868725387e98008064fb34c52d1ace4eb0fa57dbd762", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983026edbfa3f10d2e1d4eab1f6b20054b", "targetReference": "bfdfe7dc352907fc980b868725387e98e474ad9306e7b8df54bd6c4337ea1912"}, {"guid": "bfdfe7dc352907fc980b868725387e9868e2516ca2a7c8fac2e2e14d881e2131", "targetReference": "bfdfe7dc352907fc980b868725387e98a435583ab4c2282d404489aa813de99b"}], "guid": "bfdfe7dc352907fc980b868725387e9891f8b2bbc0844aa52df62cd16e2dea92", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e474ad9306e7b8df54bd6c4337ea1912", "name": "GoogleToolboxForMac-GoogleToolboxForMac_Logger_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98a435583ab4c2282d404489aa813de99b", "name": "GoogleToolboxForMac-GoogleToolboxForMac_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9896cd7ae8c7639d8f9257b5465384bf6b", "name": "GoogleToolboxForMac", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98687f19ce59be21c066e59085f757b472", "name": "GoogleToolboxForMac.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
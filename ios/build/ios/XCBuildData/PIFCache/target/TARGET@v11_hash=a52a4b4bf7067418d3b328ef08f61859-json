{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98607b6ef9b23209223d91b907b6d98c3b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98eebb6c6cb25bf0d05b895cd7ea7bdfff", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ecfe42b26abe92ed9e381571246f2f6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9818770b546e1f5694bf72b86fb2d90013", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ecfe42b26abe92ed9e381571246f2f6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98441a544767dcdf85f7d589ff74c71fc7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ed61f8f8aaf8c70a386188a68e4e5e5b", "guid": "bfdfe7dc352907fc980b868725387e98f7989b77be90c4c1ef365556b9007f6a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a622e7ee02e18e19e5cdb6fec5e63b1", "guid": "bfdfe7dc352907fc980b868725387e985f208f5342c3fa39dd5f4f42cd418fb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd08f6c5ce365361770361afb4d2eafa", "guid": "bfdfe7dc352907fc980b868725387e982b7c4830d806cfa0abf119d7213da06e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a18d46ff8bccbe13ee9029c758b88a1c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9896c6086dacfeef495af44cf9c3a6f137", "guid": "bfdfe7dc352907fc980b868725387e9899c1a70d29aae0d98a3fa9f556886d01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98151cca3f12bad8f20a37e2de82aa0304", "guid": "bfdfe7dc352907fc980b868725387e9817d1b5a47f82a2068a9f50860bad2935"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895b7ed5cad1b0fe7a8faed4b61a876ef", "guid": "bfdfe7dc352907fc980b868725387e98af023b4e548034dee58df10062ca729e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811646e8f275680976cc1509a2b107d0f", "guid": "bfdfe7dc352907fc980b868725387e982c2eb279b79bbf27e5209434975e46e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898e456d4dbd3656656d01d43885e1f2c", "guid": "bfdfe7dc352907fc980b868725387e98cefd94945230cad08b4629ea2ddf1291"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffb5f81a273e9b4e1da694c570279840", "guid": "bfdfe7dc352907fc980b868725387e98b1c75ec12145c44da9f088272e5e7325"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c24dac37fbc5ecb7b88350988db0c2c3", "guid": "bfdfe7dc352907fc980b868725387e986f470f53cbb367af9dfc1898aaf32325"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815f7a05a37a427a1185d8affd1316f34", "guid": "bfdfe7dc352907fc980b868725387e98c4abc4704d45146ea036aae699378cf9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8649f6ea4c22bca80c72fb990812af5", "guid": "bfdfe7dc352907fc980b868725387e98347d5042f2d57220a623027885ca70c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d23b86cfbcbd8ab3af000e269d97b3dc", "guid": "bfdfe7dc352907fc980b868725387e98ec4e3fb99141eac941585b93948ec674"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865190de7d5630c6e80780e2dc0456450", "guid": "bfdfe7dc352907fc980b868725387e98dc7dd6a3fc14aa96e8ae9bd8796e4ff2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98471e6cbac15a67fd276cd69c26726c38", "guid": "bfdfe7dc352907fc980b868725387e98c280201be3a79ce196a094ac36fcb9e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae7aace0b64a374152c146298af08258", "guid": "bfdfe7dc352907fc980b868725387e987466a8515ed9faedfda94f7e4c1c0fc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98024fbb4591fcf87b573648f017abefff", "guid": "bfdfe7dc352907fc980b868725387e98acc91fa8451a0883d3e487bd1abadaf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986250167af7ad2af6152d1907b157aec5", "guid": "bfdfe7dc352907fc980b868725387e98bf566af85037a672f9597429250bc03d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840ad771910127c81879bf33f2005b1f5", "guid": "bfdfe7dc352907fc980b868725387e988a13bfefbd419a94aa91356b3cc07b25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8653ced298ea7148d8ec2c14b4b612a", "guid": "bfdfe7dc352907fc980b868725387e98483d445b7207819e431246d7213ab6a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98262267302270e36c4bb21a9cbcc623c4", "guid": "bfdfe7dc352907fc980b868725387e986e18f2fe81d172a14a0eba32c97bff50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c72b782c3a1fa4f255181a986f723a5", "guid": "bfdfe7dc352907fc980b868725387e98a633aa01d57d43ff214e939f7f2e47f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9254d8f7ef16664c7c990da194585b3", "guid": "bfdfe7dc352907fc980b868725387e98777326b78bc7ad2738d0e4e9b1805224"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877ff31e1f8e569415e1fce05e0c2b9a0", "guid": "bfdfe7dc352907fc980b868725387e98bb25eec186a6a29505d106de5208c170"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a31d4a5fd784f1c3bf80682c320a9d47", "guid": "bfdfe7dc352907fc980b868725387e98d890238d44f29bfca9e71d2126cb520e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986436466b885ae78b2dba5b51837fcb74", "guid": "bfdfe7dc352907fc980b868725387e98b3d7598b51fb6587d20509151ecb7025"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a032d37dcea45dbaeda5d426f58859ef", "guid": "bfdfe7dc352907fc980b868725387e98579ffae33d445006ada9ef06e849d306"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f195d128b3c300fc7f6d28e90e916bc", "guid": "bfdfe7dc352907fc980b868725387e98c53063c63ddee77c83b8873cffaff67a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa0c815e18077284e5b08c3c54597168", "guid": "bfdfe7dc352907fc980b868725387e9819160d54427cec126503d37b4491c55f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ef79e0b5988a53a374b8cacfc51545d", "guid": "bfdfe7dc352907fc980b868725387e98bfdd6922dab33ea5764dc123e991f6c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3b37528969ef8bee57767198727dbf7", "guid": "bfdfe7dc352907fc980b868725387e98bcc33794038de33da711e659017053bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98416e571c5ebe469c59715033f6b6e82a", "guid": "bfdfe7dc352907fc980b868725387e9897096d06f02b8e6988bba38a2ae079a1"}], "guid": "bfdfe7dc352907fc980b868725387e983df0d062c37ac631b01bfa5cb0f82474", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e9844a9d2a0c8f9f2ac5998345641e528f4"}], "guid": "bfdfe7dc352907fc980b868725387e987cddd27ed1303e5257f27173ccc19f18", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b0250fbf570abaf102aba6abf2bf8e3f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e98424a0579f05b8aa7b116a0e1ae14c72d", "name": "FirebaseSessions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a41ba860aa6fc56673ac239987133d67", "name": "FirebaseSessions.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
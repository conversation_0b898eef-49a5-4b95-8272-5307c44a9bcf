{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e87284438fa16088bebcdc348ecde454", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-prefix.pch", "INFOPLIST_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "reactive_ble_mobile", "PRODUCT_NAME": "reactive_ble_mobile", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9830d37ff74875bd80d319f592ae900ae8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f95ba695e9a63e75e6f002448c8f8d16", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-prefix.pch", "INFOPLIST_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "reactive_ble_mobile", "PRODUCT_NAME": "reactive_ble_mobile", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986806771924a48c430e1fe305958d0ec1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f95ba695e9a63e75e6f002448c8f8d16", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-prefix.pch", "INFOPLIST_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "reactive_ble_mobile", "PRODUCT_NAME": "reactive_ble_mobile", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cce1de4c75077f27b0d18207d61e7086", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98beda12c90dab4a75cff8a4d606179605", "guid": "bfdfe7dc352907fc980b868725387e98960b75783491c4082705401c5de2cea2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820af98294e0adf85db87b5b327395348", "guid": "bfdfe7dc352907fc980b868725387e98ea06133a9a12cce64317195ddfa58f0d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989f569d753d4771080e6263d82d15d67a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9850a3a65e70dbd14ff6a136ab547265b4", "guid": "bfdfe7dc352907fc980b868725387e98a761bea0677e1c434bf2897a8c401b6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1029ec1025a0c9817ff56e87f8a7182", "guid": "bfdfe7dc352907fc980b868725387e988e10b313b886b381b6850797ee9fe6d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866ec72686d81237f7d30605089652100", "guid": "bfdfe7dc352907fc980b868725387e98c4c16d29d0051d0688f18ad5c3d86f16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989390044318281fba873ebda619611ba5", "guid": "bfdfe7dc352907fc980b868725387e9826320d947563937d2a45f4d7b6607ce4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a45b9c8f89f1b333074cb36b5cf44019", "guid": "bfdfe7dc352907fc980b868725387e98c0d471cad5f5cc641a1f2b5169d34990"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c401a80ceefaed718130de786bd563b4", "guid": "bfdfe7dc352907fc980b868725387e98caf15ea429237ed8fbed77d46d621e01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877f2bb5c9a4a0a7276f40a54d34e34f0", "guid": "bfdfe7dc352907fc980b868725387e9845004b09535ae183cdbb63291c19f28d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ff1efe42abebde838ee49ba217ad549", "guid": "bfdfe7dc352907fc980b868725387e98f41cdf98d7dbb38a609c515e94223fbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcd1da1a4ad929527402896326e1d0dc", "guid": "bfdfe7dc352907fc980b868725387e98c8752755996990eae00bcf70bec76257"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98036579a2604d00a2d076d191a75d420a", "guid": "bfdfe7dc352907fc980b868725387e98a4c5901b89992b619c6d57b30fab11c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7f2709101fb47d9b556647484fed3c5", "guid": "bfdfe7dc352907fc980b868725387e98809fee8638089ca5bf84d9b1239612bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98121442471935a9048309a10de2426303", "guid": "bfdfe7dc352907fc980b868725387e985cab3d9d1cf0c33f5ad2630adc613ba5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcd06ff439ba1959eb81217401c07ab4", "guid": "bfdfe7dc352907fc980b868725387e9855771689ea62b33560f6487f0157c0ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98571de36eda7c632cba0e1babde9de564", "guid": "bfdfe7dc352907fc980b868725387e98638cf21da5bf19084b35539f61120a4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984869ccf08650ec170362067999b4ae6e", "guid": "bfdfe7dc352907fc980b868725387e9816169a0374e29b7aad73f86e5bb3c4b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf263e3055db7c657de777413d7876a3", "guid": "bfdfe7dc352907fc980b868725387e981c4a731f7bf8c852acee4339dcfef7b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcd9577e770378931174e4ba41c651a9", "guid": "bfdfe7dc352907fc980b868725387e987e193b58ec71c76fb068a50c30303b75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d20d4e53ee85728784a597d20403ba3", "guid": "bfdfe7dc352907fc980b868725387e984e8656e4861c3ee92fcfe50d1cc75e9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863ad9aad614d24f48f438107ddbf5bd6", "guid": "bfdfe7dc352907fc980b868725387e9876dd8ffbb577ab0318c8d0243aa4335a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98567c64872b3798a3fa43d3e836f0ee4d", "guid": "bfdfe7dc352907fc980b868725387e98afcde53b2f1ccdffd354bb93e407e71b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c85efbce8f64d3e6aff923ce28e5cd28", "guid": "bfdfe7dc352907fc980b868725387e982df5d034dab734d7b66b9dac96c5374c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3ea1e05dc18a30cdeae84fb35135e8b", "guid": "bfdfe7dc352907fc980b868725387e9881f7de12680b18a13cae70a9513a9ae9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981346274eb518cba7fde8f3c5cea7b0c6", "guid": "bfdfe7dc352907fc980b868725387e98970b9e22e35aada9574226c7c7c391d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae8996bb6ec3bce84ee3b50d91f20c29", "guid": "bfdfe7dc352907fc980b868725387e98fe3e49acead19fd3ea007986a1328bbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b05fe73a32de9c3cb0d9b2109aa1b024", "guid": "bfdfe7dc352907fc980b868725387e9845027297f8de2b4b6d1cfd91a29771b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0ae6d86b6456d09a93d7f10ee68176b", "guid": "bfdfe7dc352907fc980b868725387e985c0b6e6bd8e3681371248bceaf76c374"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aec012e04a225a0f38f7a9d690469490", "guid": "bfdfe7dc352907fc980b868725387e98615d8a14d2d608418c592ff8735a7093"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e52c829c92429e974263ebd82bc186bd", "guid": "bfdfe7dc352907fc980b868725387e9825afe91da9f0a42cfdde4513bc05ad24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d850eab1566dc29cc6d8ffa30d1fa6b4", "guid": "bfdfe7dc352907fc980b868725387e980b26a5dccd62464352a9ce399b53fa83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca0af692e5510c6581941a8632b70f11", "guid": "bfdfe7dc352907fc980b868725387e9865f63a4eb75df534aac34eeb9a6bcafb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856be7779ee245e11d61a16d83b02277e", "guid": "bfdfe7dc352907fc980b868725387e980bc73a07e40f621c5d47b4de57fe54cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e538b62668b5f70e495735d9777d6e93", "guid": "bfdfe7dc352907fc980b868725387e98f098304621b08e66330de14dd67bade3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0b3ff7e4c442684afaaa0de10f931e1", "guid": "bfdfe7dc352907fc980b868725387e98762f2ed42d505809e6340c43d993f0e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cacad9a79fd391e0ee2e4981c218e32", "guid": "bfdfe7dc352907fc980b868725387e98a7a9d1435caa87bd3b0a52853261bf08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de33911f1a9ac07cf63ed37586e99a3b", "guid": "bfdfe7dc352907fc980b868725387e98540dee5882e16f210ff3194e1b0ca35d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98096d0ec1a581ba4436f765bfab04199e", "guid": "bfdfe7dc352907fc980b868725387e98b7d833174f36b6cdbede6aad7c2b61d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819fd9133ac5ab321647c4a6030a2e625", "guid": "bfdfe7dc352907fc980b868725387e98fc15efbc2d5527f997cf517db3afb030"}], "guid": "bfdfe7dc352907fc980b868725387e98a0b3871a37813959de520a98939b39a2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e983872e90de6d1dcdbcb948352a3086288"}], "guid": "bfdfe7dc352907fc980b868725387e985635a279bbcaf69496db81a853ee1a8d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9886a871d8a8a1f1e3019629904ec06c36", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98ea730784e531c7b29a4d3807c4f260df", "name": "Protobuf"}, {"guid": "bfdfe7dc352907fc980b868725387e9840fa72b1389229bc82a786c7bb54bc7b", "name": "SwiftProtobuf"}], "guid": "bfdfe7dc352907fc980b868725387e9882f8d386d4480ff95a26a30c940edf12", "name": "reactive_ble_mobile", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ad15dc253cf5cce54073e74a8e492f3f", "name": "reactive_ble_mobile.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
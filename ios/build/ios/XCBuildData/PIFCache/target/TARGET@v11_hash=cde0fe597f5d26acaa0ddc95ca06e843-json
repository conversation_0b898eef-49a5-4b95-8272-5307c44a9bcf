{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f86d6d353271cf3be4114f8fcdacd7a7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989c19952518df404acc48f23d6997ab6e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98635cdabea02662b0661857efa49d3ce3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989aad5eab2fb58c41bdda5b8b8d5ac53c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98635cdabea02662b0661857efa49d3ce3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988fc256b032987e800fc1366a0bc503d5", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98903cf559d758be16e51041f7fd804d7d", "guid": "bfdfe7dc352907fc980b868725387e98c85817bcdfd58ecd711e9fe89c566f7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987eb1a3107a0bd043f7d7d0d1a31132cb", "guid": "bfdfe7dc352907fc980b868725387e9842696e6ae22e0a6b46149fd40d3fb167", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828eff7cad302dd5249c1e7e0765f6f9c", "guid": "bfdfe7dc352907fc980b868725387e982805786c205bdde3feab63281e17fb95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b53cb57a2e253781402a6852757b63e3", "guid": "bfdfe7dc352907fc980b868725387e9810dd51458faf18dae0ff15d26f974980", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce59f459ce7e55f0fda45ef7f4a5bbb3", "guid": "bfdfe7dc352907fc980b868725387e98423dc5387fafeeaa0fb64006e46aa4ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b45d4ee3b838b16bb6cc465a5b8f695", "guid": "bfdfe7dc352907fc980b868725387e985196fb8ad1dbb362237998e9bd0aa227", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98beb48891079d176f82a74155f0fd93e9", "guid": "bfdfe7dc352907fc980b868725387e98ea84c57b6727a30286de22ec4c792f28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c97939159395e6cb15a0075c75bab99", "guid": "bfdfe7dc352907fc980b868725387e988f1795f528c6ca3305e0eec57189df5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aba8c9ff2bf1e19d2cc4f0c17e36f6f5", "guid": "bfdfe7dc352907fc980b868725387e9849af2d80bed91dbb163abb2d23196573"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0b0cae027ceddc6c17ac200e599302f", "guid": "bfdfe7dc352907fc980b868725387e989dd35d9362b36a612c17ee26f525953a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a5e11489c8548aa5ec31b43ca115204", "guid": "bfdfe7dc352907fc980b868725387e98da073c071eb76452fc82e909597c2a91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fec3a9f075d1fa34f018a21011851199", "guid": "bfdfe7dc352907fc980b868725387e98bdb1a0e1f4c8df07f4cd0427d04d2671", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2e113e7420d98717fbd3242477656dd", "guid": "bfdfe7dc352907fc980b868725387e98a32aa3bdbb3052ad43011879c1551325", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9f2a9a5927e4ec32e6baa17c30927ae", "guid": "bfdfe7dc352907fc980b868725387e98c176d7efed6179879bd8313a92b1d260", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984db5c2535bcbe3361b7e36e1b48f9333", "guid": "bfdfe7dc352907fc980b868725387e9812b9194b30f5eb03cf9ad4dadf5a55fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3731843732daf4d5d259590d566493f", "guid": "bfdfe7dc352907fc980b868725387e9832fe0ccdb429d4c043290fc4506827b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ccded9ae1922f8fe001ed93ceb6ed8a", "guid": "bfdfe7dc352907fc980b868725387e98038f7a0c9a58266f21a030adbec7c5d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a1781e7c5e76966430356ef25da2e2c", "guid": "bfdfe7dc352907fc980b868725387e98599a7d044d027c553daf724179b2e961", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821a47b46a7dba4bb2171a3fe1654078e", "guid": "bfdfe7dc352907fc980b868725387e989956fd404d1341dc4e471fcf9cb14d29", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858a13544bcd70d61c3f6fc69be7f5610", "guid": "bfdfe7dc352907fc980b868725387e988476b5b766aec1f1f1092c1df24e61ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98072d063a6d08f225557f843ea4b62cc6", "guid": "bfdfe7dc352907fc980b868725387e98cf1c66903603625382c51e5a9ea1c90f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831fbb8105734d6f34e3d5a7cebb5246f", "guid": "bfdfe7dc352907fc980b868725387e987b3406d077a39ba487a06f2d20862954"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dd7d2a0ac38822b7dde5fdfb6069af7", "guid": "bfdfe7dc352907fc980b868725387e9873845ee890ce7ec79d2abfb28f4e7a17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e69698585507104df417f5b7f2a0e5e3", "guid": "bfdfe7dc352907fc980b868725387e98ff3f8a99b5a327f5a218e04b2a256cac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98490eceac32447c256d77853bbd41dad9", "guid": "bfdfe7dc352907fc980b868725387e98eae4d38e110de10dd005fdb880d5a4cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864979fde15a493855402ce744b27975b", "guid": "bfdfe7dc352907fc980b868725387e98d01f0e40c5144eb6070fab648168f4f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fccddaa37fa9b6224d9899b3a9243215", "guid": "bfdfe7dc352907fc980b868725387e98b201ed538e2a61b49a739826a9c4fabb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ccbe80e4a45e2e4d8edf2741bc7f0a1", "guid": "bfdfe7dc352907fc980b868725387e989a9d72be9206139151de94404979bb3c"}], "guid": "bfdfe7dc352907fc980b868725387e985b96976494ac53d07ce88907e843ea10", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985e7013e825ed58c1a3d54740ede4c225", "guid": "bfdfe7dc352907fc980b868725387e98f0de6ddaa135d7da4154d6e3e08ece27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4cb32a14fb64e28744a97eb81e25add", "guid": "bfdfe7dc352907fc980b868725387e9852f5f32a6d2318b70f9d2d4ef55ba092"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f29d84b93164390393ab9e7b0ad9af1e", "guid": "bfdfe7dc352907fc980b868725387e988a6489f66d0cbe1bf2008a3c8c143b73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867fa7ef8bf6d6d8758bcf67325f22ac4", "guid": "bfdfe7dc352907fc980b868725387e98ffee40951aba4034aa3a6cfcef12b223"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d420d51b093b6b400369fb4d5bcf596f", "guid": "bfdfe7dc352907fc980b868725387e9807635f6dc796a4af717f5513f381b710"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98125f319d1375ebf12392d3ddae360e4d", "guid": "bfdfe7dc352907fc980b868725387e98490fcb1dcd03addbbe58f264637572bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98419f510c34dcf08f4c1c869a88fa2f56", "guid": "bfdfe7dc352907fc980b868725387e985c8625f5dfb129d8e6411ed2aecf9c05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de581479d3527df0ef5ecd808639e2f1", "guid": "bfdfe7dc352907fc980b868725387e9849830391d051be18d17c8b5a29683acb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98075419d8d20f637356b4d78b0e11e0c3", "guid": "bfdfe7dc352907fc980b868725387e98adf3ed98a11e6bfb39628f1a87305c5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982055a5efbd9a171be9f06491bea650df", "guid": "bfdfe7dc352907fc980b868725387e98bdea2136745b16058e546a63c14d6d4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d809cb2e35f6899cfb20938642fd1b2", "guid": "bfdfe7dc352907fc980b868725387e988e4bd3c1fa8d267b7c01b893f209babe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846e21652573141cd4faa17f1652123f3", "guid": "bfdfe7dc352907fc980b868725387e988e3d98d7a7a46fcf65678f59b2fbd0e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986976116cebf5dbc7a5cab0e3905a7f61", "guid": "bfdfe7dc352907fc980b868725387e98559b14e8e9e17db9d2c7ba9f1180c614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a9269495ce6fc8e7f555aa9b22eec6d", "guid": "bfdfe7dc352907fc980b868725387e9878cca96fddc300c5a7103c12128f1721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eebfba879e9b327770f259ba1ab6ddae", "guid": "bfdfe7dc352907fc980b868725387e9821fc00badcd867e586cb1233ec9e307d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817830408208443ee533b783a809ddaa5", "guid": "bfdfe7dc352907fc980b868725387e98122d6ec48b2f819b06b60f8b43a4534a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c59e8c90703fa38fc677bfaec854e67c", "guid": "bfdfe7dc352907fc980b868725387e986d51af78cc8f2b75ab20eb3ca9148318"}], "guid": "bfdfe7dc352907fc980b868725387e981efbd1be02afee0a19a6d7d960c1cafe", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e98b1ded6a0604812b79ee1e17542f3d36c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982753cfb120d9cba6aed6fd719b664a8c", "guid": "bfdfe7dc352907fc980b868725387e988349b399392476326c23caa2a267fe67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819b8cce5a516b92ba7e2f696a83a0004", "guid": "bfdfe7dc352907fc980b868725387e981051aa1b334aa4be47291490fee35996"}], "guid": "bfdfe7dc352907fc980b868725387e9885e4cb71f225c4f0313e8b90eddc2c81", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98503f62471f2eaa7c79b2323d4ced8847", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98631ea9156265c904a0bfc0755b4ae6ad", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98567cf7b56ed3079de07509f530f89fd7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9847142b39812226e335f622f9350b2988", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988afcedbaa541058708977774674b43d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98aae7a8abb2d77b9823d502814edad182", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988afcedbaa541058708977774674b43d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985de48d4737d06c18f36bc08d5ea74a88", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f57c366acc0ba6d6fecae4e4c49c43c9", "guid": "bfdfe7dc352907fc980b868725387e981e32067ed014dfe48676412e0ef0bbe4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4936c4bd1897d685efad3f69b4fe8c9", "guid": "bfdfe7dc352907fc980b868725387e9876f2daebe05677bdae0faa62f072680f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bedc8402c532f1fa8a055464dcc31fb3", "guid": "bfdfe7dc352907fc980b868725387e98aa6861348e364c28593fa2aaf7a5bfcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863962547085b31cece34de7808084ffe", "guid": "bfdfe7dc352907fc980b868725387e981aad84ebfba9ca0fae3b90ec6f5a137c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c0f9e2fdda0f1b2bf6bfc7d58d820cf", "guid": "bfdfe7dc352907fc980b868725387e9879dd763ad41a9953cc0bdb38209ad6d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f3342f3c1941903b2ca4b3ecc628e4b", "guid": "bfdfe7dc352907fc980b868725387e989a3ccdf1307d550d7a08b199f7fb1ef4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d3930791633f05b3bd00da26ef530aa3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c4286b00ef9037e6fab0babe1a8d661", "guid": "bfdfe7dc352907fc980b868725387e984cbc3b3145e7d449d3ecd77e0e88f041"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b303d2bb2450c5227ea35ae6d03192a", "guid": "bfdfe7dc352907fc980b868725387e98910fe9dc4bff16e1f400e519bca1bede"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c281b1c836a08b989daa57c6b4829a5f", "guid": "bfdfe7dc352907fc980b868725387e988a6e6c9fc6c079812fa543a26021ce45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98818f9fd9b8036066c3c5051e4e9a6746", "guid": "bfdfe7dc352907fc980b868725387e988caf0edea3c688aee0cc7d27ac7ab5bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcb931351599f95128e1592a263ff3d6", "guid": "bfdfe7dc352907fc980b868725387e98b00aa11e3c52dc81b0371660ef7316fd"}], "guid": "bfdfe7dc352907fc980b868725387e98a6359618f1daf086aef51ca4b546ea86", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e989b7af458fa619c4730bcaf204c579958"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982753cfb120d9cba6aed6fd719b664a8c", "guid": "bfdfe7dc352907fc980b868725387e98b1917a9ef3f0ea02be504e8f913b619b"}], "guid": "bfdfe7dc352907fc980b868725387e98416e0c799868630d909563ce04db7e2e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cd976e1e5fd0a315e8fbac057b926c64", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e98ab77242fed58bcb7e7c74f6cdf2510c3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
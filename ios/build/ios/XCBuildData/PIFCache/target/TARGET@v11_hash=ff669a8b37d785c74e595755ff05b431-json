{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cace80b78bc2700b9efb9cd9cf762f92", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d15420b18834da786f148242969989e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982baa20d345364ebab87e95835d382c9b", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98acf95ee3ab2a6ee729287c0d42603188", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982baa20d345364ebab87e95835d382c9b", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b68d75e79f98bc6640329a44a26f353a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988e01eb6aa6e851a3245556781d34ee42", "guid": "bfdfe7dc352907fc980b868725387e984c984196f2cc9020784c9cfb56d062e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c3f1e668ef39e3c947e00c43937458", "guid": "bfdfe7dc352907fc980b868725387e98e951e864a7f1e5abd3629820d367913e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98888ca6da3fa550b33c1ae7b37395721b", "guid": "bfdfe7dc352907fc980b868725387e9852e2960d25e022fc485e09090115b75f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893d6caa806d3127eac1c69987680eaf0", "guid": "bfdfe7dc352907fc980b868725387e9843762a47d87e8160eac2cc9e009fac10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806c5bdf2f05b64b7c24f36ecb83733df", "guid": "bfdfe7dc352907fc980b868725387e989b0a0201da21a3e0e82319a5b407891f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806ee338f293e88d7a58c40b6ac438039", "guid": "bfdfe7dc352907fc980b868725387e98fd2be56d42d7b1b798e0f1a2da6cc686", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98324641eb506d7b41ba252753bb13e3de", "guid": "bfdfe7dc352907fc980b868725387e982c648c2e69a461c10eec0a64e94b72c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a162b3e29d9ab8024a7e6a7e4cfac70", "guid": "bfdfe7dc352907fc980b868725387e9844edc1030ec95248ff0f569750cf3404", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841e2c65ea91d779b9ff1188662acabd0", "guid": "bfdfe7dc352907fc980b868725387e98fb12e3d8946201f9f14a79ab5c528a77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98131ede58b2f3648d7e535022d363bc3a", "guid": "bfdfe7dc352907fc980b868725387e98214209258f4b930844bbf972dd5319ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98338a6c4f9706c6d3cf73caa09465abc5", "guid": "bfdfe7dc352907fc980b868725387e981cca1efb4b3ff0c9b7d81406180f5e13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e11b68703b2dad991417fa7729c68eca", "guid": "bfdfe7dc352907fc980b868725387e98a8c4e7eca10803469839bd0877496d63", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6311f411f73d7799b2d6c3cfbfd4405", "guid": "bfdfe7dc352907fc980b868725387e989e97ad7faf44c970ff1e6892a1c6ff4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989936ca7d7cb1c031853716c1f88ce8b3", "guid": "bfdfe7dc352907fc980b868725387e98a442ec951f2a4c4ef29a81ed90aeba40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980da65c40b84ac31cc9cd51660fffeeb0", "guid": "bfdfe7dc352907fc980b868725387e98258cd2d39e2a31a3c437230930ed3c6e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988554d497c826316015ec286d14e2b00f", "guid": "bfdfe7dc352907fc980b868725387e9883028933237defb52eaa5866e0219856", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833d79a3cd6a8a4f8870e2796ffe5b555", "guid": "bfdfe7dc352907fc980b868725387e9805d26b73777986106510372b943d540b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861895d05b1a2e3a72a7957c452dc7b5b", "guid": "bfdfe7dc352907fc980b868725387e984d4955f004887037276ee22e6770771d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a30140c0982d74a232acc9fd9107ec35", "guid": "bfdfe7dc352907fc980b868725387e98dc4de85425b77462dbd647112c83664f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844bf215fc7294c772d2415243b3bab2d", "guid": "bfdfe7dc352907fc980b868725387e983c5ebb4745fc0b6c0e476af83bbc83c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984135def0b60cbc4b25d8707b909d763b", "guid": "bfdfe7dc352907fc980b868725387e98772139e58ed3f82dd6f2546542447787", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98176872882883813b1dedf49bcd091e4e", "guid": "bfdfe7dc352907fc980b868725387e98ee03cb27e22d2feb026b95a37d9771f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad2b8c704ea538b875dea1148473f0a1", "guid": "bfdfe7dc352907fc980b868725387e98e7a8dbf06a9d8b3a3f9cf056537e39bb", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d5c8c539c8b21555bcb4eb3367cc806c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a02bb1d51fd333aed377214c17422278", "guid": "bfdfe7dc352907fc980b868725387e98386ab3f74305c262076447151855c212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804d1ec00a4bd860b90552c83dfdbfa45", "guid": "bfdfe7dc352907fc980b868725387e98bf6bad7fbf7941c09e64d11a6e2d9060"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0eb3721bbfe74ecd6926d232438d82f", "guid": "bfdfe7dc352907fc980b868725387e98baa2bf22e222568ad045b7c639795d06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb51ebb16d436c07029a31b5c1018910", "guid": "bfdfe7dc352907fc980b868725387e98c9bbe5fdd39935f086e8dbcb9b4d4aa7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984af3870ea57b814a8e1b6b2ab00bbbdb", "guid": "bfdfe7dc352907fc980b868725387e98a38b3c0606ac91d8df938de918b45d8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b383ead128794fc3509e53e7023923cc", "guid": "bfdfe7dc352907fc980b868725387e98d9460cb511b72b356f035c4767d454db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b9be3e250c5121c3b6abd24bfc22750", "guid": "bfdfe7dc352907fc980b868725387e9844c23b8db892fdb00e206b37b094bdc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98671bc258c348aa12ef06fc0f26865484", "guid": "bfdfe7dc352907fc980b868725387e98e9a83ea2e96c82876b9cb162bde076dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f60c0cad0df82952ca1bb758e9ac505d", "guid": "bfdfe7dc352907fc980b868725387e98b4013c802ebc6d3b2b0a58c5e8a2dce4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803086f19fa3b8ee72317d78d60ae210c", "guid": "bfdfe7dc352907fc980b868725387e980977748ed1acb0699b993ef43e3348d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a44ca34c94b769c6e21015f23e0284bc", "guid": "bfdfe7dc352907fc980b868725387e98af37e7b89b554c9fa5fbde6411cc2b44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984392ea511898d8b59ae0fb6e2552cd64", "guid": "bfdfe7dc352907fc980b868725387e98fd9856aafc6f5659daad046131498d69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984efc836400df4b44346b0cbd18bbd094", "guid": "bfdfe7dc352907fc980b868725387e98c0229d70a6a984dd4b305a2b34d61e96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d681a4c85c91484734ebe9ff66a7846", "guid": "bfdfe7dc352907fc980b868725387e98cd57eaac59f1f3f6b0531e340b379640"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c95bb9b5e2be50401eef30500eae53d8", "guid": "bfdfe7dc352907fc980b868725387e98044fe391f6c569e92af059bb1a40d118"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d7fb84cbc610c8b8e5e02a6a751989f", "guid": "bfdfe7dc352907fc980b868725387e98c001e6de2ac0f0a8ad3aaa8e4289df80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98085c6b86d158e2aa175a95d4a8ef7b57", "guid": "bfdfe7dc352907fc980b868725387e98d79ff51595330a2d9c6da9089259c22d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875532e8e4bb73353511932e0275a093c", "guid": "bfdfe7dc352907fc980b868725387e9894696d55c55a639185fa5fe118e3eb85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98858988997dc082a55d77ce40db0910b4", "guid": "bfdfe7dc352907fc980b868725387e9850cbbb8dc099a59120ad33b450ad2c1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ff08dabf9f937b0c97394e2219bed0c", "guid": "bfdfe7dc352907fc980b868725387e98913ef6d794f55baa213e6a149e4eeac3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a411a4a9e53959a9a6fbcad199de4e9c", "guid": "bfdfe7dc352907fc980b868725387e9826040e8bdb719df6bff9cdd328237d18"}], "guid": "bfdfe7dc352907fc980b868725387e98057eb75c841554d25f0f5035104e0263", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa402e174de7b27da247b6083c425633", "guid": "bfdfe7dc352907fc980b868725387e980017a4fef110a270efddb901b92f2e25"}], "guid": "bfdfe7dc352907fc980b868725387e9810423e99f15760aeac349670a9357a85", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9862935e1388b76505ac12d06927e9652c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a4c09ca7184501688f4b05eb3557448", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CLANG_ENABLE_OBJC_WEAK": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "ONLY_ACTIVE_ARCH": "NO", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98ef9f782cb9969d62b692e399316b1ab3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982f686ee269dde1f76fd3ec2914cf261d", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CLANG_ENABLE_OBJC_WEAK": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e988c95250a48d08ead873f6147987d1c7c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982f686ee269dde1f76fd3ec2914cf261d", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CLANG_ENABLE_OBJC_WEAK": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98b80902772b4cd659f48dd1f8c687efde", "name": "Release"}], "buildPhases": [], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ce8e18cb7dd8a33e75f807c37bedf494", "name": "MLKitBarcodeScanning"}, {"guid": "bfdfe7dc352907fc980b868725387e986aa58d33b8894c02f49ea32c3da727fe", "name": "MLKitCommon"}], "guid": "bfdfe7dc352907fc980b868725387e9832e568d1d64358cf46b257009674a4a8", "name": "GoogleMLKit", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 0}], "type": "aggregate"}